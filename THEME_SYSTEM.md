# Theme System Documentation

## Overview

The Technoloway website now supports both dark and light themes with a seamless switching mechanism. The theme system is built using React Context and CSS custom properties (variables) to ensure smooth transitions and maintainable code.

## Features

- **Dark Theme**: The original dark theme with dark backgrounds and light text
- **Light Theme**: A new light theme with light backgrounds and dark text
- **Theme Toggle**: A button in the navbar to switch between themes
- **Persistent Storage**: Theme preference is saved in localStorage
- **Smooth Transitions**: All theme changes include smooth CSS transitions
- **Responsive Design**: Works on both desktop and mobile devices

## How It Works

### 1. Theme Context (`src/contexts/ThemeContext.tsx`)
- Manages the current theme state
- Provides `theme`, `toggleTheme`, and `setTheme` functions
- Automatically applies theme changes to CSS variables
- Saves theme preference to localStorage

### 2. Theme Configuration (`src/lib/theme.ts`)
- Defines color schemes for both themes
- Uses TypeScript interfaces for type safety
- Centralized color management

### 3. CSS Variables
The theme system uses CSS custom properties defined in `:root`:

```css
:root {
  --primary-color: #FFFFFF;
  --secondary-color: #FFFFFF0A;
  --text-color: #D1D1D1;
  --bg-color: #1C1B2B;
  --accent-color: #4185DD;
  --accent-secondary-color: #B42FDA;
  --divider-color: #FFFFFF0A;
  --error-color: rgb(230, 87, 87);
  --shadow-color: rgba(255, 255, 255, 0.1);
  --border-color: #FFFFFF0A;
  --card-bg: #2A2938;
  --hover-bg: #FFFFFF0A;
}
```

### 4. Theme-Specific Styles (`public/css/theme.css`)
- Overrides for light theme components
- Additional styling for theme toggle button
- Image filter adjustments for proper contrast

## Usage

### For Users
1. Click the theme toggle button (sun/moon icon) in the navbar
2. The theme will switch immediately with smooth transitions
3. Your preference is automatically saved

### For Developers

#### Using the Theme Context
```tsx
import { useTheme } from '@/contexts/ThemeContext';

function MyComponent() {
  const { theme, toggleTheme, setTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={toggleTheme}>Toggle Theme</button>
      <button onClick={() => setTheme('light')}>Set Light</button>
      <button onClick={() => setTheme('dark')}>Set Dark</button>
    </div>
  );
}
```

#### Adding Theme-Specific Styles
```css
/* Light theme specific styles */
.theme-light .my-component {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

/* Dark theme specific styles */
.theme-dark .my-component {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}
```

## Color Palette

### Dark Theme
- Primary: #FFFFFF (White text)
- Secondary: #FFFFFF0A (Semi-transparent white)
- Text: #D1D1D1 (Light gray text)
- Background: #1C1B2B (Dark background)
- Accent: #4185DD (Blue)
- Accent Secondary: #B42FDA (Purple)

### Light Theme
- Primary: #1C1B2B (Dark text)
- Secondary: #F8F9FA (Light gray)
- Text: #333333 (Dark gray text)
- Background: #FFFFFF (White background)
- Accent: #4185DD (Blue - same as dark)
- Accent Secondary: #B42FDA (Purple - same as dark)

## Components

### ThemeToggle (`src/components/ui/ThemeToggle.tsx`)
- Renders a sun icon for dark theme (click to switch to light)
- Renders a moon icon for light theme (click to switch to dark)
- Includes proper accessibility attributes
- Smooth hover animations

### ThemeProvider (`src/contexts/ThemeContext.tsx`)
- Wraps the entire application
- Manages theme state and localStorage
- Applies CSS variables to document root

## File Structure

```
src/
├── contexts/
│   └── ThemeContext.tsx          # Theme context provider
├── lib/
│   └── theme.ts                  # Theme configuration
├── components/
│   └── ui/
│       └── ThemeToggle.tsx       # Theme toggle button
└── app/
    └── layout.tsx                # Includes ThemeProvider

public/
└── css/
    ├── custom.css                # Original styles with CSS variables
    └── theme.css                 # Theme-specific overrides
```

## Browser Support

The theme system uses modern CSS features:
- CSS Custom Properties (CSS Variables)
- CSS Grid and Flexbox
- CSS Transitions

Supported in all modern browsers (Chrome, Firefox, Safari, Edge).

## Performance

- Theme switching is instant with smooth transitions
- No page reload required
- Minimal JavaScript overhead
- CSS variables provide efficient styling updates

## Accessibility

- Theme toggle includes proper ARIA labels
- High contrast ratios maintained in both themes
- Keyboard navigation support
- Screen reader friendly

## Future Enhancements

Potential improvements for the theme system:
1. System theme detection (prefers-color-scheme media query)
2. Additional theme options (high contrast, sepia, etc.)
3. Custom color picker for users
4. Theme-specific animations
5. Export/import theme preferences
