{"name": "technoloway", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.14.0", "@tiptap/extension-color": "^3.3.0", "@tiptap/extension-font-family": "^3.3.0", "@tiptap/extension-highlight": "^3.3.0", "@tiptap/extension-text-align": "^3.3.0", "@tiptap/extension-text-style": "^3.3.0", "@tiptap/extension-underline": "^3.3.0", "@tiptap/react": "^3.3.0", "@tiptap/starter-kit": "^3.3.0", "@types/bcryptjs": "^2.4.6", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "framer-motion": "^12.23.12", "next": "15.4.6", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "prisma": "^6.14.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.62.0", "zod": "^4.1.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.4.6", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}