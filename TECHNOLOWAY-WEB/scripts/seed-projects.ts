import { prisma } from '../src/config/prisma'

export async function seedProjects() {
  try {
    // First, let's check if we have any orders to link projects to
    const orders = await prisma.orders.findMany({
      take: 5,
      include: {
        clients: true,
      },
    })

    if (orders.length === 0) {
      console.log('No orders found. Please create some orders first.')
      return
    }

    // Sample project data
    const sampleProjects = [
      {
        name: 'EcoCommerce Platform',
        description: 'A comprehensive e-commerce platform for sustainable products with advanced filtering, payment integration, and inventory management. Built with modern technologies to provide a seamless shopping experience.',
        projgoals: 'Sustainable e-commerce platform with advanced features',
        orderid: orders[0].id,
        clientid: orders[0].clientid,
        status: 'COMPLETED',
        projmanager: null, // Will be set to a team member ID if available
        estimatecost: 45000,
        estimatetime: '6 months',
        estimateeffort: '1200 hours',
        projstartdate: new Date('2023-07-01'),
        projcompletiondate: new Date('2024-01-15'),
        imageurl: 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=800&h=600&fit=crop',
        projecturl: 'https://ecocommerce-demo.vercel.app',
        githuburl: 'https://github.com/technoloway/ecocommerce-platform',
        tags: 'e-commerce, sustainability, web, react, nextjs',
        isfeatured: true,
        ispublic: true,
        displayorder: 1,
      },
      {
        name: 'HealthTracker Mobile App',
        description: 'A comprehensive health tracking mobile application with real-time monitoring, doctor consultations, and personalized health insights. Connects patients with healthcare providers seamlessly.',
        projgoals: 'Health monitoring mobile app with real-time features',
        orderid: orders[1]?.id || orders[0].id,
        clientid: orders[1]?.clientid || orders[0].clientid,
        status: 'COMPLETED',
        projmanager: null,
        estimatecost: 65000,
        estimatetime: '8 months',
        estimateeffort: '1600 hours',
        projstartdate: new Date('2023-06-01'),
        projcompletiondate: new Date('2024-02-20'),
        imageurl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
        projecturl: 'https://healthtracker-app.com',
        githuburl: 'https://github.com/technoloway/healthtracker-app',
        tags: 'healthcare, mobile, react-native, telemedicine',
        isfeatured: true,
        ispublic: true,
        displayorder: 2,
      },
      {
        name: 'Financial Analytics Dashboard',
        description: 'A sophisticated financial analytics platform providing real-time insights, risk assessment, and portfolio management for investment firms. Features advanced data visualization and AI-powered recommendations.',
        projgoals: 'Advanced financial analytics with AI insights',
        orderid: orders[2]?.id || orders[0].id,
        clientid: orders[2]?.clientid || orders[0].clientid,
        status: 'COMPLETED',
        projmanager: null,
        estimatecost: 85000,
        estimatetime: '10 months',
        estimateeffort: '2000 hours',
        projstartdate: new Date('2023-03-01'),
        projcompletiondate: new Date('2023-12-10'),
        imageurl: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',
        projecturl: 'https://finanalytics-dashboard.com',
        githuburl: 'https://github.com/technoloway/financial-dashboard',
        tags: 'fintech, analytics, dashboard, ai, data-visualization',
        isfeatured: true,
        ispublic: true,
        displayorder: 3,
      },
      {
        name: 'Smart Logistics Management',
        description: 'An intelligent logistics management system with route optimization, real-time tracking, and automated scheduling for delivery companies. Reduces costs and improves efficiency.',
        projgoals: 'AI-powered logistics optimization system',
        orderid: orders[3]?.id || orders[0].id,
        clientid: orders[3]?.clientid || orders[0].clientid,
        status: 'IN_PROGRESS',
        projmanager: null,
        estimatecost: 70000,
        estimatetime: '9 months',
        estimateeffort: '1800 hours',
        projstartdate: new Date('2024-01-01'),
        projcompletiondate: null,
        imageurl: 'https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?w=800&h=600&fit=crop',
        projecturl: null,
        githuburl: 'https://github.com/technoloway/logistics-system',
        tags: 'logistics, optimization, tracking, automation',
        isfeatured: false,
        ispublic: true,
        displayorder: 4,
      },
      {
        name: 'EduLearn Online Platform',
        description: 'An interactive online learning platform with video courses, live sessions, progress tracking, and certification management. Revolutionizing digital education.',
        projgoals: 'Interactive online learning with certifications',
        orderid: orders[4]?.id || orders[0].id,
        clientid: orders[4]?.clientid || orders[0].clientid,
        status: 'PLANNING',
        projmanager: null,
        estimatecost: 55000,
        estimatetime: '7 months',
        estimateeffort: '1400 hours',
        projstartdate: new Date('2024-03-01'),
        projcompletiondate: null,
        imageurl: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=600&fit=crop',
        projecturl: null,
        githuburl: null,
        tags: 'education, e-learning, video, certification',
        isfeatured: false,
        ispublic: false,
        displayorder: 5,
      },
    ]

    // Create projects
    for (const projectData of sampleProjects) {
      const existingProject = await prisma.projects.findFirst({
        where: { name: projectData.name },
      })

      if (!existingProject) {
        await prisma.projects.create({
          data: projectData,
        })
        console.log(`Created project: ${projectData.name}`)
      } else {
        console.log(`Project already exists: ${projectData.name}`)
      }
    }

    console.log('Project seeding completed!')
  } catch (error) {
    console.error('Error seeding projects:', error)
  }
}

// Run this function to seed projects
if (require.main === module) {
  seedProjects()
    .then(() => {
      console.log('Seeding finished')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}
