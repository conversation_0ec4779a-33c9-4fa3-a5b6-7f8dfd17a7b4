import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedContent() {
  try {
    console.log('Seeding content...')

    // Clear existing content
    await prisma.staticcontent.deleteMany({})

    // Seed home page content
    const homeContent = [
      {
        page: 'home',
        section: 'hero',
        contentkey: 'title',
        content: 'Transform Your Business with Technology',
        contenttype: 'text',
        displayorder: 1,
        isactive: true
      },
      {
        page: 'home',
        section: 'hero',
        contentkey: 'subtitle',
        content: 'We help businesses leverage cutting-edge technology to achieve their goals',
        contenttype: 'text',
        displayorder: 2,
        isactive: true
      },
      {
        page: 'home',
        section: 'hero',
        contentkey: 'description',
        content: 'From web development to AI solutions, we provide comprehensive technology services',
        contenttype: 'text',
        displayorder: 3,
        isactive: true
      },
      {
        page: 'home',
        section: 'services',
        contentkey: 'title',
        content: 'Our Services',
        contenttype: 'text',
        displayorder: 4,
        isactive: true
      },
      {
        page: 'home',
        section: 'services',
        contentkey: 'subtitle',
        content: 'Comprehensive technology solutions for your business',
        contenttype: 'text',
        displayorder: 5,
        isactive: true
      }
    ]

    // Seed services page content
    const servicesContent = [
      {
        page: 'services',
        section: 'hero',
        contentkey: 'title',
        content: 'Our Services',
        contenttype: 'text',
        displayorder: 1,
        isactive: true
      },
      {
        page: 'services',
        section: 'hero',
        contentkey: 'subtitle',
        content: 'Comprehensive technology solutions for your business',
        contenttype: 'text',
        displayorder: 2,
        isactive: true
      },
      {
        page: 'services',
        section: 'hero',
        contentkey: 'description',
        content: 'We offer a wide range of technology services to help your business grow and succeed',
        contenttype: 'text',
        displayorder: 3,
        isactive: true
      }
    ]

    // Seed about page content
    const aboutContent = [
      {
        page: 'about',
        section: 'hero',
        contentkey: 'title',
        content: 'About Us',
        contenttype: 'text',
        displayorder: 1,
        isactive: true
      },
      {
        page: 'about',
        section: 'hero',
        contentkey: 'subtitle',
        content: 'We are a team of passionate technologists',
        contenttype: 'text',
        displayorder: 2,
        isactive: true
      },
      {
        page: 'about',
        section: 'hero',
        contentkey: 'description',
        content: 'We are a team of passionate technologists dedicated to helping businesses succeed',
        contenttype: 'text',
        displayorder: 3,
        isactive: true
      }
    ]

    // Insert all content
    await prisma.staticcontent.createMany({
      data: [...homeContent, ...servicesContent, ...aboutContent]
    })

    console.log('Content seeded successfully!')
  } catch (error) {
    console.error('Error seeding content:', error)
  } finally {
    await prisma.$disconnect()
  }
}

seedContent() 