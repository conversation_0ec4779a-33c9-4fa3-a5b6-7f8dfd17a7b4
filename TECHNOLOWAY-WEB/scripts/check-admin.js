const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkAdmin() {
  try {
    console.log('🔍 Checking admin user...')
    
    const admin = await prisma.users.findUnique({
      where: { email: '<EMAIL>' }
    })
    
    if (!admin) {
      console.log('❌ Admin user not found!')
      return
    }
    
    console.log('✅ Admin user found:')
    console.log(`   Email: ${admin.email}`)
    console.log(`   Role: ${admin.role}`)
    console.log(`   Active: ${admin.isactive}`)
    
    // Test password
    const testPassword = 'password123'
    const isValidPassword = await bcrypt.compare(testPassword, admin.password)
    
    console.log(`\n🔐 Password test for "${testPassword}": ${isValidPassword ? '✅ Valid' : '❌ Invalid'}`)
    
    if (!isValidPassword) {
      console.log('\n🔧 Creating new password hash for "admin123"...')
      const newPasswordHash = await bcrypt.hash('admin123', 12)
      
      await prisma.users.update({
        where: { email: '<EMAIL>' },
        data: { password: newPasswordHash }
      })
      
      console.log('✅ Password updated to "admin123"')
    }
    
    console.log('\n📋 Login Credentials:')
    console.log('   Email: <EMAIL>')
    console.log('   Password: admin123')
    
  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAdmin()
