#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Technoloway Database...\n');

// Check if .env.local exists
const envPath = path.join(process.cwd(), '.env.local');
if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found!');
  console.log('Please copy .env.example to .env.local and configure your database URL.');
  process.exit(1);
}

// Read environment variables
require('dotenv').config({ path: envPath });

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL not found in .env.local!');
  console.log('Please set your PostgreSQL connection string in .env.local');
  console.log('Example: DATABASE_URL="postgresql://username:password@localhost:5432/technoloway_db"');
  process.exit(1);
}

console.log('📋 Database URL configured');
console.log('🔧 Generating Prisma client...');

try {
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log('✅ Prisma client generated successfully');
} catch (error) {
  console.error('❌ Failed to generate Prisma client:', error.message);
  process.exit(1);
}

console.log('🗄️  Pushing database schema...');

try {
  execSync('npx prisma db push', { stdio: 'inherit' });
  console.log('✅ Database schema pushed successfully');
} catch (error) {
  console.error('❌ Failed to push database schema:', error.message);
  console.log('\n💡 Database setup failed. This is likely because:');
  console.log('  1. PostgreSQL server is not running');
  console.log('  2. Database does not exist');
  console.log('  3. Connection credentials are incorrect');
  console.log('\n🔧 To fix this:');
  console.log('  1. Start PostgreSQL server');
  console.log('  2. Create database: psql -U postgres -c "CREATE DATABASE technoloway_db;"');
  console.log('  3. Update DATABASE_URL in .env.local');
  console.log('  4. Run: npm run db:setup');
  console.log('\n📖 See SETUP-GUIDE.md for detailed instructions');
  process.exit(1);
}

console.log('🌱 Seeding database with sample data...');

try {
  execSync('npx tsx prisma/seed.ts', { stdio: 'inherit' });
  console.log('✅ Database seeded successfully');
} catch (error) {
  console.error('❌ Failed to seed database:', error.message);
  process.exit(1);
}

console.log('\n🎉 Database setup completed successfully!');
console.log('\n📊 You can now:');
console.log('  • Start the development server: npm run dev');
console.log('  • Open Prisma Studio: npm run db:studio');
console.log('  • View the admin dashboard: http://localhost:3000/admin');
console.log('  • Test the API endpoints: http://localhost:3000/api/services');
