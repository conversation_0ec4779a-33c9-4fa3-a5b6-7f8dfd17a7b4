#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update import statements after project structure reorganization
 * Usage: node scripts/update-imports.js
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Import path mappings
const importMappings = {
  // Services
  '@/lib/api-utils': '@/services/api/api-utils',
  '@/lib/auth-config': '@/services/auth/auth-config',
  '@/lib/payment-utils': '@/services/payment/payment-utils',
  '@/lib/payment-schema': '@/services/payment/payment-schema',
  '@/lib/stripe': '@/services/payment/stripe',
  '@/lib/email': '@/services/email',
  '@/lib/file-upload': '@/services/file-upload/file-upload',
  '@/lib/chat-access-control': '@/services/chat/chat-access-control',
  '@/lib/security': '@/services/auth/security',
  '@/lib/csrf': '@/services/auth/csrf',
  '@/lib/rate-limit': '@/services/api/rate-limit',
  
  // Configuration
  '@/lib/prisma': '@/config/prisma',
  '@/lib/analytics': '@/config/analytics',
  '@/lib/monitoring': '@/config/monitoring',
  '@/lib/audit-log': '@/config/audit-log',
  '@/lib/branding': '@/config/branding',
  
  // Utilities
  '@/lib/date-utils': '@/lib/utils/date-utils',
  '@/lib/url-utils': '@/lib/utils/url-utils',
  '@/lib/validations': '@/lib/utils/validations',
  '@/lib/data-transform': '@/lib/utils/data-transform',
  '@/lib/tsx-text-locator': '@/lib/utils/tsx-text-locator',
  '@/lib/source-locator': '@/lib/utils/source-locator',
  '@/lib/page-detector': '@/lib/utils/page-detector',
  
  // Types
  '@/types/technology': '@/types/shared/technology',
  '@/types/next-auth': '@/types/auth/next-auth',
};

// Function name mappings
const functionMappings = {
  'validateUploadFileType': 'validateUploadFileType',
  'validateUploadFileSize': 'validateUploadFileSize',
};

function updateImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;
    
    // Update import paths
    for (const [oldPath, newPath] of Object.entries(importMappings)) {
      const regex = new RegExp(`from\\s+['"]${oldPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"]`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, `from '${newPath}'`);
        updated = true;
        console.log(`Updated import in ${filePath}: ${oldPath} → ${newPath}`);
      }
    }
    
    // Update function names
    for (const [oldName, newName] of Object.entries(functionMappings)) {
      const regex = new RegExp(`\\b${oldName}\\b`, 'g');
      if (regex.test(content)) {
        content = content.replace(regex, newName);
        updated = true;
        console.log(`Updated function name in ${filePath}: ${oldName} → ${newName}`);
      }
    }
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔄 Starting import statement updates...\n');
  
  // Find all TypeScript and JavaScript files
  const patterns = [
    'src/**/*.{ts,tsx,js,jsx}',
    'app/**/*.{ts,tsx,js,jsx}',
    'scripts/**/*.{ts,js}',
  ];
  
  let totalFiles = 0;
  let updatedFiles = 0;
  
  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: ['node_modules/**', '.next/**'] });
    
    files.forEach(file => {
      totalFiles++;
      if (updateImportsInFile(file)) {
        updatedFiles++;
      }
    });
  });
  
  console.log(`\n✅ Import update complete!`);
  console.log(`📊 Files processed: ${totalFiles}`);
  console.log(`📝 Files updated: ${updatedFiles}`);
  
  if (updatedFiles > 0) {
    console.log(`\n🔍 Next steps:`);
    console.log(`1. Run 'npm run type-check' to verify TypeScript`);
    console.log(`2. Run 'npm run lint' to check for linting issues`);
    console.log(`3. Run 'npm run test' to ensure tests pass`);
    console.log(`4. Start dev server with 'npm run dev' to test`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { updateImportsInFile, importMappings, functionMappings }; 