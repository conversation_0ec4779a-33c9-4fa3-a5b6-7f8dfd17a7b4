#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to verify the new project structure
 * Usage: node scripts/verify-structure.js
 */

const fs = require('fs');
const path = require('path');

const expectedStructure = {
  'src/services': {
    'api': ['api-utils.ts', 'rate-limit.ts', 'client.ts', 'project.ts', 'contract.ts', 'invoice.ts', 'payment.ts'],
    'auth': ['auth-config.ts', 'security.ts', 'csrf.ts'],
    'payment': ['payment-utils.ts', 'payment-schema.ts', 'stripe.ts'],
    'email': ['email.ts'],
    'file-upload': ['file-upload.ts'],
    'content': ['content-json-generator.ts'],
    'chat': ['chat-access-control.ts']
  },
  'src/config': ['prisma.ts', 'analytics.ts', 'monitoring.ts', 'audit-log.ts', 'branding.ts'],
  'src/lib/utils': ['date-utils.ts', 'url-utils.ts', 'validations.ts', 'data-transform.ts', 'tsx-text-locator.ts', 'source-locator.ts', 'page-detector.ts'],
  'src/types': {
    'auth': ['next-auth.d.ts'],
    'shared': ['technology.ts']
  }
};

const indexFiles = [
  'src/services/index.ts',
  'src/services/api/index.ts',
  'src/services/auth/index.ts',
  'src/services/payment/index.ts',
  'src/services/email/index.ts',
  'src/services/file-upload/index.ts',
  'src/services/content/index.ts',
  'src/services/chat/index.ts',
  'src/types/index.ts',
  'src/types/auth/index.ts',
  'src/types/shared/index.ts',
  'src/lib/utils/index.ts',
  'src/config/index.ts'
];

function checkDirectory(dirPath, expectedFiles) {
  if (!fs.existsSync(dirPath)) {
    console.log(`❌ Directory missing: ${dirPath}`);
    return false;
  }

  const files = fs.readdirSync(dirPath);
  let allGood = true;

  if (Array.isArray(expectedFiles)) {
    // Simple file list
    expectedFiles.forEach(file => {
      if (!files.includes(file)) {
        console.log(`❌ File missing: ${dirPath}/${file}`);
        allGood = false;
      }
    });
  } else {
    // Nested structure
    Object.entries(expectedFiles).forEach(([subDir, subFiles]) => {
      const fullPath = path.join(dirPath, subDir);
      if (!checkDirectory(fullPath, subFiles)) {
        allGood = false;
      }
    });
  }

  return allGood;
}

function checkIndexFiles() {
  let allGood = true;
  indexFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      console.log(`❌ Index file missing: ${file}`);
      allGood = false;
    }
  });
  return allGood;
}

function main() {
  console.log('🔍 Verifying project structure...\n');
  
  let structureGood = true;
  
  // Check main structure
  Object.entries(expectedStructure).forEach(([dir, expected]) => {
    if (!checkDirectory(dir, expected)) {
      structureGood = false;
    }
  });
  
  // Check index files
  if (!checkIndexFiles()) {
    structureGood = false;
  }
  
  // Check that old lib directory is cleaned up
  const oldLibFiles = fs.readdirSync('src/lib').filter(file => 
    !['utils', 'hooks', 'middleware'].includes(file) && file !== 'index.ts'
  );
  
  if (oldLibFiles.length > 0) {
    console.log(`⚠️  Old files still in src/lib: ${oldLibFiles.join(', ')}`);
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (structureGood) {
    console.log('✅ Project structure verification PASSED!');
    console.log('🎉 The reorganization was successful!');
  } else {
    console.log('❌ Project structure verification FAILED!');
    console.log('🔧 Please check the missing files/directories above.');
  }
  
  console.log('\n📋 Next steps:');
  console.log('1. Run: node scripts/update-imports.js');
  console.log('2. Run: npm run type-check');
  console.log('3. Run: npm run lint');
  console.log('4. Run: npm run test');
  console.log('5. Run: npm run dev');
}

if (require.main === module) {
  main();
}

module.exports = { checkDirectory, checkIndexFiles }; 