{"sqltools.connections": [{"previewLimit": 50, "server": "localhost", "port": 5432, "driver": "PostgreSQL", "name": "TechWayLocal", "database": "TechWayDB", "username": "postgres", "password": "Techway@22", "connectionTimeout": 30}], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/.next": true, "**/out": true, "**/build": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/coverage": true}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/node_modules": true, "**/.next": true}, "typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "prettier.requireConfig": true, "editor.rulers": [100], "editor.wordWrap": "bounded", "editor.wordWrapColumn": 100, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true}