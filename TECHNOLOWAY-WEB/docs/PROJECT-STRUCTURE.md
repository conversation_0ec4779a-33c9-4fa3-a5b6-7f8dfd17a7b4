# Project Structure Documentation

## Overview

This document outlines the organized structure of the TECHNOLOWAY-WEB project, designed for scalability, readability, and developer collaboration.

## Directory Structure

```
TECHNOLOWAY-WEB/
├── 📁 app/                          # Next.js App Router (pages & routes)
│   ├── 📁 (admin)/                  # Admin route group
│   ├── 📁 (client)/                 # Client route group  
│   ├── 📁 api/                      # Next.js API routes
│   ├── 📁 auth/                     # Authentication pages
│   ├── 📁 client-auth/              # Client authentication
│   ├── 📁 client-dashboard/         # Client dashboard
│   ├── 📁 admin/                    # Admin dashboard
│   ├── 📁 about/                    # About page
│   ├── 📁 contact/                  # Contact page
│   ├── 📁 services/                 # Services pages
│   ├── 📁 projects/                 # Projects pages
│   ├── 📁 technologies/             # Technologies pages
│   ├── 📁 team/                     # Team page
│   ├── 📁 blog/                     # Blog pages
│   ├── 📁 portfolio/                # Portfolio page
│   ├── 📁 clients/                  # Client pages
│   ├── layout.tsx                   # Root layout
│   ├── page.tsx                     # Homepage
│   ├── globals.css                  # Global styles
│   └── not-found.tsx               # 404 page
├── 📁 src/
│   ├── 📁 components/               # Reusable UI components
│   │   ├── 📁 ui/                   # Base UI components
│   │   ├── 📁 admin/                # Admin-specific components
│   │   ├── 📁 client/               # Client-specific components
│   │   ├── 📁 home/                 # Homepage components
│   │   ├── 📁 layout/               # Layout components
│   │   ├── 📁 providers/            # Context providers
│   │   ├── header.tsx              # Site header
│   │   ├── footer.tsx              # Site footer
│   │   └── error-boundary.tsx      # Error boundary
│   ├── 📁 lib/                      # Utilities & helpers
│   │   ├── 📁 utils/                # General utilities
│   │   ├── 📁 hooks/                # Custom hooks
│   │   └── 📁 middleware/           # Custom middleware
│   ├── 📁 services/                 # Business logic & external services
│   │   ├── 📁 api/                  # API service functions
│   │   ├── 📁 auth/                 # Authentication services
│   │   ├── 📁 payment/              # Payment services
│   │   ├── 📁 email/                # Email services
│   │   ├── 📁 file-upload/          # File upload services
│   │   ├── 📁 content/              # Content management services
│   │   └── 📁 chat/                 # Chat services
│   ├── 📁 types/                    # TypeScript type definitions
│   │   ├── 📁 api/                  # API types
│   │   ├── 📁 auth/                 # Auth types
│   │   ├── 📁 components/           # Component types
│   │   ├── 📁 database/             # Database types
│   │   └── 📁 shared/               # Shared types
│   ├── 📁 hooks/                    # Custom React hooks
│   ├── 📁 data/                     # Static data & constants
│   └── 📁 config/                   # Configuration files
├── 📁 prisma/                       # Database schema & migrations
├── 📁 scripts/                      # Build & utility scripts
├── 📁 public/                       # Static assets
├── 📁 tests/                        # Test files
├── 📁 docs/                         # Documentation
├── 📁 .github/                      # GitHub workflows
├── package.json
├── tsconfig.json
├── next.config.ts
├── tailwind.config.js
├── jest.config.js
└── README.md
```

## Key Principles

### 1. Separation of Concerns
- **Frontend**: `app/` directory for Next.js App Router pages and routes
- **Components**: `src/components/` for reusable UI components
- **Services**: `src/services/` for business logic and external integrations
- **Utilities**: `src/lib/` for helper functions and utilities
- **Types**: `src/types/` for TypeScript type definitions
- **Configuration**: `src/config/` for app configuration

### 2. Feature-Based Organization
- Components organized by feature/domain (admin, client, home, etc.)
- Services grouped by functionality (api, auth, payment, etc.)
- Types organized by context (api, auth, components, etc.)

### 3. Scalability
- Clear import paths with index files
- Modular service architecture
- Reusable utility functions
- Centralized configuration

## Import Patterns

### Services
```typescript
// Import specific services
import { validateFileSize } from '@/services/api/api-utils';
import { sendEmail } from '@/services/email';
import { processPayment } from '@/services/payment';

// Import all services
import * as Services from '@/services';
```

### Components
```typescript
// Import components
import { Header, Footer } from '@/components';
import { AdminDashboard } from '@/components/admin';
import { ClientForm } from '@/components/client';
```

### Utilities
```typescript
// Import utilities
import { formatDate, validateEmail } from '@/lib/utils';
import { useLocalStorage } from '@/lib/hooks';
```

### Types
```typescript
// Import types
import type { User, Project } from '@/types/shared';
import type { AuthConfig } from '@/types/auth';
```

## Best Practices

### 1. File Naming
- Use kebab-case for file names: `user-profile.tsx`
- Use PascalCase for component names: `UserProfile`
- Use camelCase for function names: `getUserData`

### 2. Import Organization
- Group imports: external libraries, internal modules, types
- Use absolute imports with `@/` prefix
- Import types with `import type` syntax

### 3. Component Structure
- One component per file
- Export components as default exports
- Use index files for clean imports

### 4. Service Architecture
- Keep services focused on single responsibility
- Use dependency injection where appropriate
- Handle errors consistently across services

## Migration Notes

This structure was reorganized from the previous layout to improve:
- **Maintainability**: Clear separation of concerns
- **Scalability**: Modular architecture
- **Developer Experience**: Intuitive file organization
- **Type Safety**: Comprehensive type organization
- **Testing**: Better test organization

## Next Steps

1. Update import statements throughout the codebase
2. Add comprehensive documentation for each service
3. Implement consistent error handling patterns
4. Add unit tests for all services
5. Create development guidelines for new features 