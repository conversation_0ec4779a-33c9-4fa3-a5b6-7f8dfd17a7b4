{"compilerOptions": {"target": "es2017", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "outDir": "prisma/dist", "skipLibCheck": true, "lib": ["es2017"], "resolveJsonModule": true, "isolatedModules": true, "paths": {"@/*": ["./src/*"]}, "allowJs": true, "strict": false, "noEmit": true, "incremental": true, "jsx": "preserve", "plugins": [{"name": "next"}], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}