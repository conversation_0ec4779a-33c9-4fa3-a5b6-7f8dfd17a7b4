import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Comprehensive site settings for a professional website
const SITE_SETTINGS = [
  // GENERAL Category
  {
    key: 'site_name',
    value: 'TechnoloWay',
    category: 'General',
    description: 'Website name displayed in header and title',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'site_tagline',
    value: 'Innovative Technology Solutions',
    category: 'General',
    description: 'Website tagline or slogan',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'site_description',
    value: 'Leading software development company specializing in web applications, mobile apps, and digital transformation solutions.',
    category: 'General',
    description: 'Website meta description for SEO',
    fieldtype: 'textarea',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'site_keywords',
    value: 'software development, web development, mobile apps, digital transformation, technology solutions',
    category: 'General',
    description: 'SEO keywords for the website',
    fieldtype: 'textarea',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'site_logo_url',
    value: '/images/logo.png',
    category: 'General',
    description: 'Main website logo URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'site_favicon_url',
    value: '/favicon.ico',
    category: 'General',
    description: 'Website favicon URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'business_hours',
    value: 'Monday - Friday: 9:00 AM - 6:00 PM PST',
    category: 'General',
    description: 'Business operating hours',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'timezone',
    value: 'America/Los_Angeles',
    category: 'General',
    description: 'Default timezone for the website',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'maintenance_mode',
    value: 'false',
    category: 'General',
    description: 'Enable maintenance mode',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },

  // CONTACT Category
  {
    key: 'contact_email',
    value: '<EMAIL>',
    category: 'Contact',
    description: 'Main contact email address',
    fieldtype: 'email',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'support_email',
    value: '<EMAIL>',
    category: 'Contact',
    description: 'Technical support email address',
    fieldtype: 'email',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'sales_email',
    value: '<EMAIL>',
    category: 'Contact',
    description: 'Sales inquiries email address',
    fieldtype: 'email',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'contact_phone',
    value: '+****************',
    category: 'Contact',
    description: 'Main contact phone number',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'contact_phone_toll_free',
    value: '+****************',
    category: 'Contact',
    description: 'Toll-free contact number',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'company_address',
    value: '123 Innovation Drive, Suite 100',
    category: 'Contact',
    description: 'Company street address',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'company_city',
    value: 'San Francisco',
    category: 'Contact',
    description: 'Company city',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'company_state',
    value: 'California',
    category: 'Contact',
    description: 'Company state/province',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'company_zip',
    value: '94105',
    category: 'Contact',
    description: 'Company ZIP/postal code',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'company_country',
    value: 'United States',
    category: 'Contact',
    description: 'Company country',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },

  // SOCIAL Category
  {
    key: 'social_facebook',
    value: 'https://facebook.com/technoloway',
    category: 'Social',
    description: 'Facebook page URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'social_twitter',
    value: 'https://twitter.com/technoloway',
    category: 'Social',
    description: 'Twitter profile URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'social_linkedin',
    value: 'https://linkedin.com/company/technoloway',
    category: 'Social',
    description: 'LinkedIn company page URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'social_github',
    value: 'https://github.com/technoloway',
    category: 'Social',
    description: 'GitHub organization URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'social_instagram',
    value: 'https://instagram.com/technoloway',
    category: 'Social',
    description: 'Instagram profile URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'social_youtube',
    value: 'https://youtube.com/@technoloway',
    category: 'Social',
    description: 'YouTube channel URL',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },

  // APPEARANCE Category
  {
    key: 'theme_primary_color',
    value: '#3B82F6',
    category: 'Appearance',
    description: 'Primary brand color',
    fieldtype: 'color',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'theme_secondary_color',
    value: '#1E40AF',
    category: 'Appearance',
    description: 'Secondary brand color',
    fieldtype: 'color',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'theme_accent_color',
    value: '#F59E0B',
    category: 'Appearance',
    description: 'Accent color for highlights',
    fieldtype: 'color',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'theme_font_family',
    value: 'Inter, system-ui, sans-serif',
    category: 'Appearance',
    description: 'Primary font family',
    fieldtype: 'text',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'theme_dark_mode',
    value: 'false',
    category: 'Appearance',
    description: 'Enable dark mode by default',
    fieldtype: 'checkbox',
    ispublic: true,
    isactive: true,
  },
  {
    key: 'hero_background_image',
    value: '/images/hero-bg.jpg',
    category: 'Appearance',
    description: 'Hero section background image',
    fieldtype: 'url',
    ispublic: true,
    isactive: true,
  },

  // NOTIFICATIONS Category
  {
    key: 'email_notifications',
    value: 'true',
    category: 'Notifications',
    description: 'Enable email notifications',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'admin_notification_email',
    value: '<EMAIL>',
    category: 'Notifications',
    description: 'Admin notification email address',
    fieldtype: 'email',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'notification_frequency',
    value: 'immediate',
    category: 'Notifications',
    description: 'Notification frequency',
    fieldtype: 'dropdown',
    options: 'immediate,hourly,daily,weekly',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'sms_notifications',
    value: 'false',
    category: 'Notifications',
    description: 'Enable SMS notifications',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },

  // SECURITY Category
  {
    key: 'enable_2fa',
    value: 'true',
    category: 'Security',
    description: 'Enable two-factor authentication',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'session_timeout',
    value: '3600',
    category: 'Security',
    description: 'Session timeout in seconds',
    fieldtype: 'number',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'password_min_length',
    value: '8',
    category: 'Security',
    description: 'Minimum password length',
    fieldtype: 'number',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'login_attempts_limit',
    value: '5',
    category: 'Security',
    description: 'Maximum login attempts before lockout',
    fieldtype: 'number',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'ssl_required',
    value: 'true',
    category: 'Security',
    description: 'Require SSL/HTTPS connections',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },

  // INTEGRATIONS Category
  {
    key: 'google_analytics_id',
    value: 'GA-XXXXXXXXX',
    category: 'Integrations',
    description: 'Google Analytics tracking ID',
    fieldtype: 'text',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'google_maps_api_key',
    value: '',
    category: 'Integrations',
    description: 'Google Maps API key',
    fieldtype: 'password',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'stripe_publishable_key',
    value: 'pk_test_xxxxxxxxx',
    category: 'Integrations',
    description: 'Stripe publishable key',
    fieldtype: 'text',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'mailchimp_api_key',
    value: '',
    category: 'Integrations',
    description: 'Mailchimp API key',
    fieldtype: 'password',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'recaptcha_site_key',
    value: '',
    category: 'Integrations',
    description: 'reCAPTCHA site key',
    fieldtype: 'text',
    ispublic: false,
    isactive: true,
  },

  // SYSTEM Category
  {
    key: 'cache_enabled',
    value: 'true',
    category: 'System',
    description: 'Enable caching system',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'cache_duration',
    value: '3600',
    category: 'System',
    description: 'Cache duration in seconds',
    fieldtype: 'number',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'debug_mode',
    value: 'false',
    category: 'System',
    description: 'Enable debug mode',
    fieldtype: 'checkbox',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'api_rate_limit',
    value: '100',
    category: 'System',
    description: 'API requests per minute limit',
    fieldtype: 'number',
    ispublic: false,
    isactive: true,
  },
  {
    key: 'backup_frequency',
    value: 'daily',
    category: 'System',
    description: 'Database backup frequency',
    fieldtype: 'dropdown',
    options: 'hourly,daily,weekly,monthly',
    ispublic: false,
    isactive: true,
  },
]

async function seedSiteSettings() {
  console.log('🌱 Starting site settings seeding...')

  // Clear existing site settings
  await prisma.sitesettings.deleteMany()
  console.log('🗑️  Cleared existing site settings')

  // Create new site settings
  console.log('⚙️ Creating comprehensive site settings...')
  
  const createdSettings = await Promise.all(
    SITE_SETTINGS.map(setting =>
      prisma.sitesettings.create({
        data: setting,
      })
    )
  )

  console.log(`✅ Created ${createdSettings.length} site settings`)
  console.log('🎉 Site settings seeding completed successfully!')

  // Display summary by category
  const categories = [...new Set(SITE_SETTINGS.map(s => s.category))]
  console.log('\n📊 Settings Summary by Category:')
  categories.forEach(category => {
    const count = SITE_SETTINGS.filter(s => s.category === category).length
    console.log(`   ${category}: ${count} settings`)
  })
}

// Run the seeding function
if (require.main === module) {
  seedSiteSettings()
    .catch((e) => {
      console.error('❌ Error seeding site settings:', e)
      process.exit(1)
    })
    .finally(async () => {
      await prisma.$disconnect()
    })
}

export { seedSiteSettings, SITE_SETTINGS }
