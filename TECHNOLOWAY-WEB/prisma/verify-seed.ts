import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function verifySeedData() {
  console.log('🔍 Verifying seed data...\n')

  try {
    // Check users
    const userCounts = await prisma.users.groupBy({
      by: ['role'],
      _count: { id: true }
    })
    
    console.log('👥 Users by role:')
    userCounts.forEach(({ role, _count }) => {
      console.log(`  ${role}: ${_count.id}`)
    })
    
    // Check total users
    const totalUsers = await prisma.users.count()
    console.log(`  Total: ${totalUsers}\n`)

    // Check chat messages
    const messageCounts = await prisma.contactforms.groupBy({
      by: ['messagetype'],
      _count: { id: true }
    })
    
    console.log('💬 Messages by type:')
    messageCounts.forEach(({ messagetype, _count }) => {
      console.log(`  ${messagetype}: ${_count.id}`)
    })
    
    const totalMessages = await prisma.contactforms.count()
    console.log(`  Total: ${totalMessages}\n`)

    // Check unread messages
    const unreadCount = await prisma.contactforms.count({
      where: { isread: false }
    })
    console.log(`🔔 Unread messages: ${unreadCount}\n`)

    // Check messages with attachments
    const attachmentCount = await prisma.contactforms.count({
      where: { 
        attachments: { not: null }
      }
    })
    console.log(`📎 Messages with attachments: ${attachmentCount}\n`)

    // Check conversation threads
    const threadCount = await prisma.contactforms.count({
      where: { 
        threadid: { not: null }
      }
    })
    console.log(`🧵 Messages in threads: ${threadCount}\n`)

    // Check business entities
    const clients = await prisma.clients.count()
    const projects = await prisma.projects.count()
    const invoices = await prisma.invoices.count()
    const services = await prisma.services.count()
    
    console.log('🏢 Business entities:')
    console.log(`  Clients: ${clients}`)
    console.log(`  Projects: ${projects}`)
    console.log(`  Invoices: ${invoices}`)
    console.log(`  Services: ${services}\n`)

    // Check admin user login info
    const adminUser = await prisma.users.findFirst({
      where: { email: '<EMAIL>' },
      select: { email: true, role: true, firstname: true, lastname: true }
    })
    
    if (adminUser) {
      console.log('🔑 Admin login credentials:')
      console.log(`  URL: /auth/signin`)
      console.log(`  Email: ${adminUser.email}`)
      console.log(`  Password: password123`)
      console.log(`  Role: ${adminUser.role}`)
      console.log(`  Name: ${adminUser.firstname} ${adminUser.lastname}\n`)
    }

    // Sample chat conversations
    const sampleConversations = await prisma.contactforms.findMany({
      where: { 
        messagetype: 'chat',
        parentid: null // Only thread starters
      },
      include: {
        sender: { select: { firstname: true, lastname: true, role: true } },
        receiver: { select: { firstname: true, lastname: true, role: true } },
        replies: { 
          select: { id: true, message: true, createdat: true },
          orderBy: { createdat: 'asc' },
          take: 2
        }
      },
      take: 3,
      orderBy: { createdat: 'desc' }
    })

    console.log('💭 Sample chat conversations:')
    sampleConversations.forEach((conv, index) => {
      console.log(`\n  ${index + 1}. ${conv.subject}`)
      console.log(`     From: ${conv.sender?.firstname} ${conv.sender?.lastname} (${conv.sender?.role})`)
      console.log(`     To: ${conv.receiver?.firstname} ${conv.receiver?.lastname} (${conv.receiver?.role})`)
      console.log(`     Message: "${conv.message.substring(0, 80)}..."`)
      console.log(`     Replies: ${conv.replies.length}`)
    })

    console.log('\n✅ Seed data verification completed successfully!')
    console.log('\n📋 Summary:')
    console.log(`   • ${totalUsers} users created (${userCounts.find(u => u.role === 'ADMIN')?._count.id || 0} admins, ${userCounts.find(u => u.role === 'CLIENT')?._count.id || 0} clients)`)
    console.log(`   • ${totalMessages} total messages (${unreadCount} unread)`)
    console.log(`   • ${threadCount} threaded messages in conversations`)
    console.log(`   • ${attachmentCount} messages with file attachments`)
    console.log(`   • ${clients} clients, ${projects} projects, ${invoices} invoices`)
    console.log('\n🚀 Ready for testing!')

  } catch (error) {
    console.error('❌ Error verifying seed data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifySeedData()
