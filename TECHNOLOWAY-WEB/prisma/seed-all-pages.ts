import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedAllPagesContent() {
  console.log('🌱 Seeding static content for all navbar pages...')

  // Clear existing static content
  await prisma.staticcontent.deleteMany()

  const allPagesContent = [
    // ===== ABOUT PAGE =====
    // About Hero Section
    { page: 'about', section: 'hero', contentkey: 'title', content: 'Building the Future of', contenttype: 'text' },
    { page: 'about', section: 'hero', contentkey: 'title_highlight', content: 'Software', contenttype: 'text' },
    { page: 'about', section: 'hero', contentkey: 'subtitle', content: 'Founded in 2014, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality. We specialize in creating scalable, modern solutions that drive growth and efficiency.', contenttype: 'text' },

    // About Mission Section
    { page: 'about', section: 'mission', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'about', section: 'mission', contentkey: 'title_highlight', content: 'Mission', contenttype: 'text' },
    { page: 'about', section: 'mission', contentkey: 'description_1', content: 'To democratize cutting-edge technology and make it accessible to businesses of all sizes. We believe that every company deserves world-class software solutions that can compete with industry giants.', contenttype: 'text' },
    { page: 'about', section: 'mission', contentkey: 'description_2', content: 'Our commitment extends beyond just writing code. We partner with our clients to understand their unique challenges and craft solutions that not only meet their current needs but also scale with their future growth.', contenttype: 'text' },

    // About Values Section
    { page: 'about', section: 'values', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'about', section: 'values', contentkey: 'title_highlight', content: 'Values', contenttype: 'text' },
    { page: 'about', section: 'values', contentkey: 'subtitle', content: 'The principles that guide everything we do', contenttype: 'text' },

    // ===== SERVICES PAGE =====
    // Services Hero Section
    { page: 'services', section: 'hero', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'services', section: 'hero', contentkey: 'title_highlight', content: 'Services', contenttype: 'text' },
    { page: 'services', section: 'hero', contentkey: 'subtitle', content: 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.', contenttype: 'text' },

    // Services Process Section
    { page: 'services', section: 'process', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'title_highlight', content: 'Process', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'subtitle', content: 'A proven methodology that delivers results', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_1_title', content: 'Discovery', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_1_description', content: 'We understand your business goals, requirements, and technical needs through detailed consultation.', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_2_title', content: 'Planning', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_2_description', content: 'We create a comprehensive project plan with timelines, milestones, and resource allocation.', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_3_title', content: 'Development', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_3_description', content: 'Our expert team builds your solution using agile methodology with regular updates and feedback.', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_4_title', content: 'Delivery', contenttype: 'text' },
    { page: 'services', section: 'process', contentkey: 'step_4_description', content: 'We deploy your solution and provide ongoing support to ensure optimal performance.', contenttype: 'text' },

    // ===== PROJECTS PAGE =====
    // Projects Hero Section
    { page: 'projects', section: 'hero', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'projects', section: 'hero', contentkey: 'title_highlight', content: 'Projects', contenttype: 'text' },
    { page: 'projects', section: 'hero', contentkey: 'subtitle', content: 'Explore our portfolio of successful projects that showcase our expertise in delivering innovative software solutions across various industries.', contenttype: 'text' },

    // Projects Features Section
    { page: 'projects', section: 'features', contentkey: 'title', content: 'Why Choose', contenttype: 'text' },
    { page: 'projects', section: 'features', contentkey: 'title_highlight', content: 'Our Solutions', contenttype: 'text' },
    { page: 'projects', section: 'features', contentkey: 'subtitle', content: 'We deliver exceptional results through proven methodologies', contenttype: 'text' },

    // Projects CTA Section
    { page: 'projects', section: 'cta', contentkey: 'title', content: 'Ready to Start Your Project?', contenttype: 'text' },
    { page: 'projects', section: 'cta', contentkey: 'subtitle', content: 'Let\'s discuss how we can help bring your vision to life with our expertise and proven track record.', contenttype: 'text' },
    { page: 'projects', section: 'cta', contentkey: 'button_text', content: 'Get Started Today', contenttype: 'text' },
    { page: 'projects', section: 'cta', contentkey: 'button_url', content: '/contact', contenttype: 'url' },

    // ===== TECHNOLOGIES PAGE =====
    // Technologies Hero Section
    { page: 'technologies', section: 'hero', contentkey: 'title', content: 'Our', contenttype: 'text' },
    { page: 'technologies', section: 'hero', contentkey: 'title_highlight', content: 'Technologies', contenttype: 'text' },
    { page: 'technologies', section: 'hero', contentkey: 'subtitle', content: 'We leverage cutting-edge technologies and frameworks to build robust, scalable, and future-proof solutions for our clients.', contenttype: 'text' },

    // Technologies Categories Section
    { page: 'technologies', section: 'categories', contentkey: 'title', content: 'Technology', contenttype: 'text' },
    { page: 'technologies', section: 'categories', contentkey: 'title_highlight', content: 'Stack', contenttype: 'text' },
    { page: 'technologies', section: 'categories', contentkey: 'subtitle', content: 'Comprehensive expertise across the full technology spectrum', contenttype: 'text' },

    // ===== CONTACT PAGE =====
    // Contact Hero Section
    { page: 'contact', section: 'hero', contentkey: 'title', content: 'Let\'s Build Something', contenttype: 'text' },
    { page: 'contact', section: 'hero', contentkey: 'title_highlight', content: 'Amazing', contenttype: 'text' },
    { page: 'contact', section: 'hero', contentkey: 'subtitle', content: 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.', contenttype: 'text' },

    // Contact Info Section
    { page: 'contact', section: 'info', contentkey: 'title', content: 'Get in', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'title_highlight', content: 'Touch', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'subtitle', content: 'We\'re here to help you succeed. Reach out to us through any of the following channels.', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'email_label', content: 'Email Ussssss', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'email_value', content: '<EMAIL>', contenttype: 'email' },
    { page: 'contact', section: 'info', contentkey: 'phone_label', content: 'Call Us', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'phone_value', content: '+****************', contenttype: 'phone' },
    { page: 'contact', section: 'info', contentkey: 'address_label', content: 'Visit Us', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'address_value', content: '123 Innovation Drive, Tech City, TC 12345', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'hours_label', content: 'Business Hours', contenttype: 'text' },
    { page: 'contact', section: 'info', contentkey: 'hours_value', content: 'Mon-Fri: 9:00 AM - 6:00 PM', contenttype: 'text' },

    // Contact Form Section
    { page: 'contact', section: 'form', contentkey: 'title', content: 'Start Your', contenttype: 'text' },
    { page: 'contact', section: 'form', contentkey: 'title_highlight', content: 'Project', contenttype: 'text' },
    { page: 'contact', section: 'form', contentkey: 'subtitle', content: 'Tell us about your project and we\'ll get back to you within 24 hours.', contenttype: 'text' },
  ]

  // Insert all content
  for (const content of allPagesContent) {
    await prisma.staticcontent.create({
      data: {
        ...content,
        displayorder: 0,
        isactive: true,
      }
    })
  }

  console.log(`✅ Successfully seeded ${allPagesContent.length} static content entries for all navbar pages`)
}

async function main() {
  try {
    await seedAllPagesContent()
  } catch (error) {
    console.error('❌ Error seeding static content:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
