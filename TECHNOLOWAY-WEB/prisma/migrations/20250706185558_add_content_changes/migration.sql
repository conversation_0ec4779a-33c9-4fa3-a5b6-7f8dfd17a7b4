-- CreateTable
CREATE TABLE "contentchanges" (
    "id" BIGSERIAL NOT NULL,
    "pagepath" TEXT NOT NULL,
    "elementid" TEXT NOT NULL,
    "oldtext" TEXT NOT NULL,
    "newtext" TEXT NOT NULL,
    "filepath" TEXT NOT NULL,
    "line" INTEGER,
    "userid" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "timestamp" TIMESTAMPTZ(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "contentchanges_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ix_contentchanges_pagepath" ON "contentchanges"("pagepath");

-- CreateIndex
CREATE INDEX "ix_contentchanges_elementid" ON "contentchanges"("elementid");

-- CreateIndex
CREATE INDEX "ix_contentchanges_status" ON "contentchanges"("status");

-- CreateIndex
CREATE INDEX "ix_contentchanges_userid" ON "contentchanges"("userid");
