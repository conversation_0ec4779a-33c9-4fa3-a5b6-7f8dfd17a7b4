import { NextRequest } from 'next/server'
import { prisma } from './prisma'

export interface AuditLogEntry {
  userId?: string
  action: string
  resource?: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  success: boolean
  errorMessage?: string
}

export class AuditLogger {
  static async log(entry: AuditLogEntry, request?: NextRequest): Promise<void> {
    try {
      // Extract request information if provided
      let ipAddress = entry.ipAddress
      let userAgent = entry.userAgent

      if (request) {
        const forwarded = request.headers.get('x-forwarded-for')
        const realIp = request.headers.get('x-real-ip')
        ipAddress = forwarded?.split(',')[0] || realIp || 'unknown'
        userAgent = request.headers.get('user-agent') || 'unknown'
      }

      // Create audit log entry in database
      await prisma.auditlogs.create({
        data: {
          userid: entry.userId ? BigInt(entry.userId) : null,
          action: entry.action,
          resource: entry.resource,
          resourceid: entry.resourceId,
          details: entry.details ? JSON.stringify(entry.details) : null,
          ipaddress: ipAddress,
          useragent: userAgent,
          success: entry.success,
          errormessage: entry.errorMessage,
          createdat: new Date()
        }
      })

      // Also log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Audit Log:', {
          action: entry.action,
          resource: entry.resource,
          success: entry.success,
          userId: entry.userId,
          ipAddress,
          timestamp: new Date().toISOString()
        })
      }
    } catch (error) {
      // Don't throw errors from audit logging to avoid breaking the main flow
      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to create audit log:', error)
      }
    }
  }

  // Convenience methods for common actions
  static async logAuth(
    action: 'LOGIN_SUCCESS' | 'LOGIN_FAILED' | 'LOGOUT' | 'PASSWORD_RESET_REQUEST' | 'PASSWORD_RESET_SUCCESS',
    userId?: string,
    details?: Record<string, any>,
    request?: NextRequest,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'auth',
      details,
      success: !action.includes('FAILED'),
      errorMessage
    }, request)
  }

  static async logAdminAction(
    action: string,
    resource: string,
    resourceId?: string,
    userId?: string,
    details?: Record<string, any>,
    request?: NextRequest,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource,
      resourceId,
      details,
      success,
      errorMessage
    }, request)
  }

  static async logDataAccess(
    action: 'READ' | 'CREATE' | 'UPDATE' | 'DELETE',
    resource: string,
    resourceId?: string,
    userId?: string,
    details?: Record<string, any>,
    request?: NextRequest,
    success: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    await this.log({
      userId,
      action: `${resource.toUpperCase()}_${action}`,
      resource,
      resourceId,
      details,
      success,
      errorMessage
    }, request)
  }
}

// Helper function to get user ID from session
export async function getUserIdFromRequest(request: NextRequest): Promise<string | undefined> {
  try {
    // This would typically extract from JWT token or session
    // For now, we'll return undefined and let the calling code provide it
    return undefined
  } catch (error) {
    return undefined
  }
}
