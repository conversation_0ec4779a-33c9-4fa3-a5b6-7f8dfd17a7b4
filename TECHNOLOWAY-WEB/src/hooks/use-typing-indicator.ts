'use client'

import { useState, useEffect, useRef, useCallback } from 'react'

interface TypingUser {
  id: string | number
  name: string
  imageurl?: string
}

interface UseTypingIndicatorOptions {
  contactFormId: string | number
  currentUserId: string | number
  enabled?: boolean
  typingTimeout?: number
}

export function useTypingIndicator({
  contactFormId,
  currentUserId,
  enabled = true,
  typingTimeout = 3000 // 3 seconds
}: UseTypingIndicatorOptions) {
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([])
  const [isCurrentUserTyping, setIsCurrentUserTyping] = useState(false)
  
  const typingTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const typingUsersTimeoutRef = useRef<Map<string | number, NodeJS.Timeout>>(new Map())

  // Start typing indicator for current user
  const startTyping = useCallback(() => {
    if (!enabled || isCurrentUserTyping) return

    setIsCurrentUserTyping(true)
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set timeout to automatically stop typing
    typingTimeoutRef.current = setTimeout(() => {
      setIsCurrentUserTyping(false)
    }, typingTimeout)

    // In a real implementation, you would send this to the server
    // For now, we'll just manage local state
    console.log('User started typing in contact form:', contactFormId)
  }, [enabled, isCurrentUserTyping, typingTimeout, contactFormId])

  // Stop typing indicator for current user
  const stopTyping = useCallback(() => {
    if (!enabled || !isCurrentUserTyping) return

    setIsCurrentUserTyping(false)
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
      typingTimeoutRef.current = undefined
    }

    // In a real implementation, you would send this to the server
    console.log('User stopped typing in contact form:', contactFormId)
  }, [enabled, isCurrentUserTyping, contactFormId])

  // Add typing user (from server updates)
  const addTypingUser = useCallback((user: TypingUser) => {
    if (!enabled || user.id === currentUserId) return

    setTypingUsers(prev => {
      // Check if user is already in the list
      if (prev.some(u => u.id === user.id)) {
        return prev
      }
      return [...prev, user]
    })

    // Clear existing timeout for this user
    const existingTimeout = typingUsersTimeoutRef.current.get(user.id)
    if (existingTimeout) {
      clearTimeout(existingTimeout)
    }

    // Set timeout to remove user from typing list
    const timeout = setTimeout(() => {
      removeTypingUser(user.id)
    }, typingTimeout)
    
    typingUsersTimeoutRef.current.set(user.id, timeout)
  }, [enabled, currentUserId, typingTimeout])

  // Remove typing user
  const removeTypingUser = useCallback((userId: string | number) => {
    setTypingUsers(prev => prev.filter(u => u.id !== userId))
    
    // Clear timeout for this user
    const timeout = typingUsersTimeoutRef.current.get(userId)
    if (timeout) {
      clearTimeout(timeout)
      typingUsersTimeoutRef.current.delete(userId)
    }
  }, [])

  // Clear all typing users
  const clearAllTypingUsers = useCallback(() => {
    setTypingUsers([])
    
    // Clear all timeouts
    typingUsersTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
    typingUsersTimeoutRef.current.clear()
  }, [])

  // Simulate receiving typing updates from server
  // In a real implementation, this would come from WebSocket or polling
  const simulateTypingUpdate = useCallback((users: TypingUser[]) => {
    if (!enabled) return

    // Filter out current user
    const otherUsers = users.filter(u => u.id !== currentUserId)
    
    // Update typing users
    setTypingUsers(otherUsers)
    
    // Clear existing timeouts
    typingUsersTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
    typingUsersTimeoutRef.current.clear()
    
    // Set new timeouts for each user
    otherUsers.forEach(user => {
      const timeout = setTimeout(() => {
        removeTypingUser(user.id)
      }, typingTimeout)
      typingUsersTimeoutRef.current.set(user.id, timeout)
    })
  }, [enabled, currentUserId, typingTimeout, removeTypingUser])

  // Handle input changes to manage typing state
  const handleInputChange = useCallback((value: string) => {
    if (!enabled) return

    if (value.trim()) {
      startTyping()
    } else {
      stopTyping()
    }
  }, [enabled, startTyping, stopTyping])

  // Handle form submission to stop typing
  const handleSubmit = useCallback(() => {
    stopTyping()
  }, [stopTyping])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
      
      typingUsersTimeoutRef.current.forEach(timeout => clearTimeout(timeout))
      typingUsersTimeoutRef.current.clear()
    }
  }, [])

  // Auto-stop typing when component unmounts or contactFormId changes
  useEffect(() => {
    return () => {
      stopTyping()
    }
  }, [contactFormId, stopTyping])

  return {
    typingUsers,
    isCurrentUserTyping,
    startTyping,
    stopTyping,
    addTypingUser,
    removeTypingUser,
    clearAllTypingUsers,
    simulateTypingUpdate,
    handleInputChange,
    handleSubmit
  }
}
