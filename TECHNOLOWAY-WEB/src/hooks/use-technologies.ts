import { useMemo, useCallback } from 'react';
import { technologies } from '@/data/technologies';
import { Technology, TechnologyFilters } from '@/types/shared/technology';

export function useTechnologies(filters: TechnologyFilters) {
  // Memoized filtered technologies for performance
  const filteredTechnologies = useMemo(() => {
    return technologies.filter(tech => {
      const searchLower = filters.searchTerm.toLowerCase();
      const matchesSearch = tech.name.toLowerCase().includes(searchLower) ||
                           tech.description.toLowerCase().includes(searchLower) ||
                           tech.tags.some(tag => tag.toLowerCase().includes(searchLower));
      const matchesCategory = filters.selectedCategory === 'All' || tech.category === filters.selectedCategory;
      const matchesProficiency = filters.selectedProficiency === 'All' || tech.proficiencyLevel === filters.selectedProficiency;
      
      return matchesSearch && matchesCategory && matchesProficiency;
    });
  }, [filters]);

  // Memoized featured technologies
  const featuredTechnologies = useMemo(() => {
    return technologies.filter(tech => tech.isFeatured);
  }, []);

  // Memoized technology categories
  const technologyCategories = useMemo(() => {
    return Array.from(new Set(technologies.map(tech => tech.category)));
  }, []);

  // Memoized proficiency levels
  const technologyProficiencyLevels = useMemo(() => {
    return Array.from(new Set(technologies.map(tech => tech.proficiencyLevel)));
  }, []);

  // Memoized search suggestions
  const searchSuggestions = useMemo(() => {
    const allTags = technologies.flatMap(tech => tech.tags);
    const uniqueTags = Array.from(new Set(allTags));
    return uniqueTags.slice(0, 10); // Limit to 10 suggestions
  }, []);

  // Memoized technology stats
  const technologyStats = useMemo(() => {
    const totalTechnologies = technologies.length;
    const totalExperience = technologies.reduce((sum, tech) => sum + tech.yearsOfExperience, 0);
    const totalProjects = technologies.reduce((sum, tech) => sum + tech.projectsUsed, 0);
    const expertTechnologies = technologies.filter(tech => tech.proficiencyLevel === 'Expert').length;

    return {
      totalTechnologies,
      totalExperience,
      totalProjects,
      expertTechnologies,
      averageExperience: Math.round(totalExperience / totalTechnologies),
      averageProjects: Math.round(totalProjects / totalTechnologies)
    };
  }, []);

  return {
    filteredTechnologies,
    featuredTechnologies,
    technologyCategories,
    technologyProficiencyLevels,
    searchSuggestions,
    technologyStats,
    allTechnologies: technologies
  };
} 