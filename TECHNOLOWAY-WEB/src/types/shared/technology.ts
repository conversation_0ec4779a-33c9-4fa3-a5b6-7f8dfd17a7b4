export interface Technology {
  id: string;
  name: string;
  description: string;
  category: TechnologyCategory;
  type: string;
  proficiencyLevel: ProficiencyLevel;
  yearsOfExperience: number;
  projectsUsed: number;
  logo: string;
  website: string;
  documentation: string;
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
  useCases: string[];
  advantages: string[];
}

export type TechnologyCategory = 'Frontend' | 'Backend' | 'Language' | 'Database' | 'Cloud' | 'DevOps';

export type ProficiencyLevel = 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';

export interface TechnologyStats {
  label: string;
  value: string;
}

export interface TechnologyFilters {
  searchTerm: string;
  selectedCategory: string;
  selectedProficiency: string;
} 