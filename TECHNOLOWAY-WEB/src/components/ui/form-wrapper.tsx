'use client';

import { ReactNode, useState, useEffect } from 'react';

interface FormWrapperProps {
  children: ReactNode;
  className?: string;
  onSubmit?: (e: React.FormEvent) => void;
  [key: string]: any; // Allow any other props
}

export function FormWrapper({ children, className = '', onSubmit, ...props }: FormWrapperProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything on the server to avoid hydration mismatches
  if (!mounted) {
    return (
      <div className={className} suppressHydrationWarning>
        <div className="flex flex-col sm:flex-row gap-4" suppressHydrationWarning>
          <div className="flex-1 px-6 py-4 border border-gray-300 rounded-xl bg-gray-50" suppressHydrationWarning>
            Loading...
          </div>
          <div className="px-8 py-4 bg-gray-300 text-gray-600 rounded-xl" suppressHydrationWarning>
            Subscribe
          </div>
        </div>
      </div>
    );
  }

  return (
    <form
      className={className}
      onSubmit={onSubmit}
      suppressHydrationWarning
      {...props}
    >
      {children}
    </form>
  );
}