'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface DirectPreviewProps {
  pagePath: string
  onElementSelect: (element: any) => void
  onContentLoad: (content: Record<string, string>) => void
  interactiveMode: boolean
}

export default function DirectPreview({
  pagePath,
  onElementSelect,
  onContentLoad,
  interactiveMode
}: DirectPreviewProps) {
  const [iframeRef, setIframeRef] = useState<HTMLIFrameElement | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Use iframe with our custom preview API for better compatibility
  const previewUrl = `/api/visual-editor/preview?path=${encodeURIComponent(new URL(pagePath).pathname)}`

  useEffect(() => {
    if (!iframeRef) return

    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'ELEMENT_SELECTED') {
        onElementSelect(event.data.element)
      }
    }

    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [iframeRef, onElementSelect])

  useEffect(() => {
    if (!iframeRef || !iframeRef.contentWindow) return

    // Send interactive mode update to iframe
    iframeRef.contentWindow.postMessage({
      type: 'UPDATE_INTERACTIVE_MODE',
      interactiveMode: interactiveMode
    }, '*')
  }, [interactiveMode, iframeRef])

  useEffect(() => {
    const loadContentMapping = async () => {
      try {
        // Load content mapping
        const contentResponse = await fetch(`/api/content/page-content?pagePath=${encodeURIComponent(new URL(pagePath).pathname)}`, {
          credentials: 'include'
        })

        if (contentResponse.ok) {
          const contentData = await contentResponse.json()
          if (contentData.success) {
            onContentLoad(contentData.data.contentMap)
          }
        }
      } catch (err) {
        console.error('Error loading content mapping:', err)
      }
    }

    if (pagePath) {
      loadContentMapping()
    }
  }, [pagePath, onContentLoad])

  const handleIframeLoad = () => {
    setLoading(false)
    setError(null)
  }

  const handleIframeError = () => {
    setError('Failed to load preview')
    setLoading(false)
  }



  return (
    <div className="w-full h-full relative">
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading preview...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-white z-10">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error loading preview: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <iframe
        ref={setIframeRef}
        src={previewUrl}
        className="w-full h-full min-h-screen border-0"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        sandbox="allow-scripts allow-same-origin allow-forms"
        style={{ minHeight: '100vh' }}
      />

      {interactiveMode && !loading && !error && (
        <div className="absolute bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm shadow-lg pointer-events-none">
          Click on any text to edit
        </div>
      )}
    </div>
  )
}
