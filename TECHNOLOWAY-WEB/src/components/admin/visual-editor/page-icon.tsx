import React from 'react'
import {
  HomeIcon,
  InformationCircleIcon,
  BriefcaseIcon,
  PhoneIcon,
  UserGroupIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'

interface PageIconProps {
  pageId: string
  className?: string
}

const iconMap: Record<string, React.ComponentType<{ className?: string }>> = {
  home: HomeIcon,
  about: InformationCircleIcon,
  services: BriefcaseIcon,
  contact: PhoneIcon,
  team: UserGroupIcon,
}

export default function PageIcon({ pageId, className }: PageIconProps) {
  const IconComponent = iconMap[pageId] || DocumentTextIcon
  
  return React.createElement(IconComponent, { className })
} 