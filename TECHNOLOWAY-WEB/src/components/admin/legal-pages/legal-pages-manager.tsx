'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  DocumentTextIcon,
  ScaleIcon
} from '@heroicons/react/24/outline'
import { LegalPageModal } from './legal-page-modal'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface LegalPage {
  id: string
  title: string
  slug: string
  metaDescription?: string
  content: string
  isActive: boolean
  displayOrder: number
  lastModified: string
  modifiedBy?: string
  createdAt: string
  updatedAt: string
  [key: string]: any
}

interface LegalPagesManagerProps {
  config: CrudConfig<LegalPage>
}

export function LegalPagesManager({ config }: LegalPagesManagerProps) {
  const [legalPages, setLegalPages] = useState<LegalPage[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [sortBy, setSortBy] = useState(config.defaultSort?.field || 'updatedAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(config.defaultSort?.direction || 'desc')
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>(config.defaultViewSettings?.mode || 'list')
  const [density, setDensity] = useState<'compact' | 'comfortable'>(config.defaultViewSettings?.density || 'comfortable')
  const [visibleColumns, setVisibleColumns] = useState<string[]>(config.defaultViewSettings?.visibleColumns || [])
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnSelector, setShowColumnSelector] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingPage, setEditingPage] = useState<LegalPage | null>(null)

  const searchInputRef = useRef<HTMLInputElement>(null)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch legal pages
  const fetchLegalPages = async (preserveFocus = false) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: config.pageSize?.toString() || '10',
        sortBy,
        sortOrder,
      })

      if (debouncedSearchQuery.trim()) {
        params.append('search', debouncedSearchQuery.trim())
      }

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch legal pages: ${response.statusText}`)
      }

      const data = await response.json()
      
      setLegalPages(data.data || [])
      setTotalPages(data.pagination?.totalPages || 1)
      setTotalItems(data.pagination?.total || 0)

      // Preserve search input focus
      if (preserveFocus && searchInputRef.current) {
        setTimeout(() => {
          searchInputRef.current?.focus()
        }, 0)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch legal pages'
      setError(errorMessage)
      console.error('Error fetching legal pages:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchLegalPages(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create legal page')

      setIsCreateModalOpen(false)
      fetchLegalPages()
      alert('Legal page created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create legal page'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to update legal page')

      setIsEditModalOpen(false)
      setEditingPage(null)
      fetchLegalPages()
      alert('Legal page updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update legal page'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this legal page?')) return

    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) throw new Error('Failed to delete legal page')

      fetchLegalPages()
      alert('Legal page deleted successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete legal page'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle toggle active status
  const handleToggleActive = async (id: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !currentStatus }),
      })
      
      if (!response.ok) throw new Error('Failed to update legal page status')

      fetchLegalPages()
      alert(`Legal page ${!currentStatus ? 'activated' : 'deactivated'} successfully!`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update legal page status'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle sort
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  // Handle bulk actions
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return
    if (!confirm(`Are you sure you want to delete ${selectedItems.length} legal page(s)?`)) return

    try {
      await Promise.all(
        selectedItems.map(id =>
          fetch(`/api/admin/${config.endpoint}/${id}`, { method: 'DELETE' })
        )
      )
      
      setSelectedItems([])
      fetchLegalPages()
      alert(`${selectedItems.length} legal page(s) deleted successfully!`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete legal pages'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  const handleBulkToggleActive = async (activate: boolean) => {
    if (selectedItems.length === 0) return

    try {
      await Promise.all(
        selectedItems.map(id =>
          fetch(`/api/admin/${config.endpoint}/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ isActive: activate }),
          })
        )
      )
      
      setSelectedItems([])
      fetchLegalPages()
      alert(`${selectedItems.length} legal page(s) ${activate ? 'activated' : 'deactivated'} successfully!`)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update legal pages'
      setError(errorMessage)
      alert(errorMessage)
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === legalPages.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(legalPages.map(page => page.id))
    }
  }

  // Handle individual select
  const handleSelectItem = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get field value for display
  const getFieldValue = (page: LegalPage, fieldKey: string) => {
    const field = config.fields.find(f => f.name === fieldKey)
    const value = page[fieldKey]

    if (!field) return value

    switch (field.type) {
      case 'checkbox':
        return value ? 'Yes' : 'No'
      case 'date':
        return value ? formatDate(value) : '-'
      case 'number':
        return value?.toLocaleString() || '0'
      default:
        return value || '-'
    }
  }

  if (loading && legalPages.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        {config.permissions.create && (
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <span>+</span>
            <span>Add {config.title.slice(0, -1)}</span>
          </button>
        )}
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder={config.searchPlaceholder || 'Search legal pages by title, slug, content...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="List view"
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Grid view"
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('cards')}
                className={`p-2 rounded transition-colors ${
                  viewMode === 'cards'
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
                title="Card view"
              >
                <RectangleStackIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{density === 'compact' ? 'Compact' : 'Comfortable'}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    <button
                      onClick={() => {
                        setDensity('compact')
                        setShowDensityMenu(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        density === 'compact'
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700'
                      }`}
                    >
                      Compact
                    </button>
                    <button
                      onClick={() => {
                        setDensity('comfortable')
                        setShowDensityMenu(false)
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                        density === 'comfortable'
                          ? 'bg-blue-50 text-blue-700'
                          : 'text-gray-700'
                      }`}
                    >
                      Comfortable
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{config.fields.filter(f => f.name !== 'id').length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            setVisibleColumns(['title', 'slug', 'metaDescription', 'displayOrder', 'lastModified', 'modifiedBy', 'isActive'])
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>

                      {config.fields
                        .filter(field => field.name !== 'id')
                        .map((field) => {
                          const isVisible = visibleColumns.includes(field.name)
                          return (
                            <label
                              key={field.name}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setVisibleColumns([...visibleColumns, field.name])
                                  } else {
                                    setVisibleColumns(visibleColumns.filter(col => col !== field.name))
                                  }
                                }}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm text-gray-700">{field.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {config.fields.filter(f => f.name !== 'id').map((field) => (
                        <button
                          key={field.name}
                          onClick={() => {
                            handleSort(field.name)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === field.name
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{field.label}</span>
                          {sortBy === field.name && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {sortBy} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {density === 'compact' ? 'Compact' : 'Comfortable'}
            </span>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedItems.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900">
                {selectedItems.length} legal page{selectedItems.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkToggleActive(true)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
              >
                Activate
              </button>
              <button
                onClick={() => handleBulkToggleActive(false)}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200"
              >
                Deactivate
              </button>
              <button
                onClick={handleBulkDelete}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200"
              >
                Delete
              </button>
              <button
                onClick={() => setSelectedItems([])}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Legal Pages Display */}
      {viewMode === 'list' ? (
        /* Table View */
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {/* Checkbox column for bulk actions */}
                  {config.enableBulkActions && (
                    <th className={`px-3 text-left ${density === 'compact' ? 'py-2' : 'py-3'} w-12`}>
                      <input
                        type="checkbox"
                        checked={selectedItems.length === legalPages.length && legalPages.length > 0}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </th>
                  )}
                  {/* Dynamic columns based on visibility settings */}
                  {visibleColumns.includes('title') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-48 ${
                        density === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('title')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Title</span>
                        {sortBy === 'title' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('slug') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-40 ${
                        density === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('slug')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Slug</span>
                        {sortBy === 'slug' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('metaDescription') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-56 ${
                      density === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Meta Description
                    </th>
                  )}
                  {visibleColumns.includes('displayOrder') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 ${
                        density === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('displayOrder')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Order</span>
                        {sortBy === 'displayOrder' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('lastModified') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                        density === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('lastModified')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Last Modified</span>
                        {sortBy === 'lastModified' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {visibleColumns.includes('modifiedBy') && (
                    <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32 ${
                      density === 'compact' ? 'py-2' : 'py-3'
                    }`}>
                      Modified By
                    </th>
                  )}
                  {visibleColumns.includes('isActive') && (
                    <th
                      className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 ${
                        density === 'compact' ? 'py-2' : 'py-3'
                      }`}
                      onClick={() => handleSort('isActive')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>Status</span>
                        {sortBy === 'isActive' && (
                          sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                  )}
                  {/* Actions column */}
                  <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32 ${
                    density === 'compact' ? 'py-2' : 'py-3'
                  }`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {legalPages.map((page, index) => (
                  <motion.tr
                    key={page.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`hover:bg-gray-50 ${
                      selectedItems.includes(page.id) ? 'bg-blue-50' : ''
                    }`}
                  >
                    {/* Checkbox */}
                    {config.enableBulkActions && (
                      <td className={`px-3 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        <input
                          type="checkbox"
                          checked={selectedItems.includes(page.id)}
                          onChange={() => handleSelectItem(page.id)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>
                    )}

                    {/* Title */}
                    {visibleColumns.includes('title') && (
                      <td className={`px-4 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        <div className="flex items-center">
                          <ScaleIcon className="h-5 w-5 text-gray-400 mr-3 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {page.title}
                            </div>
                            <div className="text-sm text-gray-500 truncate">
                              /{page.slug}
                            </div>
                          </div>
                        </div>
                      </td>
                    )}

                    {/* Slug */}
                    {visibleColumns.includes('slug') && (
                      <td className={`px-4 text-sm text-gray-900 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        <span className="truncate block">{page.slug}</span>
                      </td>
                    )}

                    {/* Meta Description */}
                    {visibleColumns.includes('metaDescription') && (
                      <td className={`px-4 text-sm text-gray-500 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        <span className="truncate block max-w-xs">
                          {page.metaDescription || '-'}
                        </span>
                      </td>
                    )}

                    {/* Display Order */}
                    {visibleColumns.includes('displayOrder') && (
                      <td className={`px-4 text-sm text-gray-900 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        {page.displayOrder}
                      </td>
                    )}

                    {/* Last Modified */}
                    {visibleColumns.includes('lastModified') && (
                      <td className={`px-4 text-sm text-gray-500 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        {page.lastModified ? formatDate(page.lastModified) : '-'}
                      </td>
                    )}

                    {/* Modified By */}
                    {visibleColumns.includes('modifiedBy') && (
                      <td className={`px-4 text-sm text-gray-500 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        {page.modifiedBy || '-'}
                      </td>
                    )}

                    {/* Status */}
                    {visibleColumns.includes('isActive') && (
                      <td className={`px-4 ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          page.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {page.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                    )}

                    {/* Actions */}
                    <td className={`px-4 text-sm font-medium ${density === 'compact' ? 'py-2' : 'py-4'}`}>
                      <div className="flex items-center space-x-2">
                        {config.permissions.read && (
                          <button
                            onClick={() => window.open(`/legal/${page.slug}`, '_blank')}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="Preview"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        )}

                        {config.permissions.update && (
                          <button
                            onClick={() => {
                              setEditingPage(page)
                              setIsEditModalOpen(true)
                            }}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                        )}

                        <button
                          onClick={() => handleToggleActive(page.id, page.isActive)}
                          className={`text-gray-400 hover:text-green-600 transition-colors ${
                            page.isActive ? 'text-green-600' : ''
                          }`}
                          title={page.isActive ? 'Deactivate' : 'Activate'}
                        >
                          <PowerIcon className="h-4 w-4" />
                        </button>

                        {config.permissions.delete && (
                          <button
                            onClick={() => handleDelete(page.id)}
                            className="text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        /* Grid View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {legalPages.map((page, index) => (
            <motion.div
              key={page.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow ${
                selectedItems.includes(page.id) ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <ScaleIcon className="h-5 w-5 text-gray-400 mr-2" />
                  {config.enableBulkActions && (
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(page.id)}
                      onChange={() => handleSelectItem(page.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                    />
                  )}
                </div>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  page.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {page.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Content */}
              <div className="mb-4">
                <h3 className="text-sm font-medium text-gray-900 mb-1 truncate">
                  {page.title}
                </h3>
                <p className="text-xs text-gray-500 mb-2">/{page.slug}</p>
                {page.metaDescription && (
                  <p className="text-xs text-gray-600 line-clamp-2">
                    {page.metaDescription}
                  </p>
                )}
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Order: {page.displayOrder}</span>
                <div className="flex items-center space-x-1">
                  {config.permissions.read && (
                    <button
                      onClick={() => window.open(`/legal/${page.slug}`, '_blank')}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Preview"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                  )}
                  {config.permissions.update && (
                    <button
                      onClick={() => {
                        setEditingPage(page)
                        setIsEditModalOpen(true)
                      }}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => handleToggleActive(page.id, page.isActive)}
                    className={`text-gray-400 hover:text-green-600 transition-colors ${
                      page.isActive ? 'text-green-600' : ''
                    }`}
                    title={page.isActive ? 'Deactivate' : 'Activate'}
                  >
                    <PowerIcon className="h-4 w-4" />
                  </button>
                  {config.permissions.delete && (
                    <button
                      onClick={() => handleDelete(page.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : viewMode === 'cards' ? (
        /* Card View */
        <div className="space-y-4">
          {legalPages.map((page, index) => (
            <motion.div
              key={page.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${
                selectedItems.includes(page.id) ? 'ring-2 ring-blue-500' : ''
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="flex items-center">
                    <ScaleIcon className="h-6 w-6 text-gray-400 mr-3" />
                    {config.enableBulkActions && (
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(page.id)}
                        onChange={() => handleSelectItem(page.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {page.title}
                      </h3>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        page.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {page.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      <span>URL: /{page.slug}</span>
                      <span>Order: {page.displayOrder}</span>
                      {page.lastModified && (
                        <span>Modified: {formatDate(page.lastModified)}</span>
                      )}
                    </div>

                    {page.metaDescription && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {page.metaDescription}
                      </p>
                    )}

                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      {page.modifiedBy && <span>By: {page.modifiedBy}</span>}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  {config.permissions.read && (
                    <button
                      onClick={() => window.open(`/legal/${page.slug}`, '_blank')}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Preview"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                  )}
                  {config.permissions.update && (
                    <button
                      onClick={() => {
                        setEditingPage(page)
                        setIsEditModalOpen(true)
                      }}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  )}
                  <button
                    onClick={() => handleToggleActive(page.id, page.isActive)}
                    className={`text-gray-400 hover:text-green-600 transition-colors ${
                      page.isActive ? 'text-green-600' : ''
                    }`}
                    title={page.isActive ? 'Deactivate' : 'Activate'}
                  >
                    <PowerIcon className="h-5 w-5" />
                  </button>
                  {config.permissions.delete && (
                    <button
                      onClick={() => handleDelete(page.id)}
                      className="text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : null}

      {/* Empty State */}
      {!loading && legalPages.length === 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No legal pages found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first legal page.'}
          </p>
          {config.permissions.create && !searchQuery && (
            <div className="mt-6">
              <button
                onClick={() => setIsCreateModalOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Legal Page
              </button>
            </div>
          )}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">
                  {(currentPage - 1) * (config.pageSize || 10) + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * (config.pageSize || 10), totalItems)}
                </span>{' '}
                of{' '}
                <span className="font-medium">{totalItems}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === pageNum
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  )
                })}

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Create Modal */}
      <LegalPageModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Legal Page"
        fields={config.fields}
        layout={config.formLayout}
      />

      {/* Edit Modal */}
      <LegalPageModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingPage(null)
        }}
        onSubmit={(data) => editingPage && handleUpdate(editingPage.id, data)}
        title="Edit Legal Page"
        initialData={editingPage}
        fields={config.fields}
        layout={config.formLayout}
      />
    </div>
  )
}
