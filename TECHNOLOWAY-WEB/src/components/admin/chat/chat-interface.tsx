'use client'

import React, { useState, useEffect, useRef } from 'react'
import { MessageBubble } from './message-bubble'
import { TypingIndicator } from './typing-indicator'

interface ChatMessage {
  id: string | number
  name: string
  email: string
  message: string
  messageType: string
  contentType: string
  createdAt: string
  isRead?: boolean
  attachments?: Array<{
    id: string
    filename: string
    size: number
    mimeType: string
    url: string
  }>
  sender?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  receiver?: {
    id: string | number
    email: string
    firstname?: string
    lastname?: string
    imageurl?: string
    role: string
  }
  parent?: {
    id: string | number
    subject: string
    message: string
    createdAt: string
  }
}

interface ChatInterfaceProps {
  messages: ChatMessage[]
  currentUserId: string | number
  isLoading?: boolean
  onMessageSelect?: (message: ChatMessage) => void
  selectedMessageId?: string | number
  showTypingIndicator?: boolean
  typingUsers?: Array<{
    id: string | number
    name: string
  }>
  className?: string
  autoScroll?: boolean
}

export function ChatInterface({
  messages,
  currentUserId,
  isLoading = false,
  onMessageSelect,
  selectedMessageId,
  showTypingIndicator = false,
  typingUsers = [],
  className = '',
  autoScroll = true
}: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const [isUserScrolling, setIsUserScrolling] = useState(false)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && !isUserScrolling) {
      scrollToBottom()
    }
  }, [messages, autoScroll, isUserScrolling])

  // Reset user scrolling flag after a delay
  useEffect(() => {
    if (isUserScrolling) {
      const timer = setTimeout(() => {
        setIsUserScrolling(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [isUserScrolling])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current
      const isAtBottom = scrollHeight - scrollTop <= clientHeight + 50
      
      if (!isAtBottom) {
        setIsUserScrolling(true)
      }
    }
  }

  const groupMessagesByDate = (messages: ChatMessage[]) => {
    const groups: { [key: string]: ChatMessage[] } = {}
    
    messages.forEach(message => {
      const date = new Date(message.createdAt).toDateString()
      if (!groups[date]) {
        groups[date] = []
      }
      groups[date].push(message)
    })
    
    return groups
  }

  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })
    }
  }

  const messageGroups = groupMessagesByDate(messages)

  if (isLoading && messages.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="flex items-center space-x-2 text-gray-500">
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Loading messages...</span>
        </div>
      </div>
    )
  }

  if (messages.length === 0) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <p className="text-lg font-medium">No messages yet</p>
          <p className="text-sm">Start a conversation by sending a message</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Messages Container */}
      <div 
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto px-4 py-2 space-y-1"
        onScroll={handleScroll}
      >
        {Object.entries(messageGroups).map(([date, groupMessages]) => (
          <div key={date}>
            {/* Date Header */}
            <div className="flex justify-center my-4">
              <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                {formatDateHeader(date)}
              </div>
            </div>
            
            {/* Messages for this date */}
            {groupMessages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                currentUserId={currentUserId}
                isSelected={selectedMessageId?.toString() === message.id?.toString()}
                onSelect={onMessageSelect ? () => onMessageSelect(message) : undefined}
                showReplyContext={true}
              />
            ))}
          </div>
        ))}
        
        {/* Typing Indicator */}
        {showTypingIndicator && typingUsers.length > 0 && (
          <TypingIndicator users={typingUsers} />
        )}
        
        {/* Auto-scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to bottom button */}
      {isUserScrolling && (
        <div className="absolute bottom-4 right-4">
          <button
            onClick={scrollToBottom}
            className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-colors"
            title="Scroll to bottom"
          >
            <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </button>
        </div>
      )}
    </div>
  )
}
