'use client'

import React from 'react'

interface TypingIndicatorProps {
  users: Array<{
    id: string | number
    name: string
    imageurl?: string
  }>
  className?: string
}

export function TypingIndicator({ users, className = '' }: TypingIndicatorProps) {
  if (users.length === 0) return null

  const getAvatarInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatTypingText = () => {
    if (users.length === 1) {
      return `${users[0].name} is typing...`
    } else if (users.length === 2) {
      return `${users[0].name} and ${users[1].name} are typing...`
    } else {
      return `${users[0].name} and ${users.length - 1} others are typing...`
    }
  }

  return (
    <div className={`flex items-start space-x-2 mb-4 ${className}`}>
      {/* Avatar(s) */}
      <div className="flex-shrink-0">
        {users.length === 1 ? (
          users[0].imageurl ? (
            <img
              src={users[0].imageurl}
              alt={users[0].name}
              className="h-8 w-8 rounded-full object-cover"
            />
          ) : (
            <div className="h-8 w-8 rounded-full bg-gray-500 flex items-center justify-center text-xs font-medium text-white">
              {getAvatarInitials(users[0].name)}
            </div>
          )
        ) : (
          <div className="flex -space-x-2">
            {users.slice(0, 3).map((user, index) => (
              <div key={user.id} className="relative">
                {user.imageurl ? (
                  <img
                    src={user.imageurl}
                    alt={user.name}
                    className="h-6 w-6 rounded-full object-cover border-2 border-white"
                  />
                ) : (
                  <div className="h-6 w-6 rounded-full bg-gray-500 flex items-center justify-center text-xs font-medium text-white border-2 border-white">
                    {getAvatarInitials(user.name).charAt(0)}
                  </div>
                )}
              </div>
            ))}
            {users.length > 3 && (
              <div className="h-6 w-6 rounded-full bg-gray-400 flex items-center justify-center text-xs font-medium text-white border-2 border-white">
                +{users.length - 3}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Typing bubble */}
      <div className="bg-gray-100 rounded-lg px-3 py-2 max-w-xs">
        <div className="flex items-center space-x-1">
          <span className="text-sm text-gray-600">{formatTypingText()}</span>
          <div className="flex space-x-1">
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        </div>
      </div>
    </div>
  )
}
