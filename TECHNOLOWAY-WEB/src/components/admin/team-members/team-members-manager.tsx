'use client'

import React, { useState, useEffect, useRef, useCallback, memo } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  UserIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import { TeamMemberModal } from './team-member-modal'
import { TeamMemberAvatar } from './team-member-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface TeamMember {
  id: string
  name: string
  position: string
  email: string
  phone: string
  hireDate: string
  isActive: boolean
  updatedAt: string
  [key: string]: any
}

interface TeamMembersManagerProps {
  config: CrudConfig<TeamMember>
}

// Memoized action icon component
const ActionIcon = memo<{ actionType: string }>(({ actionType }) => {
  const iconMap = {
    view: EyeIcon,
    edit: PencilIcon,
    'toggle-status': PowerIcon,
    delete: TrashIcon,
  } as const

  const Icon = iconMap[actionType as keyof typeof iconMap] || UserIcon
  return <Icon className="h-4 w-4" aria-hidden="true" />
})
ActionIcon.displayName = 'ActionIcon'

// Memoized search component
const SearchBar = memo<{
  searchQuery: string
  onSearchChange: (query: string) => void
  placeholder: string
  searchInputRef: React.RefObject<HTMLInputElement>
}>(({ searchQuery, onSearchChange, placeholder, searchInputRef }) => (
  <div className="relative flex-1 max-w-md">
    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
      <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
    </div>
    <input
      ref={searchInputRef}
      type="text"
      value={searchQuery}
      onChange={(e) => onSearchChange(e.target.value)}
      className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
      placeholder={placeholder}
      aria-label="Search team members"
    />
    {searchQuery && (
      <button
        onClick={() => onSearchChange('')}
        className="absolute inset-y-0 right-0 pr-3 flex items-center"
        aria-label="Clear search"
      >
        <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
      </button>
    )}
  </div>
))
SearchBar.displayName = 'SearchBar'

// Memoized view controls component
const ViewControls = memo<{
  viewMode: string
  onViewModeChange: (mode: string) => void
  displayDensity: string
  onDensityChange: (density: string) => void
  showColumnMenu: boolean
  onColumnMenuToggle: () => void
  showDensityMenu: boolean
  onDensityMenuToggle: () => void
}>(({ 
  viewMode, 
  onViewModeChange, 
  displayDensity, 
  onDensityChange, 
  showColumnMenu, 
  onColumnMenuToggle, 
  showDensityMenu, 
  onDensityMenuToggle 
}) => {
  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon,
  } as const

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  } as const

  return (
    <div className="flex items-center space-x-2">
      {/* View Mode Toggle */}
      <div className="flex bg-gray-100 rounded-lg p-1">
        {Object.entries(viewModeIcons).map(([mode, Icon]) => (
          <button
            key={mode}
            onClick={() => onViewModeChange(mode)}
            className={`p-2 rounded-md transition-colors ${
              viewMode === mode
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            aria-label={`Switch to ${mode} view`}
            aria-pressed={viewMode === mode}
          >
            <Icon className="h-4 w-4" />
          </button>
        ))}
      </div>

      {/* Density Control */}
      <div className="relative">
        <button
          onClick={onDensityMenuToggle}
          className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          aria-label="Change display density"
          aria-expanded={showDensityMenu}
        >
          <AdjustmentsHorizontalIcon className="h-4 w-4" />
          <span>{densityLabels[displayDensity as keyof typeof densityLabels]}</span>
          <ChevronDownIcon className="h-4 w-4" />
        </button>

        {showDensityMenu && (
          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div className="py-1" role="menu">
              {Object.entries(densityLabels).map(([density, label]) => (
                <button
                  key={density}
                  onClick={() => {
                    onDensityChange(density)
                    onDensityMenuToggle()
                  }}
                  className={`block w-full text-left px-4 py-2 text-sm ${
                    displayDensity === density
                      ? 'bg-blue-100 text-blue-900'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                  role="menuitem"
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Column Visibility */}
      <button
        onClick={onColumnMenuToggle}
        className="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        aria-label="Toggle column visibility"
        aria-expanded={showColumnMenu}
      >
        <EyeIcon className="h-4 w-4" />
        <span>Columns</span>
        <ChevronDownIcon className="h-4 w-4" />
      </button>
    </div>
  )
})
ViewControls.displayName = 'ViewControls'

export function TeamMembersManager({ config }: TeamMembersManagerProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'name', 'position', 'email', 'phone', 'hireDate', 'updatedAt', 'isActive'
  ])
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)

  // Add filter state for each filter in config.filters
  const initialFilterState = (config.filters || []).reduce((acc, filter) => {
    acc[filter.key] = ''
    return acc
  }, {} as Record<string, string>)
  const [filters, setFilters] = useState<Record<string, string>>(initialFilterState)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch team members
  const fetchTeamMembers = useCallback(async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
        ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== '')),
      })

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch team members')

      const data = await response.json()

      setTeamMembers(data.data || [])
      setTotalPages(data.pagination?.totalPages || 1)
      setError(null)
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching team members:', err)
      }
      setError(err instanceof Error ? err.message : 'Failed to fetch team members')
    } finally {
      setLoading(false)
    }
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, config.endpoint, config.pageSize, filters])

  useEffect(() => {
    const isSearching = debouncedSearchQuery !== ''
    fetchTeamMembers(isSearching)
  }, [fetchTeamMembers])

  // Handle create
  const handleCreate = useCallback(async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create team member')

      setIsCreateModalOpen(false)
      fetchTeamMembers()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create team member'
      setError(errorMessage)
      throw err
    }
  }, [config.endpoint, fetchTeamMembers])

  // Handle update
  const handleUpdate = useCallback(async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to update team member (${response.status})`)
      }

      setIsEditModalOpen(false)
      setEditingMember(null)
      fetchTeamMembers()
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Update error:', err)
      }
      const errorMessage = err instanceof Error ? err.message : 'Failed to update team member'
      setError(errorMessage)
      throw err
    }
  }, [config.endpoint, fetchTeamMembers])

  // Handle delete
  const handleDelete = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete team member')
      }

      setError(null)
      fetchTeamMembers()
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Delete error:', err)
      }
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete team member'
      setError(errorMessage)
      throw err
    }
  }, [config.endpoint, fetchTeamMembers])

  // Handle action
  const handleAction = useCallback(async (action: string, item: TeamMember) => {
    setActionLoading(item.id)

    try {
      switch (action) {
        case 'view':
          // Handle view action
          break
        case 'edit':
          setEditingMember(item)
          setIsEditModalOpen(true)
          break
        case 'delete':
          if (confirm('Are you sure you want to delete this team member?')) {
            await handleDelete(item.id)
          }
          break
        case 'toggle-status':
          await handleToggleStatus(item.id, !item.isActive)
          break
        default:
          break
      }
    } catch (err) {
      // Error already handled in individual functions
    } finally {
      setActionLoading(null)
    }
  }, [handleDelete])

  // Handle toggle status
  const handleToggleStatus = useCallback(async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) {
        throw new Error('Failed to update status')
      }

      fetchTeamMembers()
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Toggle status error:', err)
      }
      setError(err instanceof Error ? err.message : 'Failed to update status')
    }
  }, [config.endpoint, fetchTeamMembers])

  // Format date utility
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }, [])

  // Handle bulk action
  const handleBulkAction = useCallback(async (action: string, memberIds: string[]) => {
    if (memberIds.length === 0) return

    try {
      const response = await fetch(`/api/admin/${config.endpoint}/bulk-action`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ids: memberIds }),
      })

      if (!response.ok) {
        throw new Error('Failed to perform bulk action')
      }

      setSelectedMembers([])
      fetchTeamMembers()
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Bulk action error:', err)
      }
      setError(err instanceof Error ? err.message : 'Failed to perform bulk action')
    }
  }, [config.endpoint, fetchTeamMembers])

  // Handle select all
  const handleSelectAll = useCallback(() => {
    if (selectedMembers.length === teamMembers.length) {
      setSelectedMembers([])
    } else {
      setSelectedMembers(teamMembers.map(member => member.id))
    }
  }, [selectedMembers.length, teamMembers])

  // Handle select member
  const handleSelectMember = useCallback((memberId: string) => {
    setSelectedMembers(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    )
  }, [])

  // Handle sort
  const handleSort = useCallback((field: string) => {
    setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')
    setSortBy(field)
  }, [])

  // Handle column toggle
  const handleColumnToggle = useCallback((columnKey: string) => {
    setVisibleColumns(prev => 
      prev.includes(columnKey)
        ? prev.filter(col => col !== columnKey)
        : [...prev, columnKey]
    )
  }, [])

  // Reset view settings
  const resetViewSettings = useCallback(() => {
    setViewMode('list')
    setDisplayDensity('comfortable')
    setVisibleColumns(['name', 'position', 'email', 'phone', 'hireDate', 'updatedAt', 'isActive'])
    setSortBy('updatedAt')
    setSortOrder('desc')
  }, [])

  // Use displayDensity for table row/column padding
  const densityPadding = displayDensity === 'compact' ? 'py-2' : displayDensity === 'spacious' ? 'py-6' : 'py-4'
  const densityHeaderPadding = displayDensity === 'compact' ? 'py-2' : displayDensity === 'spacious' ? 'py-4' : 'py-3'

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
            <p className="text-gray-600 mt-1">{config.description}</p>
          </div>
          <button
            onClick={() => setIsCreateModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            aria-label="Add new team member"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Team Member
          </button>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search Bar */}
          {config.enableSearch && (
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder={config.searchPlaceholder || 'Search team members...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => {
                      setSearchQuery('')
                      searchInputRef.current?.focus()
                    }}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Controls */}
          <div className="flex items-center gap-2">
            {/* Filters */}
            {config.enableFilters && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                  showFilters
                    ? 'border-blue-500 text-blue-700 bg-blue-50'
                    : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                }`}
              >
                <FunnelIcon className="w-4 h-4 mr-2" />
                Filters
              </button>
            )}

            {/* View Controls */}
            {config.enableViewControls && (
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 ${viewMode === 'list' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="List View"
                >
                  <ListBulletIcon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 ${viewMode === 'grid' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="Grid View"
                >
                  <Squares2X2Icon className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('card')}
                  className={`p-2 ${viewMode === 'card' ? 'bg-blue-50 text-blue-600' : 'text-gray-600 hover:bg-gray-50'}`}
                  title="Card View"
                >
                  <RectangleStackIcon className="w-4 h-4" />
                </button>
              </div>
            )}

            {/* Density Controls */}
            {config.enableDensityControls && (
              <div className="relative">
                <button
                  onClick={() => setShowDensityMenu(!showDensityMenu)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
                  {displayDensity.charAt(0).toUpperCase() + displayDensity.slice(1)}
                  <ChevronDownIcon className="w-4 h-4 ml-2" />
                </button>
                {showDensityMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="py-1">
                      {['compact', 'comfortable', 'spacious'].map((option) => (
                        <button
                          key={option}
                          onClick={() => {
                            setDisplayDensity(option as any)
                            setShowDensityMenu(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                            displayDensity === option ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                          }`}
                        >
                          {option.charAt(0).toUpperCase() + option.slice(1)}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Column Visibility */}
            {config.enableColumnVisibility && viewMode === 'list' && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnMenu(!showColumnMenu)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Columns
                  <ChevronDownIcon className="w-4 h-4 ml-2" />
                </button>
                {showColumnMenu && (
                  <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                    <div className="p-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Show Columns</h4>
                      <div className="space-y-2">
                        {config.columns?.map((col) => (
                          <label key={col.key} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(col.key as string)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setVisibleColumns([...visibleColumns, col.key as string])
                                } else {
                                  setVisibleColumns(visibleColumns.filter(c => c !== col.key))
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">{col.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {/* Error Message */}
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md" role="alert">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}
      </div>

      {/* Add filter panel UI below controls */}
      {showFilters && (
        <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="flex flex-wrap gap-4">
            {(config.filters || []).map((filter) => (
              <div key={filter.key} className="flex flex-col min-w-[180px]">
                <label className="text-xs font-medium text-gray-700 mb-1">{filter.label}</label>
                {filter.type === 'select' ? (
                  <select
                    className="border border-gray-300 rounded-md px-2 py-1 text-sm"
                    value={filters[filter.key] || ''}
                    onChange={e => setFilters(f => ({ ...f, [filter.key]: e.target.value }))}
                  >
                    {(filter.options || []).map(opt => (
                      <option key={opt.value} value={opt.value}>{opt.label}</option>
                    ))}
                  </select>
                ) : (
                  <input
                    className="border border-gray-300 rounded-md px-2 py-1 text-sm"
                    value={filters[filter.key] || ''}
                    onChange={e => setFilters(f => ({ ...f, [filter.key]: e.target.value }))}
                    placeholder={filter.label}
                  />
                )}
              </div>
            ))}
            <button
              className="ml-auto px-3 py-2 text-sm rounded bg-gray-200 hover:bg-gray-300"
              onClick={() => setFilters(initialFilterState)}
              type="button"
            >
              Reset Filters
            </button>
          </div>
        </div>
      )}

      {/* Add bulk actions top bar below controls and filter panel */}
      {config.enableBulkActions && selectedMembers.length > 0 && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedMembers.length} team member{selectedMembers.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex items-center gap-2">
              <button
                onClick={() => handleBulkAction('activate', selectedMembers)}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
              >
                Activate
              </button>
              <button
                onClick={() => handleBulkAction('deactivate', selectedMembers)}
                className="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                Deactivate
              </button>
              <button
                onClick={() => handleBulkAction('delete', selectedMembers)}
                className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading team members...</p>
          </div>
        ) : viewMode === 'list' ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className={`px-6 ${densityHeaderPadding} text-left text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                    <input
                      type="checkbox"
                      checked={selectedMembers.length === teamMembers.length && teamMembers.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      aria-label="Select all team members"
                    />
                  </th>
                  {config.columns
                    .filter(col => visibleColumns.includes(col.key as string))
                    .map(column => (
                      <th
                        key={column.key}
                        className={`px-6 ${densityHeaderPadding} text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100`}
                        onClick={() => column.sortable && handleSort(column.key as string)}
                      >
                        <div className="flex items-center space-x-1">
                          <span>{column.label}</span>
                          {column.sortable && (
                            <div className="flex flex-col">
                              <ArrowUpIcon 
                                className={`h-3 w-3 ${
                                  sortBy === column.key && sortOrder === 'asc' 
                                    ? 'text-blue-600' 
                                    : 'text-gray-400'
                                }`} 
                              />
                              <ArrowDownIcon 
                                className={`h-3 w-3 ${
                                  sortBy === column.key && sortOrder === 'desc' 
                                    ? 'text-blue-600' 
                                    : 'text-gray-400'
                                }`} 
                              />
                            </div>
                          )}
                        </div>
                      </th>
                    ))}
                  <th className={`px-6 ${densityHeaderPadding} text-left text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {teamMembers.map((member) => (
                  <tr key={member.id} className="hover:bg-gray-50">
                    <td className={`px-6 ${densityPadding} whitespace-nowrap`}>
                      <input
                        type="checkbox"
                        checked={selectedMembers.includes(member.id)}
                        onChange={() => handleSelectMember(member.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        aria-label={`Select ${member.name}`}
                      />
                    </td>
                    {config.columns
                      .filter(col => visibleColumns.includes(col.key as string))
                      .map(column => (
                        <td key={column.key} className={`px-6 ${densityPadding} whitespace-nowrap text-sm text-gray-900`}>
                          {column.key === 'name' ? (
                            <div className="flex items-center">
                              <TeamMemberAvatar name={member.name} photoUrl={member.photoUrl} size="sm" className="mr-3" />
                              <div>{member.name}</div>
                            </div>
                          ) : column.renderType === 'date' 
                            ? formatDate(member[column.key as keyof typeof member])
                            : column.renderType === 'status'
                            ? (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                member[column.key as keyof typeof member] 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {member[column.key as keyof typeof member] ? 'Active' : 'Inactive'}
                              </span>
                            )
                            : member[column.key as keyof typeof member]
                          }
                        </td>
                      ))}
                    <td className={`px-6 ${densityPadding} whitespace-nowrap text-sm font-medium`}>
                      <div className="flex items-center space-x-2">
                        {config.actions?.map((action) => (
                          <button
                            key={action.action}
                            onClick={() => handleAction(action.action, member)}
                            disabled={actionLoading === member.id}
                            className={`p-1 rounded-md transition-colors ${
                              action.variant === 'danger'
                                ? 'text-red-600 hover:bg-red-100'
                                : action.variant === 'warning'
                                ? 'text-yellow-600 hover:bg-yellow-100'
                                : action.variant === 'primary'
                                ? 'text-blue-600 hover:bg-blue-100'
                                : 'text-gray-600 hover:bg-gray-100'
                            }`}
                            title={action.tooltip}
                            aria-label={action.tooltip}
                          >
                            <ActionIcon actionType={action.action} />
                          </button>
                        ))}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
            {teamMembers.map((member) => (
              <div key={member.id} className={`bg-white rounded-lg border-2 p-4 hover:shadow-lg transition-all duration-200 ${
                selectedMembers.includes(String(member.id)) ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'
              }`}>
                {/* Checkbox */}
                <div className="flex justify-end mb-2">
                  <input
                    type="checkbox"
                    checked={selectedMembers.includes(String(member.id))}
                    onChange={() => handleSelectMember(String(member.id))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </div>
                {/* Avatar */}
                <div className="flex justify-center mb-4">
                  <TeamMemberAvatar name={member.name} photoUrl={member.photoUrl} size="xl" />
                </div>
                {/* Info */}
                <div className="text-center space-y-2">
                  <h3 className="font-semibold text-gray-900 truncate">{member.name}</h3>
                  <p className="text-sm text-gray-600 line-clamp-2">{member.position}</p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    member.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {member.isActive ? 'Active' : 'Inactive'}
                  </span>
                  <div className="text-xs text-gray-500">Last Active: {formatDate(member.updatedAt)}</div>
                </div>
                {/* Actions */}
                <div className="flex justify-center space-x-2 mt-4 pt-4 border-t border-gray-100">
                  {config.actions?.map((action) => (
                    <button
                      key={action.action}
                      onClick={() => handleAction(action.action, member)}
                      disabled={actionLoading === member.id}
                      className={`p-2 rounded-md transition-colors ${
                        action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                        action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                        action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                        action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                        'text-gray-600 hover:bg-gray-50'
                      } ${actionLoading === member.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                      title={action.tooltip}
                    >
                      <ActionIcon actionType={action.action} />
                    </button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Card View
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-4 sm:p-6">
            {teamMembers.map((member) => (
              <div key={member.id} className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[320px] ${selectedMembers.includes(String(member.id)) ? 'bg-blue-50 border-blue-500' : 'bg-white border-gray-100 hover:border-blue-200'}`}>
                <div className="flex h-full">
                  {/* Avatar Section */}
                  <div className="flex-shrink-0 w-56 relative">
                    <TeamMemberAvatar name={member.name} photoUrl={member.photoUrl} size="full-height" className="shadow-none" style={{ width: '100%', height: '100%', minHeight: '320px' }} />
                    {/* Checkbox overlay */}
                    <div className="absolute top-4 left-4">
                      <input
                        type="checkbox"
                        checked={selectedMembers.includes(String(member.id))}
                        onChange={() => handleSelectMember(String(member.id))}
                        className="w-5 h-5 rounded border-2 border-white text-blue-600 focus:ring-blue-500 shadow-lg"
                      />
                    </div>
                  </div>
                  {/* Content Section */}
                  <div className="flex-1 p-6 flex flex-col justify-between">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                          <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                            member.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {member.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                          {member.position}
                        </p>
                      </div>
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Phone:</span>
                          <span className="text-gray-900 font-medium">{member.phone}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Email:</span>
                          <span className="text-gray-900 font-medium">{member.email}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Last Active:</span>
                          <span className="text-gray-900 font-medium">{formatDate(member.updatedAt)}</span>
                        </div>
                      </div>
                    </div>
                    {/* Actions */}
                    <div className="flex items-center justify-end space-x-2 pt-4 border-t border-gray-100">
                      {config.actions?.map((action) => (
                        <button
                          key={action.action}
                          onClick={() => handleAction(action.action, member)}
                          disabled={actionLoading === member.id}
                          className={`p-2 rounded-lg transition-colors ${
                            action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                            action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                            action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                            action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                            'text-gray-600 hover:bg-gray-50'
                          } ${actionLoading === member.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={action.tooltip}
                        >
                          <ActionIcon actionType={action.action} />
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <AnimatePresence>
        {isCreateModalOpen && (
          <TeamMemberModal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            onSubmit={handleCreate}
            title={config.title}
            fields={config.fields}
            layout={config.formLayout}
          />
        )}

        {isEditModalOpen && editingMember && (
          <TeamMemberModal
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false)
              setEditingMember(null)
            }}
            onSubmit={(formData) => handleUpdate(editingMember.id, formData)}
            title={config.title}
            fields={config.fields}
            layout={config.formLayout}
            initialData={editingMember}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
