'use client'

import React, { useState, useCallback, memo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  FolderIcon, 
  CogIcon, 
  ListBulletIcon, 
  StarIcon,
  ChevronRightIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { CategoryManagement } from './category-management'
import { ServiceManagement } from './service-management'
import { ServiceOptionsManagement } from './service-options-management'
import { OptionFeaturesManagement } from './option-features-management'

interface Category {
  id: string
  name: string
  description?: string
  parentId?: string
  isActive: boolean
  displayOrder: number
  children?: Category[]
  _count?: {
    services: number
    children: number
  }
}

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

type ActiveSection = 'categories' | 'services' | 'options' | 'features'

// Memoized section navigation component
const SectionNavigation = memo<{
  sections: readonly {
    readonly id: ActiveSection
    readonly title: string
    readonly description: string
    readonly color: string
    readonly isActive: boolean
    readonly disabled?: boolean
  }[]
  onSectionChange: (sectionId: ActiveSection) => void
}>(({ sections, onSectionChange }) => (
  <div className="overflow-x-auto">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 min-w-[640px] md:min-w-0">
      {sections.map((section) => (
        <motion.button
          key={section.id}
          onClick={() => onSectionChange(section.id)}
          disabled={section.disabled}
          className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
            section.isActive
              ? 'border-blue-500 bg-blue-50 shadow-md'
              : section.disabled
              ? 'border-gray-200 bg-gray-50 cursor-not-allowed opacity-50'
              : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
          }`}
          whileHover={!section.disabled ? { scale: 1.02 } : undefined}
          whileTap={!section.disabled ? { scale: 0.98 } : undefined}
          aria-label={`Navigate to ${section.title} section`}
          aria-describedby={`${section.id}-description`}
          aria-current={section.isActive ? 'page' : undefined}
        >
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${section.color} ${section.disabled ? 'opacity-50' : ''}`}>
              {section.id === 'categories' && <FolderIcon className="h-5 w-5 text-white" aria-hidden="true" />}
              {section.id === 'services' && <CogIcon className="h-5 w-5 text-white" aria-hidden="true" />}
              {section.id === 'options' && <ListBulletIcon className="h-5 w-5 text-white" aria-hidden="true" />}
              {section.id === 'features' && <StarIcon className="h-5 w-5 text-white" aria-hidden="true" />}
            </div>
            <div className="flex-1">
              <h3 className={`font-semibold ${
                section.isActive ? 'text-blue-900' : section.disabled ? 'text-gray-400' : 'text-gray-900'
              }`}>
                {section.title}
              </h3>
              <p 
                id={`${section.id}-description`}
                className={`text-sm ${
                  section.isActive ? 'text-blue-600' : section.disabled ? 'text-gray-400' : 'text-gray-500'
                }`}
              >
                {section.description}
              </p>
            </div>
          </div>
        </motion.button>
      ))}
    </div>
  </div>
))
SectionNavigation.displayName = 'SectionNavigation'

// Memoized breadcrumb component
const Breadcrumb = memo<{
  selectedCategory: Category | null
  selectedService: Service | null
  selectedOption: ServiceOption | null
}>(({ selectedCategory, selectedService, selectedOption }) => (
  <div className="mt-4 flex items-center space-x-2 text-sm" aria-label="Navigation breadcrumb">
    <span className="text-gray-500">Current Path:</span>
    <nav className="flex items-center space-x-1" aria-label="Breadcrumb">
      <span className="font-medium text-blue-600">Categories</span>
      {selectedCategory && selectedCategory.name && (
        <>
          <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
          <span className="font-medium text-green-600">{selectedCategory.name}</span>
        </>
      )}
      {selectedService && selectedService.name && (
        <>
          <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
          <span className="font-medium text-orange-600">{selectedService.name}</span>
        </>
      )}
      {selectedOption && selectedOption.name && (
        <>
          <ChevronRightIcon className="h-4 w-4 text-gray-400" aria-hidden="true" />
          <span className="font-medium text-purple-600">{selectedOption.name}</span>
        </>
      )}
    </nav>
  </div>
))
Breadcrumb.displayName = 'Breadcrumb'

// Memoized header component
const Header = memo<{
  selectedCategory: Category | null
  selectedService: Service | null
  selectedOption: ServiceOption | null
}>(({ selectedCategory, selectedService, selectedOption }) => (
  <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <div className="flex items-center justify-between">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Services Management System</h1>
        <p className="text-gray-600 mt-1">
          Manage your service hierarchy: Categories → Services → Options → Features
        </p>
      </div>
      <div className="flex items-center space-x-2 text-sm text-gray-500">
        <span>Hierarchical Management System</span>
      </div>
    </div>
    <Breadcrumb 
      selectedCategory={selectedCategory}
      selectedService={selectedService}
      selectedOption={selectedOption}
    />
  </div>
))
Header.displayName = 'Header'

export function ServicesManagement() {
  const [activeSection, setActiveSection] = useState<ActiveSection>('categories')
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedService, setSelectedService] = useState<Service | null>(null)
  const [selectedOption, setSelectedOption] = useState<ServiceOption | null>(null)

  const sections = [
    {
      id: 'categories' as const,
      title: 'Categories',
      description: 'Manage service categories and subcategories',
      color: 'bg-blue-500',
      isActive: activeSection === 'categories',
      disabled: false
    },
    {
      id: 'services' as const,
      title: 'Services',
      description: 'Manage services under categories',
      color: 'bg-green-500',
      isActive: activeSection === 'services',
      disabled: !selectedCategory
    },
    {
      id: 'options' as const,
      title: 'Service Options',
      description: 'Manage options for services',
      color: 'bg-orange-500',
      isActive: activeSection === 'options',
      disabled: !selectedService
    },
    {
      id: 'features' as const,
      title: 'Option Features',
      description: 'Manage features for service options',
      color: 'bg-purple-500',
      isActive: activeSection === 'features',
      disabled: !selectedOption
    }
  ] as const

  const handleSectionChange = useCallback((sectionId: ActiveSection) => {
    if (sections.find(s => s.id === sectionId)?.disabled) return
    setActiveSection(sectionId)
  }, [sections])

  const handleCategorySelect = useCallback((category: Category | null) => {
    setSelectedCategory(category)
    setSelectedService(null)
    setSelectedOption(null)
    if (category && activeSection === 'categories') {
      setActiveSection('services')
    }
  }, [activeSection])

  const handleServiceSelect = useCallback((service: Service | null) => {
    setSelectedService(service)
    setSelectedOption(null)
    if (service && activeSection === 'services') {
      setActiveSection('options')
    }
  }, [activeSection])

  const handleOptionSelect = useCallback((option: ServiceOption | null) => {
    setSelectedOption(option)
    if (option && activeSection === 'options') {
      setActiveSection('features')
    }
  }, [activeSection])

  return (
    <div className="space-y-6">
      <Header 
        selectedCategory={selectedCategory}
        selectedService={selectedService}
        selectedOption={selectedOption}
      />

      <SectionNavigation 
        sections={sections}
        onSectionChange={handleSectionChange}
      />

      {/* Content Area */}
      <div 
        className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-x-auto"
        role="main"
        aria-label={`${sections.find(s => s.isActive)?.title} management section`}
      >
        <AnimatePresence mode="wait">
          {activeSection === 'categories' && (
            <motion.div
              key="categories"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <CategoryManagement
                selectedCategory={selectedCategory}
                onCategorySelect={handleCategorySelect}
              />
            </motion.div>
          )}

          {activeSection === 'services' && selectedCategory && (
            <motion.div
              key="services"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ServiceManagement
                category={selectedCategory}
                selectedService={selectedService}
                onServiceSelect={handleServiceSelect}
              />
            </motion.div>
          )}

          {activeSection === 'options' && selectedService && (
            <motion.div
              key="options"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ServiceOptionsManagement
                service={selectedService}
                selectedOption={selectedOption}
                onOptionSelect={handleOptionSelect}
              />
            </motion.div>
          )}

          {activeSection === 'features' && selectedOption && (
            <motion.div
              key="features"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <OptionFeaturesManagement
                option={selectedOption}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
