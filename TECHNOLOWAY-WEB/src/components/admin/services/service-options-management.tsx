'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ListBulletIcon,
  CurrencyDollarIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import { safeToLocaleDateString } from '@/lib/utils/date-utils'

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  features?: OptionFeature[]
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  name: string
  cost?: number
  isIncluded: boolean
}

interface ServiceOptionsManagementProps {
  service: Service
  selectedOption: ServiceOption | null
  onOptionSelect: (option: ServiceOption | null) => void
}

interface OptionFormData {
  name: string
  description: string
  price: number
  discountRate: number
  totalDiscount: number
  isActive: boolean
}

export function ServiceOptionsManagement({ service, selectedOption, onOptionSelect }: ServiceOptionsManagementProps) {
  const [options, setOptions] = useState<ServiceOption[]>([])
  const [filteredOptions, setFilteredOptions] = useState<ServiceOption[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOption, setEditingOption] = useState<ServiceOption | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('list')
  const [displayDensity, setDisplayDensity] = useState<'compact' | 'comfortable'>('comfortable')
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filters, setFilters] = useState<Record<string, string>>({})

  const [formData, setFormData] = useState<OptionFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    totalDiscount: 0,
    isActive: true
  })

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    if (service && service.id) {
      fetchOptions()
    }
  }, [service.id])

  useEffect(() => {
    filterAndSortOptions()
  }, [options, debouncedSearchTerm, sortBy, sortOrder, filters])

  const filterAndSortOptions = () => {
    let filtered = [...options]

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase()
      filtered = filtered.filter(option =>
        option.name.toLowerCase().includes(searchLower) ||
        (option.description && option.description.toLowerCase().includes(searchLower))
      )
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '') {
        if (key === 'isActive') {
          filtered = filtered.filter(option =>
            option.isActive === (value === 'true')
          )
        }
      }
    })

    // Sort options
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof ServiceOption]
      let bValue: any = b[sortBy as keyof ServiceOption]

      if (sortBy === 'features') {
        aValue = a._count?.features || 0
        bValue = b._count?.features || 0
      } else if (sortBy === 'orders') {
        aValue = a._count?.orderDetails || 0
        bValue = b._count?.orderDetails || 0
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    setFilteredOptions(filtered)
  }

  const fetchOptions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/service-options?serviceId=${service.id}&limit=100`)
      if (response.ok) {
        const data = await response.json()
        setOptions(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching service options:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingOption
        ? `/api/admin/service-options/${editingOption.id}`
        : '/api/admin/service-options'

      const method = editingOption ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceId: service.id,
          name: formData.name,
          description: formData.description,
          price: formData.price,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          isActive: formData.isActive
        }),
      })

      if (response.ok) {
        await fetchOptions()
        setIsFormOpen(false)
        setEditingOption(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving service option:', error)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      totalDiscount: 0,
      isActive: true
    })
  }

  const handleEdit = (option: ServiceOption) => {
    setEditingOption(option)
    setFormData({
      name: option.name,
      description: option.description || '',
      price: option.price || 0,
      discountRate: option.discountRate || 0,
      totalDiscount: option.totalDiscount || 0,
      isActive: option.isActive
    })
    setIsFormOpen(true)
  }

  const handleDelete = async (option: ServiceOption) => {
    if (!confirm(`Are you sure you want to delete "${option.name}"?`)) return

    try {
      const response = await fetch(`/api/admin/service-options/${option.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchOptions()
        if (selectedOption?.id === option.id) {
          onOptionSelect(null)
        }
      } else {
        const errorData = await response.json()
        alert(errorData.message || 'Failed to delete service option')
      }
    } catch (error) {
      console.error('Error deleting service option:', error)
      alert('An error occurred while deleting the service option')
    }
  }

  const handleToggleActive = async (option: ServiceOption) => {
    try {
      const response = await fetch(`/api/admin/service-options/${option.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          serviceId: service.id,
          name: option.name,
          description: option.description,
          price: option.price,
          discountRate: option.discountRate,
          totalDiscount: option.totalDiscount,
          isActive: !option.isActive
        })
      })
      if (response.ok) {
        fetchOptions()
      }
    } catch (error) {
      console.error('Error toggling option status:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Options for "{service.name}"
          </h2>
          <p className="text-gray-600">Manage service options and their configurations</p>
        </div>
        <button
          onClick={() => {
            setEditingOption(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Option</span>
        </button>
      </div>

      {/* Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4 mb-6">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search options by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchTerm !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-orange-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-orange-50 border-orange-300 text-orange-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'cards')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-orange-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-orange-50 text-orange-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    value={filters.isActive || ''}
                    onChange={(e) => setFilters({ ...filters, isActive: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">All Options</option>
                    <option value="true">Active Only</option>
                    <option value="false">Inactive Only</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="name">Name</option>
                    <option value="price">Price</option>
                    <option value="features">Feature Count</option>
                    <option value="orders">Order Count</option>
                    <option value="updatedAt">Last Updated</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  {filteredOptions.length} of {options.length} options
                </div>
                <button
                  onClick={() => {
                    setFilters({})
                    setSearchTerm('')
                    setSortBy('name')
                    setSortOrder('asc')
                  }}
                  className="text-sm text-orange-600 hover:text-orange-800"
                >
                  Clear all filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Options Display */}
      {filteredOptions.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <ListBulletIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>
            {searchTerm || Object.values(filters).some(v => v)
              ? 'No options match your search criteria'
              : 'No options found for this service. Create your first option to get started.'}
          </p>
        </div>
      ) : (
        <>
          {viewMode === 'list' && (
            <div className="space-y-3">
              {filteredOptions.map(option => (
                <motion.div
                  key={option.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`border-2 rounded-lg transition-all ${
                    selectedOption?.id === option.id
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <div
                    className="p-4 cursor-pointer"
                    onClick={() => onOptionSelect(option)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="p-2 bg-orange-100 rounded-lg">
                          <ListBulletIcon className="h-5 w-5 text-orange-600" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className={`font-semibold ${
                                selectedOption?.id === option.id ? 'text-orange-900' : 'text-gray-900'
                              }`}>
                                {option.name}
                              </h3>
                              {option.description && (
                                <p className={`text-sm ${
                                  selectedOption?.id === option.id ? 'text-orange-700' : 'text-gray-600'
                                }`}>
                                  {option.description}
                                </p>
                              )}
                            </div>

                            <div className="flex items-center space-x-4">
                              {option.price && (
                                <div className="flex items-center space-x-1 text-sm text-gray-600">
                                  <CurrencyDollarIcon className="h-4 w-4" />
                                  <span>${Number(option.price)}</span>
                                </div>
                              )}

                              <span className={`px-2 py-1 text-xs rounded-full ${
                                option.isActive
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {option.isActive ? 'Active' : 'Inactive'}
                              </span>

                              {option._count && typeof option._count.features === 'number' && (
                                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                  {option._count.features} features
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1 ml-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEdit(option)
                          }}
                          className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                          title="Edit option"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>

                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleToggleActive(option)
                          }}
                          className={`p-1.5 rounded-md transition-colors focus:outline-none focus:ring-2 ${
                            option.isActive
                              ? 'text-green-600 hover:text-green-700 hover:bg-green-50 focus:ring-green-500'
                              : 'text-gray-400 hover:text-green-600 hover:bg-green-50 focus:ring-green-500'
                          }`}
                          title={option.isActive ? 'Deactivate option' : 'Activate option'}
                        >
                          {option.isActive ? (
                            <EyeIcon className="h-4 w-4" />
                          ) : (
                            <EyeSlashIcon className="h-4 w-4" />
                          )}
                        </button>

                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDelete(option)
                          }}
                          className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                          title="Delete option"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredOptions.map(option => (
                <motion.div
                  key={option.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className={`border-2 rounded-lg p-4 transition-all cursor-pointer ${
                    selectedOption?.id === option.id
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                  }`}
                  onClick={() => onOptionSelect(option)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <ListBulletIcon className="h-5 w-5 text-orange-600" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(option)
                        }}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        title="Edit option"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleToggleActive(option)
                        }}
                        className={`p-1.5 rounded-md transition-colors ${
                          option.isActive
                            ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                            : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                        }`}
                        title={option.isActive ? 'Deactivate option' : 'Activate option'}
                      >
                        {option.isActive ? (
                          <EyeIcon className="h-4 w-4" />
                        ) : (
                          <EyeSlashIcon className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(option)
                        }}
                        className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        title="Delete option"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <h3 className={`font-semibold mb-2 ${
                    selectedOption?.id === option.id ? 'text-orange-900' : 'text-gray-900'
                  }`}>
                    {option.name}
                  </h3>

                  {option.description && (
                    <p className={`text-sm mb-3 line-clamp-2 ${
                      selectedOption?.id === option.id ? 'text-orange-700' : 'text-gray-600'
                    }`}>
                      {option.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {option.price && (
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <CurrencyDollarIcon className="h-4 w-4" />
                          <span>${Number(option.price)}</span>
                        </div>
                      )}
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        option.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {option.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    {option._count && typeof option._count.features === 'number' && (
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {option._count.features} features
                      </span>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'cards' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredOptions.map(option => (
                <motion.div
                  key={option.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`border-2 rounded-xl p-6 transition-all cursor-pointer ${
                    selectedOption?.id === option.id
                      ? 'border-orange-500 bg-orange-50 shadow-lg'
                      : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-lg'
                  }`}
                  onClick={() => onOptionSelect(option)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-orange-100 rounded-xl">
                        <ListBulletIcon className="h-6 w-6 text-orange-600" />
                      </div>
                      <div>
                        <h3 className={`text-lg font-semibold ${
                          selectedOption?.id === option.id ? 'text-orange-900' : 'text-gray-900'
                        }`}>
                          {option.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          option.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {option.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleEdit(option)
                        }}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit option"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleToggleActive(option)
                        }}
                        className={`p-2 rounded-lg transition-colors ${
                          option.isActive
                            ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                            : 'text-gray-400 hover:text-green-600 hover:bg-green-50'
                        }`}
                        title={option.isActive ? 'Deactivate option' : 'Activate option'}
                      >
                        {option.isActive ? (
                          <EyeIcon className="h-5 w-5" />
                        ) : (
                          <EyeSlashIcon className="h-5 w-5" />
                        )}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(option)
                        }}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete option"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {option.description && (
                    <p className={`text-sm mb-4 ${
                      selectedOption?.id === option.id ? 'text-orange-700' : 'text-gray-600'
                    }`}>
                      {option.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4">
                      {option.price && (
                        <div className="flex items-center space-x-1 text-gray-600">
                          <CurrencyDollarIcon className="h-5 w-5" />
                          <span className="font-semibold">${Number(option.price)}</span>
                        </div>
                      )}
                      {option._count && typeof option._count.features === 'number' && (
                        <div className="flex items-center space-x-1 text-gray-600">
                          <StarIcon className="h-5 w-5" />
                          <span>{option._count.features} features</span>
                        </div>
                      )}
                    </div>
                    {option.discountRate && option.discountRate > 0 && (
                      <div className="text-sm text-green-600 font-medium">
                        {option.discountRate}% off
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-lg mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold mb-4">
                {editingOption ? 'Edit Service Option' : 'Add New Service Option'}
              </h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Option Name
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Price ($)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Discount Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: Number(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="flex items-center">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="text-sm font-medium text-gray-700">Active</span>
                  </label>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    {editingOption ? 'Update' : 'Create'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
