'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline'

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface OptionFeature {
  id: string
  optionId: string
  name: string
  description?: string
  cost?: number
  discountRate?: number
  totalDiscount?: number
  isIncluded: boolean
  createdAt: string
  updatedAt: string
  option?: {
    id: string
    name: string
    service?: {
      id: string
      name: string
      category?: {
        id: string
        name: string
      }
    }
  }
  _count?: {
    orderDetails: number
  }
}

interface OptionFeaturesManagementProps {
  option: ServiceOption
}

interface FeatureFormData {
  name: string
  description: string
  cost: number
  discountRate: number
  totalDiscount: number
  isIncluded: boolean
}

export function OptionFeaturesManagement({ option }: OptionFeaturesManagementProps) {
  const [features, setFeatures] = useState<OptionFeature[]>([])
  const [filteredFeatures, setFilteredFeatures] = useState<OptionFeature[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingFeature, setEditingFeature] = useState<OptionFeature | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'cards'>('list')
  const [displayDensity, setDisplayDensity] = useState<'compact' | 'comfortable'>('comfortable')
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filters, setFilters] = useState<Record<string, any>>({})

  const [formData, setFormData] = useState<FeatureFormData>({
    name: '',
    description: '',
    cost: 0,
    discountRate: 0,
    totalDiscount: 0,
    isIncluded: true
  })

  // View mode icons
  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    cards: RectangleStackIcon
  }

  // Density labels
  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable'
  }

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  // Fetch features
  const fetchFeatures = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        optionId: option.id,
        limit: '100',
        sortBy,
        sortOrder
      })

      if (debouncedSearchTerm) {
        params.append('search', debouncedSearchTerm)
      }

      const response = await fetch(`/api/admin/service-option-features?${params}`)
      if (response.ok) {
        const data = await response.json()
        setFeatures(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching features:', error)
    } finally {
      setLoading(false)
    }
  }

  // Initial fetch and refetch on dependencies
  useEffect(() => {
    fetchFeatures()
  }, [option.id, debouncedSearchTerm, sortBy, sortOrder])

  // Filter and sort features
  useEffect(() => {
    let filtered = [...features]

    // Apply filters
    if (filters.isIncluded !== undefined && filters.isIncluded !== '') {
      filtered = filtered.filter(feature =>
        feature.isIncluded === (filters.isIncluded === 'true')
      )
    }

    setFilteredFeatures(filtered)
  }, [features, filters])

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      cost: 0,
      discountRate: 0,
      totalDiscount: 0,
      isIncluded: true
    })
  }

  const handleEdit = (feature: OptionFeature) => {
    setEditingFeature(feature)
    setFormData({
      name: feature.name,
      description: feature.description || '',
      cost: feature.cost || 0,
      discountRate: feature.discountRate || 0,
      totalDiscount: feature.totalDiscount || 0,
      isIncluded: feature.isIncluded
    })
    setIsFormOpen(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      const url = editingFeature
        ? `/api/admin/service-option-features/${editingFeature.id}`
        : '/api/admin/service-option-features'

      const method = editingFeature ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          optionId: option.id,
          name: formData.name,
          description: formData.description,
          cost: formData.cost,
          discountRate: formData.discountRate,
          totalDiscount: formData.totalDiscount,
          isIncluded: formData.isIncluded
        }),
      })

      if (response.ok) {
        await fetchFeatures()
        setIsFormOpen(false)
        setEditingFeature(null)
        resetForm()
      }
    } catch (error) {
      console.error('Error saving feature:', error)
    }
  }

  const handleDelete = async (feature: OptionFeature) => {
    if (!confirm(`Are you sure you want to delete "${feature.name}"?`)) {
      return
    }

    try {
      const response = await fetch('/api/admin/service-option-features', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: [feature.id] })
      })
      if (response.ok) {
        fetchFeatures()
      }
    } catch (error) {
      console.error('Error deleting feature:', error)
    }
  }

  const handleToggleIncluded = async (feature: OptionFeature) => {
    try {
      const response = await fetch(`/api/admin/service-option-features/${feature.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          optionId: option.id,
          name: feature.name,
          description: feature.description,
          cost: feature.cost,
          discountRate: feature.discountRate,
          totalDiscount: feature.totalDiscount,
          isIncluded: !feature.isIncluded
        })
      })
      if (response.ok) {
        fetchFeatures()
      }
    } catch (error) {
      console.error('Error toggling feature inclusion:', error)
    }
  }

  // Safe date formatting
  const safeToLocaleDateString = (dateString: string | null | undefined): string => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Invalid Date'
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-200 rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            Features for "{option.name}"
          </h2>
          <p className="text-gray-600">Manage features and their configurations</p>
        </div>
        <button
          onClick={() => {
            setEditingFeature(null)
            resetForm()
            setIsFormOpen(true)
          }}
          className="flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4" />
          <span>Add Feature</span>
        </button>
      </div>

      {/* Search and Controls */}
      <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
        <div className="flex items-center justify-between mb-4">
          {/* Search Bar */}
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search features..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-orange-50 border-orange-300 text-orange-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'cards')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-orange-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-orange-50 text-orange-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Inclusion Status
                  </label>
                  <select
                    value={filters.isIncluded || ''}
                    onChange={(e) => setFilters({ ...filters, isIncluded: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">All Features</option>
                    <option value="true">Included Only</option>
                    <option value="false">Optional Only</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="name">Name</option>
                    <option value="cost">Cost</option>
                    <option value="createdAt">Created Date</option>
                    <option value="updatedAt">Last Updated</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sort Order
                  </label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value as 'asc' | 'desc')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="asc">Ascending</option>
                    <option value="desc">Descending</option>
                  </select>
                </div>
              </div>

              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                <div className="text-sm text-gray-500">
                  {filteredFeatures.length} of {features.length} features
                </div>
                <button
                  onClick={() => {
                    setFilters({})
                    setSearchTerm('')
                    setSortBy('name')
                    setSortOrder('asc')
                  }}
                  className="text-sm text-orange-600 hover:text-orange-800"
                >
                  Clear all filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Features Display */}
      {filteredFeatures.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <StarIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>
            {searchTerm || Object.values(filters).some(v => v)
              ? 'No features match your search criteria'
              : 'No features found for this option. Create your first feature to get started.'}
          </p>
        </div>
      ) : (
        <>
          {viewMode === 'list' && (
            <div className="space-y-3">
              {filteredFeatures.map(feature => (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border-2 rounded-lg bg-white hover:border-gray-300 transition-all"
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <StarIcon className="h-5 w-5 text-blue-600" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-semibold text-gray-900">
                                {feature.name}
                              </h3>
                              {feature.description && (
                                <p className="text-sm text-gray-600">
                                  {feature.description}
                                </p>
                              )}
                            </div>

                            <div className="flex items-center space-x-4">
                              {feature.cost && feature.cost > 0 && (
                                <div className="flex items-center space-x-1 text-sm text-gray-600">
                                  <CurrencyDollarIcon className="h-4 w-4" />
                                  <span>${Number(feature.cost)}</span>
                                </div>
                              )}

                              <span className={`px-2 py-1 text-xs rounded-full ${
                                feature.isIncluded
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {feature.isIncluded ? 'Included' : 'Optional'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1 ml-4">
                        <button
                          onClick={() => handleEdit(feature)}
                          className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                          title="Edit feature"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>

                        <button
                          onClick={() => handleToggleIncluded(feature)}
                          className={`p-1.5 rounded-md transition-colors focus:outline-none focus:ring-2 ${
                            feature.isIncluded
                              ? 'text-green-600 hover:text-green-700 hover:bg-green-50 focus:ring-green-500'
                              : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50 focus:ring-blue-500'
                          }`}
                          title={feature.isIncluded ? 'Make optional' : 'Include by default'}
                        >
                          {feature.isIncluded ? (
                            <CheckCircleIcon className="h-4 w-4" />
                          ) : (
                            <XCircleIcon className="h-4 w-4" />
                          )}
                        </button>

                        <button
                          onClick={() => handleDelete(feature)}
                          className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                          title="Delete feature"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredFeatures.map(feature => (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="border-2 rounded-lg p-4 bg-white hover:border-gray-300 hover:shadow-md transition-all"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <StarIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleEdit(feature)}
                        className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
                        title="Edit feature"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleIncluded(feature)}
                        className={`p-1.5 rounded-md transition-colors ${
                          feature.isIncluded
                            ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                            : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                        }`}
                        title={feature.isIncluded ? 'Make optional' : 'Include by default'}
                      >
                        {feature.isIncluded ? (
                          <CheckCircleIcon className="h-4 w-4" />
                        ) : (
                          <XCircleIcon className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDelete(feature)}
                        className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
                        title="Delete feature"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  <h3 className="font-semibold mb-2 text-gray-900">
                    {feature.name}
                  </h3>

                  {feature.description && (
                    <p className="text-sm mb-3 line-clamp-2 text-gray-600">
                      {feature.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {feature.cost && feature.cost > 0 && (
                        <div className="flex items-center space-x-1 text-sm text-gray-600">
                          <CurrencyDollarIcon className="h-4 w-4" />
                          <span>${Number(feature.cost)}</span>
                        </div>
                      )}
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        feature.isIncluded
                          ? 'bg-green-100 text-green-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {feature.isIncluded ? 'Included' : 'Optional'}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'cards' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredFeatures.map(feature => (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border-2 rounded-xl p-6 bg-white hover:border-gray-300 hover:shadow-lg transition-all"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-blue-100 rounded-xl">
                        <StarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {feature.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          feature.isIncluded
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {feature.isIncluded ? 'Included' : 'Optional'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => handleEdit(feature)}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit feature"
                      >
                        <PencilIcon className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleToggleIncluded(feature)}
                        className={`p-2 rounded-lg transition-colors ${
                          feature.isIncluded
                            ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                            : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
                        }`}
                        title={feature.isIncluded ? 'Make optional' : 'Include by default'}
                      >
                        {feature.isIncluded ? (
                          <CheckCircleIcon className="h-5 w-5" />
                        ) : (
                          <XCircleIcon className="h-5 w-5" />
                        )}
                      </button>
                      <button
                        onClick={() => handleDelete(feature)}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete feature"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {feature.description && (
                    <p className="text-sm mb-4 text-gray-600">
                      {feature.description}
                    </p>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4">
                      {feature.cost && feature.cost > 0 && (
                        <div className="flex items-center space-x-1 text-gray-600">
                          <CurrencyDollarIcon className="h-5 w-5" />
                          <span className="font-semibold">${Number(feature.cost)}</span>
                        </div>
                      )}
                    </div>
                    {feature.discountRate && feature.discountRate > 0 && (
                      <div className="text-sm text-green-600 font-medium">
                        {feature.discountRate}% off
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </>
      )}

      {/* Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {editingFeature ? 'Edit Feature' : 'Add New Feature'}
                  </h3>
                  <button
                    onClick={() => setIsFormOpen(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Feature Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Enter feature name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="Enter feature description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cost ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.cost}
                      onChange={(e) => setFormData({ ...formData, cost: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Rate (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Total Discount ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.totalDiscount}
                      onChange={(e) => setFormData({ ...formData, totalDiscount: parseFloat(e.target.value) || 0 })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder="0.00"
                    />
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isIncluded"
                      checked={formData.isIncluded}
                      onChange={(e) => setFormData({ ...formData, isIncluded: e.target.checked })}
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isIncluded" className="ml-2 block text-sm text-gray-700">
                      Include this feature by default
                    </label>
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={() => setIsFormOpen(false)}
                      className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                    >
                      {editingFeature ? 'Update Feature' : 'Create Feature'}
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
