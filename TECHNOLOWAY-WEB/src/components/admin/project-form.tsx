'use client'

import { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'
import ImageUpload from './image-upload'

interface Project {
  id?: string
  name: string
  slug?: string
  description: string
  excerpt?: string
  status: string
  manager?: string
  estimatedCost?: number
  startDate?: string
  completionDate?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
  isFeatured: boolean
  isPublic: boolean
  displayOrder: number
  orderId?: string
  clientId?: string
}

interface ProjectFormProps {
  project?: Project | null
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
}

const statuses = [
  { value: 'PLANNING', label: 'Planning' },
  { value: 'IN_PROGRESS', label: 'In Progress' },
  { value: 'COMPLETED', label: 'Completed' },
  { value: 'ON_HOLD', label: 'On Hold' },
  { value: 'CANCELLED', label: 'Cancelled' },
]

export default function ProjectForm({ project, isOpen, onClose, onSubmit, title }: ProjectFormProps) {
  const [formData, setFormData] = useState<Project>({
    name: '',
    description: '',
    excerpt: '',
    status: 'PLANNING',
    manager: '',
    estimatedCost: 0,
    startDate: '',
    completionDate: '',
    imageUrl: '',
    projectUrl: '',
    githubUrl: '',
    tags: '',
    isFeatured: false,
    isPublic: false,
    displayOrder: 0,
    orderId: '',
    clientId: '',
  })
  
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [orders, setOrders] = useState<Array<{ id: string; orderNumber: string }>>([])
  const [clients, setClients] = useState<Array<{ id: string; companyName: string }>>([])

  // Load form data when project changes
  useEffect(() => {
    if (project) {

      setFormData({
        ...project,
        estimatedCost: project.estimatedCost || 0,
        startDate: project.startDate ? (typeof project.startDate === 'string' ? project.startDate.split('T')[0] : new Date(project.startDate).toISOString().split('T')[0]) : '',
        completionDate: project.completionDate ? (typeof project.completionDate === 'string' ? project.completionDate.split('T')[0] : new Date(project.completionDate).toISOString().split('T')[0]) : '',
        tags: project.tags || '',
        orderId: project.orderId || '',
        clientId: project.clientId || '',
      })
    } else {
      setFormData({
        name: '',
        description: '',
        excerpt: '',
        status: 'PLANNING',
        manager: '',
        estimatedCost: 0,
        startDate: '',
        completionDate: '',
        imageUrl: '',
        projectUrl: '',
        githubUrl: '',
        tags: '',
        isFeatured: false,
        isPublic: false,
        displayOrder: 0,
        orderId: '',
        clientId: '',
      })
    }
  }, [project])

  // Fetch orders and clients for dropdowns
  useEffect(() => {
    if (isOpen) {
      fetchDropdownData()
    }
  }, [isOpen])

  const fetchDropdownData = async () => {
    try {
      // Fetch orders
      const ordersResponse = await fetch('/api/admin/orders?limit=100', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (ordersResponse.ok) {
        const ordersResult = await ordersResponse.json()
        if (ordersResult.success) {
          setOrders(ordersResult.data || [])
        } else {
          console.warn('Failed to fetch orders:', ordersResult.error)
        }
      } else {
        console.warn('Orders API error:', ordersResponse.status, ordersResponse.statusText)
      }

      // Fetch clients
      const clientsResponse = await fetch('/api/admin/clients?limit=100', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (clientsResponse.ok) {
        const clientsResult = await clientsResponse.json()
        if (clientsResult.success) {
          setClients(clientsResult.data || [])
        } else {
          console.warn('Failed to fetch clients:', clientsResult.error)
        }
      } else {
        console.warn('Clients API error:', clientsResponse.status, clientsResponse.statusText)
      }
    } catch (error) {
      console.error('Error fetching dropdown data:', error)
      // Set empty arrays as fallback
      setOrders([])
      setClients([])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // Clean up form data - convert empty strings to null for optional fields
      const cleanedData = { ...formData }

      console.log('Form submission - original formData:', formData)
      console.log('Form submission - project prop:', project)

      // Convert empty strings to null for optional fields (excluding orderId which is required)
      const optionalStringFields = ['slug', 'manager', 'excerpt', 'imageUrl', 'projectUrl', 'githubUrl', 'tags', 'clientId']
      optionalStringFields.forEach(field => {
        if (cleanedData[field] === '') {
          cleanedData[field] = null
        }
      })

      // Handle orderId separately - it's required for new projects
      // For existing projects, if orderId is not changed, don't include it in the update
      if (!cleanedData.orderId || cleanedData.orderId === '') {
        if (!project) {
          // Creating new project - orderId is required
          throw new Error('Order is required. Please select an order for this project.')
        } else {
          // Editing existing project - remove orderId from update data if empty
          delete cleanedData.orderId
        }
      }

      // Convert empty strings to null for optional date fields
      if (cleanedData.startDate === '') cleanedData.startDate = null
      if (cleanedData.completionDate === '') cleanedData.completionDate = null

      // Convert estimatedCost to null if 0 or empty
      if (cleanedData.estimatedCost === 0 || cleanedData.estimatedCost === '') {
        cleanedData.estimatedCost = null
      }

      console.log('Form submission - final cleanedData:', cleanedData)

      await onSubmit(cleanedData)
      onClose()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({ ...prev, [name]: checked }))
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }))
    } else {
      setFormData(prev => ({ ...prev, [name]: value }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900">Basic Information</h4>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Project Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">URL Slug</label>
                <input
                  type="text"
                  name="slug"
                  value={formData.slug || ''}
                  onChange={handleChange}
                  placeholder="Auto-generated from name if empty"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Status *</label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  {statuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Project Manager</label>
                <input
                  type="text"
                  name="manager"
                  value={formData.manager || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Estimated Cost</label>
                <input
                  type="number"
                  name="estimatedCost"
                  value={formData.estimatedCost || ''}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900">Additional Information</h4>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Order *</label>
                <select
                  name="orderId"
                  value={formData.orderId || ''}
                  onChange={handleChange}
                  required
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select an order</option>
                  {orders.length === 0 ? (
                    <option value="" disabled>No orders available</option>
                  ) : (
                    orders.map(order => (
                      <option key={order.id} value={order.id}>
                        {order.orderNumber}
                      </option>
                    ))
                  )}
                </select>
                {orders.length === 0 && (
                  <p className="mt-1 text-sm text-gray-500">
                    No orders found. You may need to create an order first or check your permissions.
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Client</label>
                <select
                  name="clientId"
                  value={formData.clientId || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a client</option>
                  {clients.length === 0 ? (
                    <option value="" disabled>No clients available</option>
                  ) : (
                    clients.map(client => (
                      <option key={client.id} value={client.id}>
                        {client.companyName}
                      </option>
                    ))
                  )}
                </select>
                {clients.length === 0 && (
                  <p className="mt-1 text-sm text-gray-500">
                    No clients found. You may need to create a client first or check your permissions.
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Start Date</label>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Completion Date</label>
                <input
                  type="date"
                  name="completionDate"
                  value={formData.completionDate || ''}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Display Order</label>
                <input
                  type="number"
                  name="displayOrder"
                  value={formData.displayOrder}
                  onChange={handleChange}
                  min="0"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Full-width fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Description *</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                required
                rows={4}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Short Excerpt</label>
              <textarea
                name="excerpt"
                value={formData.excerpt || ''}
                onChange={handleChange}
                rows={2}
                placeholder="Brief summary for cards and previews"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <ImageUpload
                  label="Project Image"
                  value={formData.imageUrl || ''}
                  onChange={(url) => setFormData(prev => ({ ...prev, imageUrl: url }))}
                  accept="image/*"
                  maxSize={10}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">Live Project URL</label>
                <input
                  type="url"
                  name="projectUrl"
                  value={formData.projectUrl || ''}
                  onChange={handleChange}
                  placeholder="https://project.com"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700">GitHub URL</label>
                <input
                  type="url"
                  name="githubUrl"
                  value={formData.githubUrl || ''}
                  onChange={handleChange}
                  placeholder="https://github.com/user/repo"
                  className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Tags</label>
              <input
                type="text"
                name="tags"
                value={formData.tags || ''}
                onChange={handleChange}
                placeholder="web, mobile, api (comma-separated)"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Checkboxes */}
            <div className="flex space-x-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="isPublic"
                  checked={formData.isPublic}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Public Project (show in portfolio)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="isFeatured"
                  checked={formData.isFeatured}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label className="ml-2 block text-sm text-gray-900">
                  Featured Project (show on homepage)
                </label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Saving...' : project ? 'Update Project' : 'Create Project'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
