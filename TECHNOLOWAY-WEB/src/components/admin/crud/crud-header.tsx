'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline'
import { useCrud } from './crud-context'
import { CrudConfig } from './types'
import { IconRenderer } from './icon-renderer'
import { CrudViewControls } from './crud-view-controls'

interface CrudHeaderProps<T> {
  config: CrudConfig<T>
}

export function CrudHeader<T>({ config }: CrudHeaderProps<T>) {
  const { state, actions } = useCrud<T>()
  const [showFilters, setShowFilters] = useState(false)
  const [localFilters, setLocalFilters] = useState(state.filters)

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    actions.setSearch(e.target.value)
  }

  const handleFilterChange = (filterName: string, value: any) => {
    setLocalFilters(prev => ({ ...prev, [filterName]: value }))
  }

  const applyFilters = () => {
    actions.setFilters(localFilters)
    setShowFilters(false)
  }

  const clearFilters = () => {
    setLocalFilters({})
    actions.setFilters({})
    setShowFilters(false)
  }

  const handleBulkDelete = async () => {
    if (state.selectedItems.length === 0) return
    
    const confirmed = window.confirm(
      `Are you sure you want to delete ${state.selectedItems.length} item(s)? This action cannot be undone.`
    )
    
    if (confirmed) {
      const ids = state.selectedItems.map(item => (item as any).id)
      try {
        await actions.bulkDelete(ids)
        actions.clearSelection()
      } catch (error) {
        console.error('Bulk delete failed:', error)
      }
    }
  }

  const activeFiltersCount = Object.values(state.filters).filter(value => 
    value !== null && value !== undefined && value !== ''
  ).length

  return (
    <div className="bg-white rounded-lg shadow mb-6">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
            {config.description && (
              <p className="text-gray-600 mt-1">{config.description}</p>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            {config.enableExport && (
              <button
                onClick={actions.exportData}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
                <span>Export</span>
              </button>
            )}
            
            {config.customCreateButton || (
              <button
                onClick={actions.openCreateModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <PlusIcon className="h-4 w-4" />
                <span>Create {config.title.slice(0, -1)}</span>
              </button>
            )}
          </div>
        </div>

        {/* Search, Filters, and View Controls */}
        <div className="flex items-center justify-between space-x-4 mb-4">
          <div className="flex items-center space-x-4 flex-1">
            {config.enableSearch && (
              <div className="flex-1 relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={state.search}
                  onChange={handleSearchChange}
                  placeholder={config.searchPlaceholder || `Search ${config.title.toLowerCase()}...`}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            )}

            {config.enableFilters && config.filters && config.filters.length > 0 && (
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 border rounded-lg transition-colors flex items-center space-x-2 ${
                  showFilters || activeFiltersCount > 0
                    ? 'bg-blue-50 border-blue-300 text-blue-700'
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                <FunnelIcon className="h-4 w-4" />
                <span>Filters</span>
                {activeFiltersCount > 0 && (
                  <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1">
                    {activeFiltersCount}
                  </span>
                )}
              </button>
            )}
          </div>

          {/* View Controls */}
          <CrudViewControls config={config} />
        </div>

        {/* Bulk Actions */}
        {config.enableBulkActions && state.selectedItems.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {state.selectedItems.length} item(s) selected
                </span>
                <button
                  onClick={actions.clearSelection}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Clear selection
                </button>
              </div>
              
              <div className="flex items-center space-x-2">
                {config.bulkActions?.map((action, index) => (
                  <button
                    key={index}
                    onClick={() => (actions as any).bulkAction(action.action, state.selectedItems)}
                    className={`px-3 py-1 rounded text-sm transition-colors flex items-center space-x-1 ${
                      action.variant === 'danger'
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : action.variant === 'success'
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : action.variant === 'warning'
                        ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {action.icon && <IconRenderer name={action.icon} className="h-4 w-4" />}
                    <span>{action.label}</span>
                  </button>
                ))}

                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 transition-colors flex items-center space-x-1"
                >
                  <TrashIcon className="h-4 w-4" />
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Filter Panel */}
        {showFilters && config.filters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border border-gray-200 rounded-lg p-4 mb-4"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Filters</h3>
              <button
                onClick={() => setShowFilters(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
              {config.filters.map((filter) => (
                <div key={filter.name}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {filter.label}
                  </label>
                  
                  {filter.type === 'select' && (
                    <select
                      value={localFilters[filter.name] || ''}
                      onChange={(e) => handleFilterChange(filter.name, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All</option>
                      {filter.options?.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  )}
                  
                  {filter.type === 'date-range' && (
                    <div className="flex space-x-2">
                      <input
                        type="date"
                        value={localFilters[`${filter.name}_from`] || ''}
                        onChange={(e) => handleFilterChange(`${filter.name}_from`, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="From"
                      />
                      <input
                        type="date"
                        value={localFilters[`${filter.name}_to`] || ''}
                        onChange={(e) => handleFilterChange(`${filter.name}_to`, e.target.value)}
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="To"
                      />
                    </div>
                  )}
                  
                  {filter.type === 'checkbox' && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={localFilters[filter.name] || false}
                        onChange={(e) => handleFilterChange(filter.name, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-700">{filter.label}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
            
            <div className="flex items-center justify-end space-x-3">
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Clear All
              </button>
              <button
                onClick={applyFilters}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Apply Filters
              </button>
            </div>
          </motion.div>
        )}

        {config.customHeader}
      </div>
    </div>
  )
}
