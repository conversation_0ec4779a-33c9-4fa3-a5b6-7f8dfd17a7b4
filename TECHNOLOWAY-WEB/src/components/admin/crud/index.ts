// Main CRUD components
export { CrudManager } from './crud-manager-new'
export { CrudProvider, useCrud } from './crud-context'
export { CrudTable } from './crud-table'
export { CrudForm } from './crud-form'
export { CrudFormModal } from './crud-form-modal'
export { CrudHeader } from './crud-header'
export { CrudPagination } from './crud-pagination'
export { CrudDataView } from './crud-data-view'
export { CrudViewControls } from './crud-view-controls'

// Types
export type {
  CrudItem,
  CrudColumn,
  CrudField,
  CrudAction,
  CrudBulkAction,
  CrudFilter,
  CrudPermissions,
  CrudConfig,
  CrudState,
  CrudContextValue,
  UserRole,
  RolePermissions,
  ViewMode,
  DisplayDensity,
  ViewSettings,
} from './types'

export { DEFAULT_ROLE_PERMISSIONS } from './types'

// UI components
export { LoadingSpinner } from '../ui/loading-spinner'
export { EmptyState } from '../ui/empty-state'
export { ConfirmationModal } from '../ui/confirmation-modal'
export { NotificationProvider, useNotification } from '../ui/notification'

// Renderers
export { CellRenderer } from './cell-renderer'
export { IconRenderer } from './icon-renderer'
