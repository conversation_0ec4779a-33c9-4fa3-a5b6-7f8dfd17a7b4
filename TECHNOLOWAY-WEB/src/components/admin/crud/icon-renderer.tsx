'use client'

import React from 'react'
import {
  PencilIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,
  PlusIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  ArrowDownTrayIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  EllipsisVerticalIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  BriefcaseIcon,
  DocumentTextIcon,
  CogIcon,
  ClockIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/outline'

interface IconRendererProps {
  name: string
  className?: string
}

const iconMap = {
  // Actions
  'pencil': PencilIcon,
  'edit': PencilIcon,
  'trash': TrashIcon,
  'delete': TrashIcon,
  'eye': EyeIcon,
  'view': EyeIcon,
  'star': StarIcon,
  'plus': PlusIcon,
  'add': PlusIcon,
  'x-mark': XMarkIcon,
  'close': XMarkIcon,
  'check': CheckIcon,
  'exclamation-triangle': ExclamationTriangleIcon,
  'warning': ExclamationTriangleIcon,
  
  // UI
  'arrow-down-tray': ArrowDownTrayIcon,
  'download': ArrowDownTrayIcon,
  'export': ArrowDownTrayIcon,
  'funnel': FunnelIcon,
  'filter': FunnelIcon,
  'magnifying-glass': MagnifyingGlassIcon,
  'search': MagnifyingGlassIcon,
  'chevron-left': ChevronLeftIcon,
  'chevron-right': ChevronRightIcon,
  'chevron-up': ChevronUpIcon,
  'chevron-down': ChevronDownIcon,
  'ellipsis-vertical': EllipsisVerticalIcon,
  'more': EllipsisVerticalIcon,
  
  // Business
  'building-office': BuildingOfficeIcon,
  'company': BuildingOfficeIcon,
  'user-group': UserGroupIcon,
  'users': UserGroupIcon,
  'briefcase': BriefcaseIcon,
  'project': BriefcaseIcon,
  'document-text': DocumentTextIcon,
  'document': DocumentTextIcon,
  'cog': CogIcon,
  'settings': CogIcon,
  'clock': ClockIcon,
  'time': ClockIcon,
  'envelope': EnvelopeIcon,
  'email': EnvelopeIcon,
  'phone': PhoneIcon,
  'map-pin': MapPinIcon,
  'location': MapPinIcon,
  'currency-dollar': CurrencyDollarIcon,
  'money': CurrencyDollarIcon,
}

export function IconRenderer({ name, className = "h-4 w-4" }: IconRendererProps) {
  const IconComponent = iconMap[name as keyof typeof iconMap]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in iconMap`)
    return <div className={`${className} bg-gray-300 rounded`} />
  }
  
  return <IconComponent className={className} />
}
