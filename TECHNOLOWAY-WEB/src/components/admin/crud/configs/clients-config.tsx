import { CrudConfig } from '../types'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  industry: string
  companySize: string
  address?: string
  city?: string
  state?: string
  country?: string
  zipCode?: string
  isActive: boolean
  notes?: string
  createdAt: string
  updatedAt: string
}

export const clientsConfig: CrudConfig<Client> = {
  title: 'Clients',
  description: 'Manage client relationships and contact information',
  endpoint: 'clients',
  
  columns: [
    {
      key: 'companyName',
      label: 'Company',
      sortable: true,
      searchable: true,
      renderType: 'company'
    },
    {
      key: 'contactName',
      label: 'Contact Person',
      sortable: true,
      searchable: true,
    },
    {
      key: 'contactEmail',
      label: 'Email',
      sortable: true,
      searchable: true,
      renderType: 'email'
    },
    {
      key: 'contactPhone',
      label: 'Phone'
    },
    {
      key: 'companySize',
      label: 'Size',
      sortable: true,
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      }
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      renderType: 'date'
    }
  ],

  fields: [
    {
      name: 'companyName',
      label: 'Company Name',
      type: 'text',
      required: true,
      placeholder: 'Enter company name',
      validation: {
        min: 2,
        max: 100
      }
    },
    {
      name: 'contactName',
      label: 'Contact Person',
      type: 'text',
      required: true,
      placeholder: 'Primary contact name',
      validation: {
        min: 2,
        max: 100
      }
    },
    {
      name: 'contactEmail',
      label: 'Contact Email',
      type: 'email',
      required: true,
      placeholder: '<EMAIL>'
    },
    {
      name: 'contactPhone',
      label: 'Contact Phone',
      type: 'text',
      placeholder: '+****************'
    },
    {
      name: 'website',
      label: 'Website',
      type: 'text',
      placeholder: 'https://company.com'
    },
    {
      name: 'industry',
      label: 'Industry',
      type: 'select',
      required: true,
      options: [
        { value: 'Technology', label: 'Technology' },
        { value: 'Healthcare', label: 'Healthcare' },
        { value: 'Finance', label: 'Finance' },
        { value: 'Education', label: 'Education' },
        { value: 'Retail', label: 'Retail' },
        { value: 'Manufacturing', label: 'Manufacturing' },
        { value: 'Real Estate', label: 'Real Estate' },
        { value: 'Consulting', label: 'Consulting' },
        { value: 'Other', label: 'Other' }
      ]
    },
    {
      name: 'companySize',
      label: 'Company Size',
      type: 'select',
      required: true,
      options: [
        { value: '1-10', label: '1-10 employees' },
        { value: '11-50', label: '11-50 employees' },
        { value: '51-200', label: '51-200 employees' },
        { value: '201-500', label: '201-500 employees' },
        { value: '501-1000', label: '501-1000 employees' },
        { value: '1000+', label: '1000+ employees' }
      ]
    },
    {
      name: 'address',
      label: 'Address',
      type: 'text',
      placeholder: 'Street address'
    },
    {
      name: 'city',
      label: 'City',
      type: 'text',
      placeholder: 'City'
    },
    {
      name: 'state',
      label: 'State/Province',
      type: 'text',
      placeholder: 'State or Province'
    },
    {
      name: 'country',
      label: 'Country',
      type: 'text',
      placeholder: 'Country',
      defaultValue: 'United States'
    },
    {
      name: 'zipCode',
      label: 'ZIP/Postal Code',
      type: 'text',
      placeholder: 'ZIP or Postal Code'
    },
    {
      name: 'isActive',
      label: 'Active Client',
      type: 'checkbox',
      defaultValue: true,
      description: 'Active clients appear in project assignments'
    },
    {
      name: 'notes',
      label: 'Notes',
      type: 'textarea',
      placeholder: 'Additional notes about the client...'
    }
  ],

  actions: [
    {
      label: 'View',
      icon: 'eye',
      action: 'view',
      variant: 'secondary'
    },
    {
      label: 'Edit',
      icon: 'edit',
      action: 'edit',
      variant: 'primary'
    },
    {
      label: 'Delete',
      icon: 'delete',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true
    }
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  filters: [
    {
      name: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    },
    {
      name: 'industry',
      label: 'Industry',
      type: 'select',
      options: [
        { value: 'Technology', label: 'Technology' },
        { value: 'Healthcare', label: 'Healthcare' },
        { value: 'Finance', label: 'Finance' },
        { value: 'Education', label: 'Education' },
        { value: 'Retail', label: 'Retail' },
        { value: 'Manufacturing', label: 'Manufacturing' },
        { value: 'Real Estate', label: 'Real Estate' },
        { value: 'Consulting', label: 'Consulting' },
        { value: 'Other', label: 'Other' }
      ]
    },
    {
      name: 'companySize',
      label: 'Company Size',
      type: 'select',
      options: [
        { value: '1-10', label: '1-10 employees' },
        { value: '11-50', label: '11-50 employees' },
        { value: '51-200', label: '51-200 employees' },
        { value: '201-500', label: '201-500 employees' },
        { value: '501-1000', label: '501-1000 employees' },
        { value: '1000+', label: '1000+ employees' }
      ]
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search clients by company name, contact, or email...',
  defaultSort: { field: 'companyName', order: 'asc' },
  pageSize: 15,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
