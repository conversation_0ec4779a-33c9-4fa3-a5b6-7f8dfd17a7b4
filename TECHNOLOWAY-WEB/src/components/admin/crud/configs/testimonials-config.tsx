import { CrudConfig } from '../types'

interface Testimonial {
  id: string
  clientName: string
  clientTitle: string
  clientCompany: string
  clientPhotoUrl?: string
  content: string
  rating: number
  isFeatured: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
}

export const testimonialsConfig: CrudConfig<Testimonial> = {
  title: 'Testimonials',
  description: 'Manage customer testimonials and reviews',
  endpoint: 'testimonials',
  
  columns: [
    {
      key: 'clientName',
      label: 'Client',
      sortable: true,
      searchable: true,
      renderType: 'client'
    },
    {
      key: 'clientCompany',
      label: 'Company',
      sortable: true,
      searchable: true,
    },
    {
      key: 'rating',
      label: 'Rating',
      sortable: true,
      renderType: 'rating'
    },
    {
      key: 'content',
      label: 'Testimonial',
      renderType: 'truncated',
      renderProps: { maxLength: 50, quote: true }
    },
    {
      key: 'isFeatured',
      label: 'Featured',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Featured',
        falseLabel: 'Regular',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-gray-100 text-gray-800'
        }
      }
    },
    {
      key: 'displayOrder',
      label: 'Order',
      sortable: true,
      width: '80px'
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      renderType: 'date'
    }
  ],

  fields: [
    {
      name: 'clientName',
      label: 'Client Name',
      type: 'text',
      required: true,
      placeholder: 'Enter client name',
      validation: {
        min: 2,
        max: 100
      }
    },
    {
      name: 'clientTitle',
      label: 'Client Title',
      type: 'text',
      required: true,
      placeholder: 'e.g., CEO, Marketing Director',
      validation: {
        min: 2,
        max: 100
      }
    },
    {
      name: 'clientCompany',
      label: 'Company',
      type: 'text',
      required: true,
      placeholder: 'Company name',
      validation: {
        min: 2,
        max: 100
      }
    },
    {
      name: 'clientPhotoUrl',
      label: 'Client Photo URL',
      type: 'text',
      placeholder: 'https://example.com/photo.jpg',
      description: 'Optional URL to client photo'
    },
    {
      name: 'content',
      label: 'Testimonial Content',
      type: 'textarea',
      required: true,
      placeholder: 'Enter the testimonial content...',
      validation: {
        min: 10,
        max: 1000
      }
    },
    {
      name: 'rating',
      label: 'Rating',
      type: 'select',
      required: true,
      defaultValue: 5,
      options: [
        { value: 1, label: '1 Star' },
        { value: 2, label: '2 Stars' },
        { value: 3, label: '3 Stars' },
        { value: 4, label: '4 Stars' },
        { value: 5, label: '5 Stars' }
      ]
    },
    {
      name: 'isFeatured',
      label: 'Featured Testimonial',
      type: 'checkbox',
      defaultValue: false,
      description: 'Featured testimonials appear prominently on the website'
    },
    {
      name: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      description: 'Lower numbers appear first'
    }
  ],

  actions: [
    {
      label: 'View',
      icon: 'eye',
      action: 'view',
      variant: 'secondary'
    },
    {
      label: 'Edit',
      icon: 'edit',
      action: 'edit',
      variant: 'primary'
    },
    {
      label: 'Delete',
      icon: 'delete',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to delete this testimonial?'
    }
  ],

  bulkActions: [
    {
      label: 'Feature Selected',
      action: 'feature',
      variant: 'success'
    },
    {
      label: 'Unfeature Selected',
      action: 'unfeature',
      variant: 'secondary'
    }
  ],

  filters: [
    {
      name: 'isFeatured',
      label: 'Featured Status',
      type: 'select',
      options: [
        { value: 'true', label: 'Featured' },
        { value: 'false', label: 'Not Featured' }
      ]
    },
    {
      name: 'rating',
      label: 'Minimum Rating',
      type: 'select',
      options: [
        { value: '1', label: '1+ Stars' },
        { value: '2', label: '2+ Stars' },
        { value: '3', label: '3+ Stars' },
        { value: '4', label: '4+ Stars' },
        { value: '5', label: '5 Stars' }
      ]
    },
    {
      name: 'createdAt',
      label: 'Created Date',
      type: 'date-range'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search testimonials by client name, company, or content...',
  defaultSort: { field: 'displayOrder', order: 'asc' },
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
