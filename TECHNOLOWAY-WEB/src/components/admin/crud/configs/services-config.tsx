import { CrudConfig } from '../types'

interface Service {
  id: string
  categoryId: string
  name: string
  slug?: string
  description: string
  excerpt?: string
  iconClass?: string
  logoUrl?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  duration?: string
  complexity?: string
  features?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category: {
    id: string
    name: string
    description?: string
  }
  serviceOptions: Array<{
    id: string
    name: string
    price?: number
  }>
  _count: {
    projects: number
    orderDetails: number
    serviceOptions: number
  }
}

export const servicesConfig: CrudConfig<Service> = {
  title: 'Services',
  description: 'Manage your service offerings, pricing, and features',
  endpoint: 'services',
  
  columns: [
    {
      key: 'name',
      label: 'Service Name',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '200px'
    },
    {
      key: 'category.name',
      label: 'Category',
      sortable: true,
      renderType: 'text'
    },
    {
      key: 'price',
      label: 'Price',
      sortable: true,
      renderType: 'currency'
    },
    {
      key: 'manager',
      label: 'Manager',
      sortable: true,
      searchable: true,
      renderType: 'text'
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      }
    },
    {
      key: 'displayOrder',
      label: 'Order',
      sortable: true,
      renderType: 'text',
      width: '80px'
    },
    {
      key: '_count.projects',
      label: 'Projects',
      renderType: 'text',
      width: '80px'
    },
    {
      key: 'updatedAt',
      label: 'Updated',
      sortable: true,
      renderType: 'date'
    }
  ],

  fields: [
    {
      name: 'name',
      label: 'Service Name',
      type: 'text',
      required: true,
      placeholder: 'Enter service name',
      validation: {
        minLength: 1,
        maxLength: 255
      }
    },
    {
      name: 'slug',
      label: 'URL Slug',
      type: 'text',
      placeholder: 'auto-generated-from-name',
      validation: {
        maxLength: 255
      },
      helpText: 'Leave empty to auto-generate from service name'
    },
    {
      name: 'categoryId',
      label: 'Category',
      type: 'select',
      required: true,
      options: [], // Will be populated dynamically
      placeholder: 'Select a category'
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
      placeholder: 'Enter detailed service description',
      rows: 4,
      validation: {
        minLength: 10
      }
    },
    {
      name: 'excerpt',
      label: 'Short Description',
      type: 'textarea',
      placeholder: 'Enter a brief summary (used in cards and previews)',
      rows: 2,
      validation: {
        maxLength: 500
      }
    },
    {
      name: 'logoUrl',
      label: 'Service Logo',
      type: 'file',
      accept: 'image/*',
      uploadEndpoint: '/api/upload/service-logo',
      placeholder: 'Upload service logo image',
      helpText: 'Recommended: 200x200px, PNG or SVG format'
    },
    {
      name: 'iconClass',
      label: 'Icon Class',
      type: 'text',
      placeholder: 'e.g., CodeBracketIcon, CogIcon',
      validation: {
        maxLength: 100
      },
      helpText: 'Heroicons class name for fallback icon'
    },
    {
      name: 'price',
      label: 'Starting Price',
      type: 'number',
      required: true,
      placeholder: '0.00',
      validation: {
        min: 0,
        step: 0.01
      }
    },
    {
      name: 'discountRate',
      label: 'Discount Rate (%)',
      type: 'number',
      placeholder: '0',
      validation: {
        min: 0,
        max: 100
      }
    },
    {
      name: 'totalDiscount',
      label: 'Total Discount Amount',
      type: 'number',
      placeholder: '0.00',
      validation: {
        min: 0,
        step: 0.01
      }
    },
    {
      name: 'manager',
      label: 'Service Manager',
      type: 'text',
      placeholder: 'Enter manager name',
      validation: {
        maxLength: 50
      }
    },
    {
      name: 'duration',
      label: 'Estimated Duration',
      type: 'text',
      placeholder: 'e.g., 2-4 weeks, 1-3 months',
      validation: {
        maxLength: 50
      }
    },
    {
      name: 'complexity',
      label: 'Complexity Level',
      type: 'select',
      options: [
        { value: '', label: 'Select complexity' },
        { value: 'Simple', label: 'Simple' },
        { value: 'Medium', label: 'Medium' },
        { value: 'Complex', label: 'Complex' }
      ],
      placeholder: 'Select complexity level'
    },
    {
      name: 'features',
      label: 'Service Features',
      type: 'textarea',
      placeholder: 'Enter features separated by new lines or as JSON array',
      rows: 3,
      helpText: 'One feature per line or JSON format: ["Feature 1", "Feature 2"]'
    },
    {
      name: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      validation: {
        min: 0
      }
    },
    {
      name: 'isActive',
      label: 'Active',
      type: 'checkbox',
      defaultValue: true
    }
  ],

  filters: [
    {
      name: 'category',
      label: 'Category',
      type: 'select',
      options: [] // Will be populated dynamically
    },
    {
      name: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    },
    {
      name: 'priceRange',
      label: 'Price Range',
      type: 'select',
      options: [
        { value: '', label: 'All Prices' },
        { value: '0-1000', label: '$0 - $1,000' },
        { value: '1000-5000', label: '$1,000 - $5,000' },
        { value: '5000-10000', label: '$5,000 - $10,000' },
        { value: '10000+', label: '$10,000+' }
      ]
    }
  ],

  actions: [
    {
      label: 'Preview',
      icon: 'EyeIcon',
      action: 'preview',
      variant: 'secondary',
      tooltip: 'Preview service on public site'
    },
    {
      label: 'Edit',
      icon: 'PencilIcon',
      action: 'edit',
      variant: 'primary',
      tooltip: 'Edit service details'
    },
    {
      label: 'Toggle Status',
      icon: 'PowerIcon',
      action: 'toggle-status',
      variant: 'warning',
      tooltip: 'Activate/Deactivate service'
    },
    {
      label: 'Delete',
      icon: 'TrashIcon',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to delete this service? This action cannot be undone.',
      tooltip: 'Delete service permanently'
    }
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search services by name, description, or manager...',
  defaultSort: { field: 'displayOrder', direction: 'asc' },
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
