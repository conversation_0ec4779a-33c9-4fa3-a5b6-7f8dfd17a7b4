# Comprehensive CRUD System

A powerful, type-safe, and feature-rich CRUD (Create, Read, Update, Delete) system built with Next.js, TypeScript, and TailwindCSS. This system provides a complete solution for managing data with advanced features like role-based access control, real-time search, filtering, pagination, and bulk operations.

## 🚀 Features

### Core CRUD Operations
- **Create**: Modal-based forms with validation
- **Read**: Advanced table view with sorting and pagination
- **Update**: Pre-populated edit forms
- **Delete**: Confirmation dialogs with bulk delete support

### Advanced Features
- **Role-based Access Control**: Granular permissions per operation
- **Real-time Search**: Search across multiple fields simultaneously
- **Smart Filtering**: Multiple filter types (select, date-range, checkbox)
- **Bulk Operations**: Select and perform actions on multiple items
- **Data Export**: Export filtered data to CSV
- **Responsive Design**: Works seamlessly on all device sizes
- **Form Validation**: Client-side and server-side validation
- **Loading States**: Comprehensive loading and error handling
- **Animations**: Smooth transitions with Framer Motion

## 📁 File Structure

```
src/components/admin/crud/
├── index.ts                    # Main exports
├── types.ts                    # TypeScript interfaces
├── crud-context.tsx           # React context for state management
├── crud-manager.tsx           # Main CRUD component
├── crud-table.tsx             # Data table component
├── crud-form.tsx              # Form component for create/edit
├── crud-header.tsx            # Header with search and filters
├── crud-pagination.tsx        # Pagination component
├── configs/                   # Configuration files
│   ├── testimonials-config.tsx
│   ├── clients-config.tsx
│   └── projects-config.tsx
└── ui/                        # UI utility components
    ├── loading-spinner.tsx
    ├── empty-state.tsx
    └── confirmation-modal.tsx
```

## 🛠️ Usage

### Basic Implementation

```tsx
import { CrudManager } from '@/components/admin/crud'
import { testimonialsConfig } from '@/components/admin/crud/configs/testimonials-config'

export default function TestimonialsPage() {
  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <CrudManager config={testimonialsConfig} />
      </div>
    </div>
  )
}
```

### Creating a Configuration

```tsx
import { CrudConfig } from '@/components/admin/crud/types'

interface MyEntity {
  id: string
  name: string
  email: string
  status: 'active' | 'inactive'
  createdAt: string
}

export const myEntityConfig: CrudConfig<MyEntity> = {
  title: 'My Entities',
  description: 'Manage your entities',
  endpoint: 'my-entities',
  
  columns: [
    {
      key: 'name',
      label: 'Name',
      sortable: true,
      searchable: true,
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      searchable: true,
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          value === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value}
        </span>
      )
    }
  ],

  fields: [
    {
      name: 'name',
      label: 'Name',
      type: 'text',
      required: true,
      validation: { min: 2, max: 100 }
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' }
      ]
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true
}
```

## 🔐 Role-based Access Control

The system includes comprehensive role-based access control:

```tsx
// Default permissions by role
const DEFAULT_ROLE_PERMISSIONS = {
  ADMIN: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true,
  },
  USER: {
    create: false,
    read: true,
    update: false,
    delete: false,
    export: false,
  },
  CLIENT: {
    create: false,
    read: false,
    update: false,
    delete: false,
    export: false,
  },
}
```

Override permissions in your configuration:

```tsx
permissions: {
  create: true,  // Override default role permissions
  read: true,
  update: true,
  delete: false, // Disable delete for this entity
  export: true
}
```

## 📊 Field Types

The system supports various field types:

- **text**: Basic text input
- **email**: Email input with validation
- **password**: Password input
- **number**: Number input with min/max validation
- **textarea**: Multi-line text input
- **select**: Dropdown with options
- **checkbox**: Boolean checkbox
- **date**: Date picker
- **file**: File upload
- **rich-text**: Rich text editor (planned)

## 🎨 Customization

### Custom Renderers

```tsx
columns: [
  {
    key: 'avatar',
    label: 'Avatar',
    render: (value, item) => (
      <img 
        src={value} 
        alt={item.name}
        className="h-8 w-8 rounded-full"
      />
    )
  }
]
```

### Custom Actions

```tsx
actions: [
  {
    label: 'Send Email',
    icon: <EnvelopeIcon className="h-4 w-4" />,
    onClick: (item) => {
      // Custom action logic
      window.open(`mailto:${item.email}`)
    },
    variant: 'primary'
  }
]
```

### Bulk Actions

```tsx
bulkActions: [
  {
    label: 'Archive Selected',
    onClick: async (items) => {
      // Bulk action logic
      const ids = items.map(item => item.id)
      await archiveItems(ids)
    },
    variant: 'warning'
  }
]
```

## 🔍 Filtering and Search

### Search Configuration

```tsx
searchPlaceholder: 'Search by name, email, or company...',
enableSearch: true,
```

### Filter Configuration

```tsx
filters: [
  {
    name: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  },
  {
    name: 'createdAt',
    label: 'Created Date',
    type: 'date-range'
  },
  {
    name: 'featured',
    label: 'Featured',
    type: 'checkbox'
  }
]
```

## 📄 API Integration

The system expects your API endpoints to follow this structure:

### GET /api/admin/{endpoint}
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### POST /api/admin/{endpoint}
```json
{
  "success": true,
  "data": {...}
}
```

### PUT /api/admin/{endpoint}/{id}
```json
{
  "success": true,
  "data": {...}
}
```

### DELETE /api/admin/{endpoint}/{id}
```json
{
  "success": true
}
```

## 🎯 Best Practices

1. **Type Safety**: Always define proper TypeScript interfaces
2. **Validation**: Use both client-side and server-side validation
3. **Permissions**: Configure appropriate role-based permissions
4. **Performance**: Use pagination for large datasets
5. **UX**: Provide clear feedback for all operations
6. **Accessibility**: Ensure proper ARIA labels and keyboard navigation

## 🚀 Getting Started

1. Import the CRUD system:
```tsx
import { CrudManager } from '@/components/admin/crud'
```

2. Create your configuration:
```tsx
import { myEntityConfig } from './configs/my-entity-config'
```

3. Use in your page:
```tsx
<CrudManager config={myEntityConfig} />
```

4. Ensure your API endpoints are properly implemented

## 📝 Examples

Check out the example configurations:
- `testimonials-config.tsx` - Complete testimonials management
- `clients-config.tsx` - Client relationship management
- `projects-config.tsx` - Project tracking system

Visit `/admin/crud-demo` to see the system in action!
