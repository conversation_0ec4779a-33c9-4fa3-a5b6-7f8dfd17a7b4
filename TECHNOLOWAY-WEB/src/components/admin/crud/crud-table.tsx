'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  ChevronUpIcon,
  ChevronDownIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline'
import { useCrud } from './crud-context'
import { CrudConfig, CrudColumn } from './types'
import { LoadingSpinner } from '../ui/loading-spinner'
import { EmptyState } from '../ui/empty-state'
import { CellRenderer } from './cell-renderer'
import { IconRenderer } from './icon-renderer'

interface CrudTableProps<T> {
  config: CrudConfig<T>
}

export function CrudTable<T>({ config }: CrudTableProps<T>) {
  const { state, actions } = useCrud<T>()

  // Filter columns based on visibility settings
  const visibleColumns = config.columns.filter(column =>
    state.viewSettings.visibleColumns.includes(column.key as string)
  )

  const handleSort = (field: string) => {
    const newOrder = state.sort.field === field && state.sort.order === 'asc' ? 'desc' : 'asc'
    actions.setSort(field, newOrder)
  }

  const renderSortIcon = (column: CrudColumn<T>) => {
    if (!column.sortable) return null
    
    const field = column.key as string
    if (state.sort.field !== field) {
      return <ChevronUpIcon className="h-4 w-4 text-gray-400" />
    }
    
    return state.sort.order === 'asc' 
      ? <ChevronUpIcon className="h-4 w-4 text-blue-600" />
      : <ChevronDownIcon className="h-4 w-4 text-blue-600" />
  }

  const renderCellValue = (column: CrudColumn<T>, item: T) => {
    const value = (item as any)[column.key]

    return (
      <CellRenderer
        value={value}
        item={item}
        renderType={column.renderType}
        renderProps={column.renderProps}
      />
    )
  }

  const handleActionClick = (action: any, item: T) => {
    switch (action.action) {
      case 'edit':
        actions.openEditModal(item)
        break
      case 'delete':
        actions.openDeleteModal(item)
        break
      case 'view':
        // Handle view action
        console.log('View item:', item)
        break
      case 'custom':
        // Handle custom actions
        console.log('Custom action:', action.customAction, item)
        break
      default:
        console.log('Unknown action:', action.action)
    }
  }

  const renderActions = (item: T) => {
    if (!config.actions || config.actions.length === 0) return null

    const visibleActions = config.actions.filter(action => !action.hidden)

    if (visibleActions.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        {visibleActions.slice(0, 2).map((action, index) => (
          <button
            key={index}
            onClick={() => handleActionClick(action, item)}
            disabled={action.disabled}
            className={`p-2 rounded-lg transition-colors ${
              action.variant === 'danger'
                ? 'text-red-600 hover:bg-red-50'
                : action.variant === 'success'
                ? 'text-green-600 hover:bg-green-50'
                : action.variant === 'warning'
                ? 'text-yellow-600 hover:bg-yellow-50'
                : 'text-gray-600 hover:bg-gray-50'
            } disabled:opacity-50 disabled:cursor-not-allowed`}
            title={action.label}
          >
            {action.icon && <IconRenderer name={action.icon} />}
          </button>
        ))}

        {visibleActions.length > 2 && (
          <div className="relative group">
            <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg">
              <EllipsisVerticalIcon className="h-4 w-4" />
            </button>
            <div className="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
              {visibleActions.slice(2).map((action, index) => (
                <button
                  key={index + 2}
                  onClick={() => handleActionClick(action, item)}
                  disabled={action.disabled}
                  className={`w-full px-4 py-2 text-left text-sm transition-colors first:rounded-t-lg last:rounded-b-lg ${
                    action.variant === 'danger'
                      ? 'text-red-600 hover:bg-red-50'
                      : action.variant === 'success'
                      ? 'text-green-600 hover:bg-green-50'
                      : action.variant === 'warning'
                      ? 'text-yellow-600 hover:bg-yellow-50'
                      : 'text-gray-700 hover:bg-gray-50'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  <div className="flex items-center space-x-2">
                    {action.icon && <IconRenderer name={action.icon} />}
                    <span>{action.label}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (state.loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-8">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    )
  }

  if (state.error) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-8">
          <div className="text-center">
            <div className="text-red-600 mb-2">Error loading data</div>
            <div className="text-gray-600 text-sm">{state.error}</div>
            <button
              onClick={actions.refresh}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (state.items.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="p-8">
          <EmptyState
            title={`No ${config.title.toLowerCase()} found`}
            description={state.search ? 'Try adjusting your search criteria' : `Create your first ${config.title.toLowerCase().slice(0, -1)}`}
            action={
              state.search ? (
                <button
                  onClick={() => actions.setSearch('')}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                >
                  Clear Search
                </button>
              ) : (
                <button
                  onClick={actions.openCreateModal}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Create {config.title.slice(0, -1)}
                </button>
              )
            }
          />
        </div>
      </div>
    )
  }

  return (
    <div className="mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {config.enableBulkActions && (
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={state.selectedItems.length === state.items.length && state.items.length > 0}
                    onChange={actions.selectAllItems}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
              )}
              
              {visibleColumns.map((column, index) => (
                <th
                  key={index}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  } ${column.className || ''} ${
                    state.viewSettings.density === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key as string)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.label}</span>
                    {renderSortIcon(column)}
                  </div>
                </th>
              ))}
              
              {config.actions && config.actions.length > 0 && (
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {state.items.map((item, index) => (
              <motion.tr
                key={(item as any).id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={`hover:bg-gray-50 transition-colors ${
                  config.onItemClick ? 'cursor-pointer' : ''
                }`}
                onClick={() => config.onItemClick?.(item)}
              >
                {config.enableBulkActions && (
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={state.selectedItems.some(selected => (selected as any).id === (item as any).id)}
                      onChange={() => actions.selectItem(item)}
                      onClick={(e) => e.stopPropagation()}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </td>
                )}
                
                {visibleColumns.map((column, columnIndex) => (
                  <td
                    key={columnIndex}
                    className={`px-6 whitespace-nowrap text-sm text-gray-900 ${column.className || ''} ${
                      state.viewSettings.density === 'compact' ? 'py-2' : 'py-4'
                    }`}
                  >
                    {renderCellValue(column, item)}
                  </td>
                ))}
                
                {config.actions && config.actions.length > 0 && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div onClick={(e) => e.stopPropagation()}>
                      {renderActions(item)}
                    </div>
                  </td>
                )}
              </motion.tr>
            ))}
          </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
