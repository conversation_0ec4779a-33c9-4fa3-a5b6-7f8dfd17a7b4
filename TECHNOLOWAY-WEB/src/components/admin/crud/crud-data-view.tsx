'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { useCrud } from './crud-context'
import { CrudConfig } from './types'
import { CrudTable } from './crud-table'

interface CrudDataViewProps<T> {
  config: CrudConfig<T>
}

export function CrudDataView<T>({ config }: CrudDataViewProps<T>) {
  const { state } = useCrud<T>()

  const renderListView = () => {
    return <CrudTable config={config} />
  }

  const renderGridView = () => {
    const { items, viewSettings } = state
    const isCompact = viewSettings.density === 'compact'

    return (
      <div className={`grid gap-4 ${
        isCompact 
          ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
          : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
      }`}>
        {items.map((item: any, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors ${
              isCompact ? 'p-3' : 'p-4'
            }`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className={`font-medium text-gray-900 ${isCompact ? 'text-sm' : 'text-base'}`}>
                  {item.name || item.title || 'Unnamed'}
                </h3>
                {item.position && (
                  <p className={`text-gray-600 ${isCompact ? 'text-xs' : 'text-sm'}`}>
                    {item.position}
                  </p>
                )}
              </div>
              <span className={`px-2 py-1 rounded-full text-xs ${
                item.isActive 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-red-100 text-red-700'
              }`}>
                {item.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            {item.email && (
              <p className={`text-gray-600 mb-2 ${isCompact ? 'text-xs' : 'text-sm'}`}>
                {item.email}
              </p>
            )}
            
            {item.phone && (
              <p className={`text-gray-600 mb-2 ${isCompact ? 'text-xs' : 'text-sm'}`}>
                {item.phone}
              </p>
            )}
            
            <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
              <div className={`text-gray-500 ${isCompact ? 'text-xs' : 'text-sm'}`}>
                {item.hireDate && (
                  <span>Hired: {new Date(item.hireDate).toLocaleDateString()}</span>
                )}
              </div>
              <div className="flex items-center space-x-1">
                {config.actions?.map((action) => {
                  // Use a simple icon placeholder for now
                  return (
                    <button
                      key={action.action}
                      className={`p-1 rounded hover:bg-gray-100 transition-colors ${
                        isCompact ? 'text-xs' : 'text-sm'
                      }`}
                      title={action.tooltip}
                    >
                      <span className={`${isCompact ? 'h-3 w-3' : 'h-4 w-4'} bg-gray-400 rounded`} />
                    </button>
                  )
                })}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    )
  }

  const renderCardView = () => {
    const { items, viewSettings } = state
    const isCompact = viewSettings.density === 'compact'

    return (
      <div className={`grid gap-6 ${
        isCompact 
          ? 'grid-cols-1 md:grid-cols-2' 
          : 'grid-cols-1 lg:grid-cols-2'
      }`}>
        {items.map((item: any, index) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors ${
              isCompact ? 'p-4' : 'p-6'
            }`}
          >
            <div className="flex items-start space-x-4">
              {item.photoUrl && (
                <img
                  src={item.photoUrl}
                  alt={item.name}
                  className={`rounded-full object-cover ${
                    isCompact ? 'h-12 w-12' : 'h-16 w-16'
                  }`}
                />
              )}
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className={`font-semibold text-gray-900 ${isCompact ? 'text-base' : 'text-lg'}`}>
                      {item.name || item.title || 'Unnamed'}
                    </h3>
                    {item.position && (
                      <p className={`text-gray-600 ${isCompact ? 'text-sm' : 'text-base'}`}>
                        {item.position}
                      </p>
                    )}
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    item.isActive 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {item.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                <div className={`mt-3 space-y-2 ${isCompact ? 'text-sm' : 'text-base'}`}>
                  {item.email && (
                    <p className="text-gray-600 flex items-center">
                      <span className="font-medium mr-2">Email:</span>
                      {item.email}
                    </p>
                  )}
                  {item.phone && (
                    <p className="text-gray-600 flex items-center">
                      <span className="font-medium mr-2">Phone:</span>
                      {item.phone}
                    </p>
                  )}
                  {item.hireDate && (
                    <p className="text-gray-600 flex items-center">
                      <span className="font-medium mr-2">Hired:</span>
                      {new Date(item.hireDate).toLocaleDateString()}
                    </p>
                  )}
                  {item.salary && (
                    <p className="text-gray-600 flex items-center">
                      <span className="font-medium mr-2">Salary:</span>
                      ${item.salary.toLocaleString()}
                    </p>
                  )}
                </div>
                
                {item.bio && (
                  <p className={`mt-3 text-gray-600 ${isCompact ? 'text-sm' : 'text-base'}`}>
                    {item.bio.length > 100 ? `${item.bio.substring(0, 100)}...` : item.bio}
                  </p>
                )}
                
                <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-3">
                    {item.linkedinUrl && (
                      <a
                        href={item.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        LinkedIn
                      </a>
                    )}
                    {item.githubUrl && (
                      <a
                        href={item.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-800"
                      >
                        GitHub
                      </a>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {config.actions?.map((action) => {
                      // Use a simple icon placeholder for now
                      return (
                        <button
                          key={action.action}
                          className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                          title={action.tooltip}
                        >
                          <span className="h-4 w-4 bg-gray-400 rounded" />
                        </button>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    )
  }

  const renderView = () => {
    switch (state.viewSettings.mode) {
      case 'grid':
        return renderGridView()
      case 'card':
        return renderCardView()
      case 'list':
      default:
        return renderListView()
    }
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {renderView()}
    </div>
  )
}
