'use client'

import React, { create<PERSON>ontext, useContext, useReducer, useCallback, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { CrudContextValue, CrudState, CrudConfig, DEFAULT_ROLE_PERMISSIONS, UserRole, ViewMode, DisplayDensity, ViewSettings } from './types'
import { useNotification } from '../ui/notification'

// CRUD Reducer
type CrudAction<T> =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ITEMS'; payload: T[] }
  | { type: 'SET_PAGINATION'; payload: any }
  | { type: 'SET_SEARCH'; payload: string }
  | { type: 'SET_FILTERS'; payload: Record<string, any> }
  | { type: 'SET_SORT'; payload: { field: string; order: 'asc' | 'desc' } }
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_VIEW_MODE'; payload: ViewMode }
  | { type: 'SET_DISPLAY_DENSITY'; payload: DisplayDensity }
  | { type: 'SET_COLUMN_VISIBILITY'; payload: { columnKey: string; visible: boolean } }
  | { type: 'RESET_VIEW_SETTINGS' }
  | { type: 'SELECT_ITEM'; payload: T }
  | { type: 'SELECT_ALL_ITEMS' }
  | { type: 'CLEAR_SELECTION' }
  | { type: 'OPEN_CREATE_MODAL' }
  | { type: 'CLOSE_CREATE_MODAL' }
  | { type: 'OPEN_EDIT_MODAL'; payload: T }
  | { type: 'CLOSE_EDIT_MODAL' }
  | { type: 'OPEN_DELETE_MODAL'; payload: T }
  | { type: 'CLOSE_DELETE_MODAL' }
  | { type: 'ADD_ITEM'; payload: T }
  | { type: 'UPDATE_ITEM'; payload: T }
  | { type: 'REMOVE_ITEM'; payload: string }

function crudReducer<T>(state: CrudState<T>, action: CrudAction<T>): CrudState<T> {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    case 'SET_ITEMS':
      return { ...state, items: action.payload }
    case 'SET_PAGINATION':
      return { ...state, pagination: action.payload }
    case 'SET_SEARCH':
      return { ...state, search: action.payload }
    case 'SET_FILTERS':
      return { ...state, filters: action.payload }
    case 'SET_SORT':
      return { ...state, sort: action.payload }
    case 'SET_PAGE':
      return { ...state, pagination: state.pagination ? { ...state.pagination, page: action.payload } : null }
    case 'SET_VIEW_MODE':
      return {
        ...state,
        viewSettings: { ...state.viewSettings, mode: action.payload }
      }
    case 'SET_DISPLAY_DENSITY':
      return {
        ...state,
        viewSettings: { ...state.viewSettings, density: action.payload }
      }
    case 'SET_COLUMN_VISIBILITY':
      const { columnKey, visible } = action.payload
      const currentColumns = state.viewSettings.visibleColumns
      const newColumns = visible
        ? [...currentColumns, columnKey]
        : currentColumns.filter(col => col !== columnKey)
      return {
        ...state,
        viewSettings: { ...state.viewSettings, visibleColumns: newColumns }
      }
    case 'RESET_VIEW_SETTINGS':
      // Reset to default view settings based on config
      const defaultViewSettings: ViewSettings = {
        mode: 'list',
        density: 'comfortable',
        visibleColumns: [], // Will be set in the provider
      }
      return {
        ...state,
        viewSettings: defaultViewSettings
      }
    case 'SELECT_ITEM':
      const isSelected = state.selectedItems.some(item => (item as any).id === (action.payload as any).id)
      return {
        ...state,
        selectedItems: isSelected
          ? state.selectedItems.filter(item => (item as any).id !== (action.payload as any).id)
          : [...state.selectedItems, action.payload]
      }
    case 'SELECT_ALL_ITEMS':
      return {
        ...state,
        selectedItems: state.selectedItems.length === state.items.length ? [] : [...state.items]
      }
    case 'CLEAR_SELECTION':
      return { ...state, selectedItems: [] }
    case 'OPEN_CREATE_MODAL':
      return { ...state, isCreateModalOpen: true }
    case 'CLOSE_CREATE_MODAL':
      return { ...state, isCreateModalOpen: false }
    case 'OPEN_EDIT_MODAL':
      return { ...state, isEditModalOpen: true, editingItem: action.payload }
    case 'CLOSE_EDIT_MODAL':
      return { ...state, isEditModalOpen: false, editingItem: null }
    case 'OPEN_DELETE_MODAL':
      return { ...state, isDeleteModalOpen: true, deletingItem: action.payload }
    case 'CLOSE_DELETE_MODAL':
      return { ...state, isDeleteModalOpen: false, deletingItem: null }
    case 'ADD_ITEM':
      return { ...state, items: [action.payload, ...state.items] }
    case 'UPDATE_ITEM':
      return {
        ...state,
        items: state.items.map(item => 
          (item as any).id === (action.payload as any).id ? action.payload : item
        )
      }
    case 'REMOVE_ITEM':
      return {
        ...state,
        items: state.items.filter(item => (item as any).id !== action.payload),
        selectedItems: state.selectedItems.filter(item => (item as any).id !== action.payload)
      }
    default:
      return state
  }
}

const CrudContext = createContext<CrudContextValue | null>(null)

interface CrudProviderProps<T> {
  children: React.ReactNode
  config: CrudConfig<T>
}

export function CrudProvider<T>({ children, config }: CrudProviderProps<T>) {
  const { data: session } = useSession()
  const { success, error } = useNotification()

  // Get default visible columns
  const getDefaultVisibleColumns = () => {
    return config.columns
      .filter(col => col.defaultVisible !== false)
      .map(col => col.key as string)
  }

  // Get default view settings
  const getDefaultViewSettings = (): ViewSettings => {
    return {
      mode: config.defaultViewSettings?.mode || 'list',
      density: config.defaultViewSettings?.density || 'comfortable',
      visibleColumns: config.defaultViewSettings?.visibleColumns || getDefaultVisibleColumns(),
    }
  }

  const initialState: CrudState<T> = {
    items: [],
    selectedItems: [],
    loading: false,
    error: null,
    pagination: null,
    search: '',
    filters: {},
    sort: config.defaultSort || { field: 'updatedAt', order: 'desc' },
    viewSettings: getDefaultViewSettings(),
    isCreateModalOpen: false,
    isEditModalOpen: false,
    editingItem: null,
    isDeleteModalOpen: false,
    deletingItem: null,
  }

  const [state, dispatch] = useReducer(crudReducer<T>, initialState)

  // Check permissions
  const hasPermission = useCallback((action: string): boolean => {
    if (!session?.user?.role) return false
    
    const userRole = session.user.role as UserRole
    const rolePermissions = DEFAULT_ROLE_PERMISSIONS[userRole]
    const configPermissions = config.permissions
    
    // Config permissions override default role permissions
    if (configPermissions && action in configPermissions) {
      return configPermissions[action as keyof typeof configPermissions] ?? false
    }
    
    return rolePermissions[action as keyof typeof rolePermissions] ?? false
  }, [session, config.permissions])

  // Build query string for API calls
  const buildQueryString = useCallback(() => {
    const params = new URLSearchParams()
    
    if (state.search) params.append('search', state.search)
    if (state.pagination?.page) params.append('page', state.pagination.page.toString())
    if (state.pagination?.limit) params.append('limit', state.pagination.limit.toString())
    if (state.sort.field) params.append('sortBy', state.sort.field)
    if (state.sort.order) params.append('sortOrder', state.sort.order)
    
    Object.entries(state.filters).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        params.append(key, value.toString())
      }
    })
    
    return params.toString()
  }, [state.search, state.pagination, state.sort, state.filters])

  // Fetch data from API
  const fetchData = useCallback(async () => {
    if (!hasPermission('read')) {
      dispatch({ type: 'SET_ERROR', payload: 'You do not have permission to view this data' })
      return
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      const queryString = buildQueryString()
      const response = await fetch(`/api/admin/${config.endpoint}?${queryString}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success && result.data) {
        dispatch({ type: 'SET_ITEMS', payload: result.data })
        dispatch({ type: 'SET_PAGINATION', payload: result.pagination || null })
      } else {
        throw new Error(result.error || 'Failed to fetch data')
      }
    } catch (err) {
      dispatch({ type: 'SET_ERROR', payload: err instanceof Error ? err.message : 'An error occurred' })
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [config.endpoint, buildQueryString, hasPermission])

  // CRUD Actions
  const actions = {
    setSearch: (search: string) => dispatch({ type: 'SET_SEARCH', payload: search }),
    setFilters: (filters: Record<string, any>) => dispatch({ type: 'SET_FILTERS', payload: filters }),
    setSort: (field: string, order: 'asc' | 'desc') => dispatch({ type: 'SET_SORT', payload: { field, order } }),
    setPage: (page: number) => dispatch({ type: 'SET_PAGE', payload: page }),

    // View control actions
    setViewMode: (mode: ViewMode) => dispatch({ type: 'SET_VIEW_MODE', payload: mode }),
    setDisplayDensity: (density: DisplayDensity) => dispatch({ type: 'SET_DISPLAY_DENSITY', payload: density }),
    setColumnVisibility: (columnKey: string, visible: boolean) =>
      dispatch({ type: 'SET_COLUMN_VISIBILITY', payload: { columnKey, visible } }),
    resetViewSettings: () => {
      const defaultSettings = getDefaultViewSettings()
      dispatch({ type: 'RESET_VIEW_SETTINGS' })
      // Update the visible columns to the default
      dispatch({ type: 'SET_COLUMN_VISIBILITY', payload: { columnKey: '', visible: false } })
      // Set all default columns as visible
      defaultSettings.visibleColumns.forEach(columnKey => {
        dispatch({ type: 'SET_COLUMN_VISIBILITY', payload: { columnKey, visible: true } })
      })
    },

    selectItem: (item: T) => dispatch({ type: 'SELECT_ITEM', payload: item }),
    selectAllItems: () => dispatch({ type: 'SELECT_ALL_ITEMS' }),
    clearSelection: () => dispatch({ type: 'CLEAR_SELECTION' }),
    openCreateModal: () => dispatch({ type: 'OPEN_CREATE_MODAL' }),
    closeCreateModal: () => dispatch({ type: 'CLOSE_CREATE_MODAL' }),
    openEditModal: (item: T) => dispatch({ type: 'OPEN_EDIT_MODAL', payload: item }),
    closeEditModal: () => dispatch({ type: 'CLOSE_EDIT_MODAL' }),
    openDeleteModal: (item: T) => dispatch({ type: 'OPEN_DELETE_MODAL', payload: item }),
    closeDeleteModal: () => dispatch({ type: 'CLOSE_DELETE_MODAL' }),
    
    createItem: async (data: any) => {
      if (!hasPermission('create')) {
        throw new Error('You do not have permission to create items')
      }

      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success && result.data) {
        dispatch({ type: 'ADD_ITEM', payload: result.data })
        success('Item Created', `${config.title.slice(0, -1)} has been created successfully`)
      } else {
        throw new Error(result.error || 'Failed to create item')
      }
    },

    updateItem: async (id: string, data: any) => {
      if (!hasPermission('update')) {
        throw new Error('You do not have permission to update items')
      }

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success && result.data) {
        dispatch({ type: 'UPDATE_ITEM', payload: result.data })
        success('Item Updated', `${config.title.slice(0, -1)} has been updated successfully`)
      } else {
        throw new Error(result.error || 'Failed to update item')
      }
    },

    deleteItem: async (id: string) => {
      if (!hasPermission('delete')) {
        throw new Error('You do not have permission to delete items')
      }

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success) {
        dispatch({ type: 'REMOVE_ITEM', payload: id })
        success('Item Deleted', `${config.title.slice(0, -1)} has been deleted successfully`)
      } else {
        throw new Error(result.error || 'Failed to delete item')
      }
    },

    bulkDelete: async (ids: string[]) => {
      if (!hasPermission('delete')) {
        throw new Error('You do not have permission to delete items')
      }

      const response = await fetch(`/api/admin/${config.endpoint}/bulk-delete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (result.success) {
        ids.forEach(id => dispatch({ type: 'REMOVE_ITEM', payload: id }))
      } else {
        throw new Error(result.error || 'Failed to delete items')
      }
    },

    bulkAction: async (action: string, items: T[]) => {
      const ids = items.map(item => (item as any).id)

      const response = await fetch(`/api/admin/${config.endpoint}/bulk-action`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ids }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      if (!result.success) {
        throw new Error(result.error || 'Failed to perform bulk action')
      }

      // Refresh data after bulk action
      fetchData()
    },

    refresh: fetchData,

    exportData: async () => {
      if (!hasPermission('export')) {
        throw new Error('You do not have permission to export data')
      }

      const response = await fetch(`/api/admin/${config.endpoint}/export`)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${config.endpoint}-export.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    },
  }

  // Fetch data on mount and when dependencies change
  useEffect(() => {
    fetchData()
  }, [state.search, state.filters, state.sort, state.pagination?.page])

  const contextValue: CrudContextValue<T> = {
    state,
    actions,
  }

  return (
    <CrudContext.Provider value={contextValue}>
      {children}
    </CrudContext.Provider>
  )
}

export function useCrud<T>(): CrudContextValue<T> {
  const context = useContext(CrudContext)
  if (!context) {
    throw new Error('useCrud must be used within a CrudProvider')
  }
  return context as CrudContextValue<T>
}
