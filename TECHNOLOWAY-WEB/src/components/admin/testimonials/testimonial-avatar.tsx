'use client'

import React, { useState } from 'react'
import { UserCircleIcon } from '@heroicons/react/24/outline'

interface TestimonialAvatarProps {
  name: string
  photoUrl?: string | null
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full-height'
  className?: string
  style?: React.CSSProperties
}

export function TestimonialAvatar({ 
  name, 
  photoUrl, 
  size = 'md', 
  className = '',
  style = {}
}: TestimonialAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [imageLoading, setImageLoading] = useState(true)

  // Size configurations
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
    'full-height': 'w-full h-full'
  }

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
    'full-height': 'w-16 h-16'
  }

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    'full-height': 'text-4xl'
  }

  // Generate initials from client name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Generate a consistent color based on the client name
  const getBackgroundColor = (name: string) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ]
    
    let hash = 0
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash)
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  const handleImageLoad = () => {
    setImageLoading(false)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageLoading(false)
    setImageError(true)
  }

  const baseClasses = `
    ${sizeClasses[size]} 
    rounded-full 
    flex 
    items-center 
    justify-center 
    overflow-hidden 
    ${size === 'full-height' ? 'min-h-[320px] rounded-lg' : ''}
    ${className}
  `

  // If we have a valid photo URL and no error, show the image
  if (photoUrl && !imageError) {
    return (
      <div 
        className={`${baseClasses} bg-gray-100 relative`}
        style={style}
      >
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6"></div>
          </div>
        )}
        <img
          src={photoUrl}
          alt={`${name} photo`}
          className={`
            ${size === 'full-height' ? 'w-full h-full object-cover' : 'w-full h-full object-cover'}
            ${imageLoading ? 'opacity-0' : 'opacity-100'}
            transition-opacity duration-200
          `}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
    )
  }

  // Fallback: Show initials with colored background
  return (
    <div 
      className={`
        ${baseClasses} 
        ${getBackgroundColor(name)} 
        text-white 
        font-semibold 
        ${textSizes[size]}
        shadow-sm
      `}
      style={style}
      title={name}
    >
      {size === 'full-height' ? (
        <div className="flex flex-col items-center justify-center space-y-4">
          <UserCircleIcon className="w-24 h-24 text-white opacity-80" />
          <div className="text-center px-4">
            <div className="text-2xl font-bold mb-2">{getInitials(name)}</div>
            <div className="text-sm opacity-90 break-words">{name}</div>
          </div>
        </div>
      ) : (
        <span>{getInitials(name)}</span>
      )}
    </div>
  )
}
