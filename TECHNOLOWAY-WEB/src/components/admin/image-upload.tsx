'use client'

import React, { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { PhotoIcon, ArrowUpTrayIcon } from '@heroicons/react/24/outline'

interface ImageUploadProps {
  value?: string
  onChange: (url: string) => void
  label?: string
  accept?: string
  maxSize?: number // in MB
  className?: string
}

export default function ImageUpload({
  value,
  onChange,
  label = 'Project Image',
  accept = 'image/*',
  maxSize = 10, // 10MB default
  className = ''
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    setError(null)

    // Enhanced validation for all image types
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp',
      'image/svg+xml', 'image/bmp', 'image/tiff', 'image/ico', 'image/avif'
    ]

    if (!allowedTypes.includes(file.type.toLowerCase())) {
      setError('Please select a valid image file (JPEG, PNG, GIF, WebP, SVG, BMP, TIFF, ICO, AVIF)')
      return
    }

    // Validate file size
    if (file.size > maxSize * 1024 * 1024) {
      setError(`File size must be less than ${maxSize}MB`)
      return
    }

    setUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('category', 'project') // Set category for project images

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        credentials: 'include', // Include authentication
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()

      console.log('Upload response:', result)

      if (result.success && result.data?.files?.[0]) {
        let uploadedUrl = result.data.files[0].url

        // Ensure the URL is absolute by adding the domain if it's relative
        if (uploadedUrl.startsWith('/')) {
          uploadedUrl = `${window.location.origin}${uploadedUrl}`
        }

        onChange(uploadedUrl)
      } else {
        throw new Error(result.error || 'Upload failed')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed')
    } finally {
      setUploading(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null)
    onChange(e.target.value)
  }

  const removeImage = () => {
    onChange('')
    setError(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">{label}</label>

      {/* URL Input with Upload Button */}
      <div className="relative">
        <input
          type="url"
          value={value || ''}
          onChange={handleUrlChange}
          placeholder="Enter image URL or click upload button"
          disabled={uploading}
          className={`w-full px-3 py-2 pr-24 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
            uploading ? 'bg-gray-50 cursor-not-allowed' : ''
          }`}
        />

        {/* Upload Button */}
        <button
          type="button"
          onClick={handleUploadClick}
          disabled={uploading}
          className={`absolute right-1 top-1 bottom-1 px-3 py-1 text-xs font-medium rounded border transition-colors ${
            uploading
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-200'
              : 'bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 hover:border-blue-300'
          }`}
        >
          {uploading ? (
            <div className="flex items-center space-x-1">
              <div className="animate-spin rounded-full h-3 w-3 border border-blue-600 border-t-transparent"></div>
              <span>Uploading...</span>
            </div>
          ) : (
            <div className="flex items-center space-x-1">
              <ArrowUpTrayIcon className="h-3 w-3" />
              <span>Upload</span>
            </div>
          )}
        </button>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp,image/svg+xml,image/bmp,image/tiff,image/ico,image/avif"
          onChange={handleFileChange}
          className="hidden"
        />
      </div>

      {/* Helper Text */}
      <p className="text-xs text-gray-500">
        Supports: JPEG, PNG, GIF, WebP, SVG, BMP, TIFF, ICO, AVIF (max {maxSize}MB)
      </p>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-red-600 text-sm bg-red-50 border border-red-200 rounded-md p-2"
        >
          {error}
        </motion.div>
      )}

      {/* Preview */}
      {value && !uploading && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="relative"
        >
          <img
            src={value}
            alt="Preview"
            className="w-full h-32 object-cover rounded-md border border-gray-200"
            onError={() => setError('Failed to load image')}
          />
          <button
            type="button"
            onClick={removeImage}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
            title="Remove image"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </motion.div>
      )}
    </div>
  )
}
