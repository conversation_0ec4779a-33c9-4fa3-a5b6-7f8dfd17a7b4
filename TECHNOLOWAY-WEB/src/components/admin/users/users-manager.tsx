'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  UserIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import { UserModal } from './user-modal'
import { UserAvatar } from './user-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'
import { safeToLocaleDateString } from '@/lib/utils/date-utils'

interface User {
  id: number
  email: string
  firstname?: string
  lastname?: string
  imageurl?: string
  role: 'ADMIN' | 'USER' | 'CLIENT'
  isactive: boolean
  emailverified?: string
  createdat: string
  updatedat: string
  _count?: {
    clients: number
    auditlogs: number
  }
  [key: string]: any
}

interface UsersManagerProps {
  config: CrudConfig<User>
}

export function UsersManager({ config }: UsersManagerProps) {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState(
    config.defaultViewSettings?.visibleColumns || [
      'email', 'firstname', 'lastname', 'role', 'emailverified', 'createdat', 'isactive'
    ]
  )
  const [sortBy, setSortBy] = useState(config.defaultSort?.field || 'createdat')
  const [sortOrder, setSortOrder] = useState(config.defaultSort?.direction || 'desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)

  // Filter states
  const [roleFilter, setRoleFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [emailVerifiedFilter, setEmailVerifiedFilter] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch users
  const fetchUsers = async (preserveFocus = false) => {
    try {
      // Only show full loading for initial load, not for search
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
        ...(roleFilter && { role: roleFilter }),
        ...(statusFilter && { isactive: statusFilter }),
        ...(emailVerifiedFilter && { emailverified: emailVerifiedFilter }),
      })

      console.log('Fetching users with params:', params.toString()) // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch users')

      const data = await response.json()
      console.log('Received users data:', data) // Debug log

      setUsers(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / (config.pageSize || 10)))
      setError(null) // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching users:', err) // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchUsers(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, roleFilter, statusFilter, emailVerifiedFilter])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create user')

      setIsCreateModalOpen(false)
      fetchUsers()
      alert('User created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create user'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      console.log('Updating user with data:', formData)

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      console.log('Update response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Update error response:', errorData)
        throw new Error(errorData.error || `Failed to update user (${response.status})`)
      }

      const result = await response.json()
      console.log('Update success:', result)

      setIsEditModalOpen(false)
      setEditingUser(null)
      fetchUsers()
      alert('User updated successfully!')
    } catch (err) {
      console.error('Update error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update user'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        // Handle specific error for users with associated clients
        if (data.error?.includes('associated clients')) {
          const user = users.find(u => u.id.toString() === id)
          const userName = user?.firstname && user?.lastname
            ? `${user.firstname} ${user.lastname}`
            : user?.email || 'this user'

          const shouldDeactivate = window.confirm(
            `Cannot delete ${userName} because they have associated clients.\n\n` +
            `Would you like to deactivate the user instead? This will:\n` +
            `• Keep all data intact\n` +
            `• Prevent the user from logging in\n` +
            `• Preserve client relationships\n\n` +
            `Click OK to deactivate or Cancel to do nothing.`
          )

          if (shouldDeactivate) {
            await handleToggleStatus(id, false)
            alert(`User ${userName} has been deactivated successfully.`)
          }
          return
        }

        throw new Error(data.error || 'Failed to delete user')
      }

      // Show success message
      setError(null)
      fetchUsers()
      alert('User deleted successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete user'
      setError(errorMessage)
      alert(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: User) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open user details in new tab or modal
          window.open(`/admin/users/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingUser(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(item.id.toString(), !item.isactive)
          break

        case 'delete':
          const userName = item.firstname && item.lastname
            ? `${item.firstname} ${item.lastname}`
            : item.email

          const hasClients = item._count?.clients ? item._count.clients > 0 : false
          const hasAuditLogs = item._count?.auditlogs ? item._count.auditlogs > 0 : false

          let confirmMessage = `Are you sure you want to delete "${userName}"?\n\n`

          if (hasClients || hasAuditLogs) {
            confirmMessage += 'WARNING: This user has associated data:\n'
            if (hasClients) confirmMessage += `• ${item._count?.clients ?? 0} client(s)\n`
            if (hasAuditLogs) confirmMessage += `• ${item._count?.auditlogs ?? 0} audit log(s)\n`
            confirmMessage += '\nIf deletion fails due to data constraints, you will be offered the option to deactivate instead.\n\n'
          }

          confirmMessage += 'This action cannot be undone. Continue?'

          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id.toString())
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle user status
  const handleToggleStatus = async (id: string, isactive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isactive }),
      })

      if (!response.ok) throw new Error('Failed to update user status')

      fetchUsers()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update user status')
    }
  }

  const formatDate = (dateString: string) => {
    return safeToLocaleDateString(dateString)
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string, userIds: string[]) => {
    try {
      if (action === 'delete') {
        // Check if any selected users have associated clients
        const usersWithClients = users.filter(user =>
          userIds.includes(user.id.toString()) && (user._count?.clients ? user._count.clients > 0 : false)
        )

        if (usersWithClients.length > 0) {
          const userNames = usersWithClients.map(user =>
            user.firstname && user.lastname
              ? `${user.firstname} ${user.lastname}`
              : user.email
          ).join(', ')

          const shouldDeactivate = window.confirm(
            `Cannot delete the following users because they have associated clients:\n${userNames}\n\n` +
            `Would you like to deactivate these users instead? This will:\n` +
            `• Keep all data intact\n` +
            `• Prevent users from logging in\n` +
            `• Preserve client relationships\n\n` +
            `Click OK to deactivate or Cancel to do nothing.`
          )

          if (shouldDeactivate) {
            await handleBulkAction('deactivate', userIds)
            alert(`Selected users have been deactivated successfully.`)
          }
          return
        }
      }

      const endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: userIds }

      switch (action) {
        case 'activate':
          body.data = { isactive: true }
          break
        case 'deactivate':
          body.data = { isactive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: userIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} users`)
      }

      const result = await response.json()

      if (result.success) {
        setSelectedUsers([])
        fetchUsers()
        alert(`Bulk ${action} completed successfully!`)
      } else {
        throw new Error(result.error || `Failed to ${action} users`)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `Failed to ${action} users`
      alert(errorMessage)
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([])
    } else {
      setSelectedUsers(users.map(user => String(user.id)))
    }
  }

  // Handle select individual user
  const handleSelectUser = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  // View control functions
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(config.defaultViewSettings?.visibleColumns || [
      'email', 'firstname', 'lastname', 'role', 'emailverified', 'createdat', 'isactive'
    ])
    setViewMode(config.defaultViewSettings?.mode || 'list')
    setDisplayDensity(config.defaultViewSettings?.density || 'comfortable')
    setSortBy(config.defaultSort?.field || 'createdat')
    setSortOrder(config.defaultSort?.direction || 'desc')
  }

  const availableColumns = [
    { key: 'email', label: 'Email', hideable: false },
    { key: 'firstname', label: 'First Name', hideable: true },
    { key: 'lastname', label: 'Last Name', hideable: true },
    { key: 'role', label: 'Role', hideable: true },
    { key: 'emailverified', label: 'Email Verified', hideable: true },
    { key: 'createdat', label: 'Created', hideable: true },
    { key: 'updatedat', label: 'Last Active', hideable: true },
    { key: 'isactive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Get density-based classes
  const getDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-4'
  }

  const getHeaderDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-3'
  }

  const getImageSize = () => {
    return displayDensity === 'compact'
      ? 'h-8 w-8'
      : 'h-10 w-10'
  }

  const getTextSize = () => {
    return displayDensity === 'compact'
      ? 'text-xs'
      : 'text-sm'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add User</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder={config.searchPlaceholder || 'Search users by email, first name, last name...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                  >
                    <div className="py-1">
                      {Object.entries(densityLabels).map(([density, label]) => (
                        <button
                          key={density}
                          onClick={() => {
                            setDisplayDensity(density as 'compact' | 'comfortable')
                            setShowDensityMenu(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 ${
                            displayDensity === density ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                          }`}
                        >
                          {label}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-10"
                  >
                    <div className="py-2">
                      <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-100">
                        Show/Hide Columns
                      </div>
                      {availableColumns.map((column) => (
                        <label
                          key={column.key}
                          className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={visibleColumns.includes(column.key)}
                            onChange={() => handleColumnToggle(column.key)}
                            disabled={!column.hideable}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className={`ml-3 text-sm ${!column.hideable ? 'text-gray-400' : 'text-gray-700'}`}>
                            {column.label}
                          </span>
                        </label>
                      ))}
                      <div className="border-t border-gray-100 mt-2 pt-2">
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-blue-600 hover:bg-blue-50"
                        >
                          Reset to Default
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Filters Section */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 pt-4"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Role Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All Roles</option>
                    <option value="ADMIN">Administrator</option>
                    <option value="USER">User</option>
                    <option value="CLIENT">Client</option>
                  </select>
                </div>

                {/* Status Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All</option>
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                  </select>
                </div>

                {/* Email Verified Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email Verification</label>
                  <select
                    value={emailVerifiedFilter}
                    onChange={(e) => setEmailVerifiedFilter(e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="">All</option>
                    <option value="true">Verified</option>
                    <option value="false">Unverified</option>
                  </select>
                </div>
              </div>

              {/* Clear Filters */}
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    setRoleFilter('')
                    setStatusFilter('')
                    setEmailVerifiedFilter('')
                    setSearchQuery('')
                  }}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Clear Filters
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Actions */}
        {selectedUsers.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-blue-50 border border-blue-200 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleBulkAction('activate', selectedUsers)}
                    className="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleBulkAction('deactivate', selectedUsers)}
                    className="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700"
                  >
                    Deactivate
                  </button>
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to delete the selected users? This action cannot be undone.')) {
                        handleBulkAction('delete', selectedUsers)
                      }
                    }}
                    className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Delete Selected
                  </button>
                </div>
              </div>
              <button
                onClick={() => setSelectedUsers([])}
                className="text-blue-600 hover:text-blue-800"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Users Display */}
      {viewMode === 'list' ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className={`w-12 ${getHeaderDensityClasses()} text-left`}>
                    <input
                      type="checkbox"
                      checked={selectedUsers.length === users.length && users.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>

                  {availableColumns
                    .filter(column => visibleColumns.includes(column.key))
                    .map((column) => (
                      <th
                        key={column.key}
                        className={`${getHeaderDensityClasses()} text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100`}
                        onClick={() => handleSort(column.key)}
                      >
                        <div className="flex items-center space-x-1">
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </div>
                      </th>
                    ))}

                  <th className={`${getHeaderDensityClasses()} text-right text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.length === 0 ? (
                  <tr>
                    <td colSpan={visibleColumns.length + 2} className="px-6 py-12 text-center text-gray-500">
                      {debouncedSearchQuery || roleFilter || statusFilter || emailVerifiedFilter ? 'No users found matching your criteria.' : 'No users found.'}
                    </td>
                  </tr>
                ) : (
                  users.map((user) => (
                    <tr
                      key={user.id}
                      className={`hover:bg-gray-50 ${
                        selectedUsers.includes(user.id.toString()) ? 'bg-blue-50' : ''
                      }`}
                    >
                      <td className={`${getDensityClasses()} whitespace-nowrap`}>
                        <input
                          type="checkbox"
                          checked={selectedUsers.includes(user.id.toString())}
                          onChange={() => handleSelectUser(user.id.toString())}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </td>

                      {availableColumns
                        .filter(column => visibleColumns.includes(column.key))
                        .map((column) => (
                          <td key={column.key} className={`${getDensityClasses()} whitespace-nowrap`}>
                            {column.key === 'email' && (
                              <div className="flex items-center">
                                <UserAvatar user={user} size={displayDensity === 'compact' ? 'sm' : 'md'} />
                                <div className={displayDensity === 'compact' ? 'ml-2' : 'ml-4'}>
                                  <div className={`${getTextSize()} font-medium text-gray-900`}>{user.email}</div>
                                  {user.firstname && user.lastname && (
                                    <div className={`${getTextSize()} text-gray-500`}>{user.firstname} {user.lastname}</div>
                                  )}
                                </div>
                              </div>
                            )}
                            {column.key === 'firstname' && (
                              <div className={`${getTextSize()} text-gray-900`}>
                                {user.firstname || '-'}
                              </div>
                            )}
                            {column.key === 'lastname' && (
                              <div className={`${getTextSize()} text-gray-900`}>
                                {user.lastname || '-'}
                              </div>
                            )}
                            {column.key === 'role' && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.role === 'ADMIN' ? 'bg-red-100 text-red-800' :
                                user.role === 'CLIENT' ? 'bg-green-100 text-green-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {user.role}
                              </span>
                            )}
                            {column.key === 'emailverified' && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.emailverified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {user.emailverified ? 'Verified' : 'Unverified'}
                              </span>
                            )}
                            {column.key === 'createdat' && user.createdat && (
                              <div className={`${getTextSize()} text-gray-900`}>{formatDate(user.createdat)}</div>
                            )}
                            {column.key === 'updatedat' && user.updatedat && (
                              <div className={`${getTextSize()} text-gray-900`}>{formatDate(user.updatedat)}</div>
                            )}
                            {column.key === 'isactive' && (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                user.isactive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}>
                                {user.isactive ? 'Active' : 'Inactive'}
                              </span>
                            )}
                          </td>
                        ))}

                      <td className={`${getDensityClasses()} whitespace-nowrap text-right text-sm font-medium`}>
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleAction('view', user)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="View"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('edit', user)}
                            className="text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('toggle-status', user)}
                            className={`text-gray-400 hover:text-yellow-600 transition-colors ${
                              user.isactive ? 'text-green-600' : ''
                            }`}
                            title={user.isactive ? 'Deactivate' : 'Activate'}
                          >
                            <PowerIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleAction('delete', user)}
                            className={`transition-colors ${
                              user._count?.clients && user._count.clients > 0
                                ? 'text-yellow-400 hover:text-yellow-600'
                                : 'text-gray-400 hover:text-red-600'
                            }`}
                            title={
                              user._count?.clients && user._count.clients > 0
                                ? `Cannot delete - has ${user._count?.clients ?? 0} associated client(s)`
                                : undefined
                            }
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {users.length === 0 ? (
            <div className="col-span-full text-center py-12 text-gray-500">
              {debouncedSearchQuery || roleFilter || statusFilter || emailVerifiedFilter ? 'No users found matching your criteria.' : 'No users found.'}
            </div>
          ) : (
            users.map((user) => (
              <div
                key={user.id}
                className={`bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow ${
                  selectedUsers.includes(user.id.toString()) ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
              >
                {/* Header with checkbox and actions */}
                <div className="flex items-center justify-between mb-3">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id.toString())}
                    onChange={() => handleSelectUser(user.id.toString())}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => handleAction('view', user)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="View"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleAction('edit', user)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      title="Edit"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleAction('toggle-status', user)}
                      className={`text-gray-400 hover:text-yellow-600 transition-colors ${
                        user.isactive ? 'text-green-600' : ''
                      }`}
                      title={user.isactive ? 'Deactivate' : 'Activate'}
                    >
                      <PowerIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleAction('delete', user)}
                      className={`transition-colors ${
                        user._count?.clients && user._count.clients > 0
                          ? 'text-yellow-400 hover:text-yellow-600'
                          : 'text-gray-400 hover:text-red-600'
                      }`}
                      title={
                        user._count?.clients && user._count.clients > 0
                          ? `Cannot delete - has ${user._count?.clients ?? 0} associated client(s)`
                          : undefined
                      }
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* User Avatar and Info */}
                <div className="text-center mb-3">
                  <UserAvatar user={user} size="lg" className="mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900">
                    {user.firstname && user.lastname ? `${user.firstname} ${user.lastname}` : user.email}
                  </h3>
                  <p className="text-sm text-gray-500">{user.email}</p>
                </div>

                {/* User Details */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Role:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.role === 'ADMIN' ? 'bg-red-100 text-red-800' :
                      user.role === 'CLIENT' ? 'bg-green-100 text-green-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {user.role}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Status:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded ${
                      user.isactive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.isactive ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">Email Verified:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      user.emailverified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {user.emailverified ? 'Verified' : 'Unverified'}
                    </span>
                  </div>

                  {user.updatedat && (
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Last Active:</span>
                      <span className="text-xs text-gray-900">{formatDate(user.updatedat)}</span>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      ) : (
        // Card View - Professional grid layout with essential info only
        <div className={`grid gap-4 px-2 ${
          displayDensity === 'compact'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
        }`}>
          {users.length === 0 ? (
            <div className="col-span-full text-center py-12 text-gray-500">
              {debouncedSearchQuery || roleFilter || statusFilter || emailVerifiedFilter ? 'No users found matching your criteria.' : 'No users found.'}
            </div>
          ) : (
            users.map((user) => (
              <div
                key={user.id}
                className={`bg-white rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 border ${
                  displayDensity === 'compact' ? 'h-64' : 'h-80'
                } ${
                  selectedUsers.includes(user.id.toString())
                    ? 'border-blue-500 ring-2 ring-blue-100 shadow-md'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                <div className="flex h-full">
                  {/* Photo Section - Left Half */}
                  <div className="w-1/2 relative">
                    <UserAvatar
                      user={user}
                      size="full-height"
                      className="shadow-none"
                      style={{
                        width: '100%',
                        height: '100%'
                      }}
                    />

                    {/* Checkbox Overlay */}
                    <div className="absolute top-2 left-2">
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user.id.toString())}
                        onChange={() => handleSelectUser(user.id.toString())}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded shadow-sm bg-white"
                      />
                    </div>
                  </div>

                  {/* Content Section - Right Half */}
                  <div className={`w-1/2 flex flex-col ${
                    displayDensity === 'compact' ? 'p-5' : 'p-8'
                  }`}>
                    {/* User Information */}
                    <div className="space-y-2 mb-2 flex-1">
                      {/* Name */}
                      <h3 className={`font-semibold text-gray-900 leading-tight ${
                        displayDensity === 'compact' ? 'text-sm' : 'text-base'
                      }`} title={user.firstname && user.lastname ? `${user.firstname} ${user.lastname}` : user.email}>
                        {user.firstname && user.lastname
                          ? `${user.firstname} ${user.lastname}`.length > 18
                            ? `${user.firstname} ${user.lastname}`.substring(0, 18) + '...'
                            : `${user.firstname} ${user.lastname}`
                          : user.email.length > 18
                            ? user.email.substring(0, 18) + '...'
                            : user.email
                        }
                      </h3>

                      {/* Email */}
                      <p className={`text-blue-600 font-medium leading-tight ${
                        displayDensity === 'compact' ? 'text-xs' : 'text-sm'
                      }`} title={user.email}>
                        {user.email.length > 20 ? user.email.substring(0, 20) + '...' : user.email}
                      </p>

                      {/* Role */}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 font-medium">Role</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded ${
                          user.role === 'ADMIN' ? 'bg-red-100 text-red-700' :
                          user.role === 'CLIENT' ? 'bg-green-100 text-green-700' :
                          'bg-blue-100 text-blue-700'
                        }`}>
                          {user.role}
                        </span>
                      </div>

                      {/* Status */}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 font-medium">Status</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded ${
                          user.isactive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                        }`}>
                          {user.isactive ? 'Active' : 'Inactive'}
                        </span>
                      </div>

                      {/* Created Date */}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 font-medium">Created</span>
                        <span className="text-xs text-gray-700 font-medium">
                          {user.createdat ? formatDate(user.createdat) : 'N/A'}
                        </span>
                      </div>
                    </div>

                    {/* Action Buttons - moved up */}
                    <div className="flex items-center justify-between pt-2 border-t border-gray-100 mt-2">
                      <button
                        onClick={() => handleAction('view', user)}
                        className="p-2 rounded-md transition-colors text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                        title="View User"
                      >
                        <EyeIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleAction('edit', user)}
                        className="p-2 rounded-md transition-colors text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                        title="Edit User"
                      >
                        <PencilIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleAction('toggle-status', user)}
                        className={`p-2 rounded-md transition-colors ${
                          user.isactive
                            ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                            : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                        }`}
                        title={user.isactive ? 'Deactivate User' : 'Activate User'}
                      >
                        <PowerIcon className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleAction('delete', user)}
                        className={`p-2 rounded-md transition-colors ${
                          user._count?.clients && user._count.clients > 0
                            ? 'text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50'
                            : 'text-red-600 hover:text-red-800 hover:bg-red-50'
                        }`}
                        title={
                          user._count?.clients && user._count.clients > 0
                            ? `Cannot delete - has ${user._count?.clients ?? 0} associated client(s)`
                            : undefined
                        }
                      >
                        <TrashIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Create Modal */}
      <UserModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create User"
      />

      {/* Edit Modal */}
      <UserModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingUser(null)
        }}
        onSubmit={(formData) =>
          editingUser
            ? handleUpdate(editingUser.id.toString(), formData)
            : Promise.resolve()
        }
        title="Edit User"
        initialData={editingUser}
      />
    </div>
  )
}
