'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import {
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ArrowPathIcon,
  DocumentTextIcon,
  PencilIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  PlusIcon,
  FunnelIcon,
  ListBulletIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  EyeSlashIcon,
  PowerIcon
} from '@heroicons/react/24/outline'
import { EnhancedPaymentFormModalWrapper as EnhancedPaymentFormModal } from './enhanced-payment-form-modal'
import { PaymentRow } from './payment-row'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
}

interface Invoice {
  id: string | number
  clientId: string | number
  projectId?: string | number
  totalAmount: number
  status: string
  dueDate: string
  description?: string
  _count?: {
    payments: number
  }
}

interface Payment {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  invoiceId: string | number
  createdAt: string
  updatedAt?: string
  currency?: string
  transactionId?: string
  processingFee?: number
  netAmount?: number
  refundedAmount?: number
  stripePaymentIntentId?: string
  paypalOrderId?: string
  promoCode?: string
  discountAmount?: number
  billingAddress?: any
  receiptEmail?: string
  metadata?: any
}

interface EnhancedPaymentManagementProps {
  client: Client
  project: Project
  invoice: Invoice
  selectedPayment: Payment | null
  onPaymentSelect: (payment: Payment | null) => void
}

export function EnhancedPaymentManagement({ client, project, invoice, selectedPayment, onPaymentSelect }: EnhancedPaymentManagementProps) {
  // State management
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  
  // Enhanced controls state
  const [selectedPayments, setSelectedPayments] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'amount', 'status', 'paymentDate', 'paymentMethod', 'notes', 'updatedAt'
  ])
  const [sortBy, setSortBy] = useState('paymentDate')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnControls, setShowColumnControls] = useState(false)
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  
  // Modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)
  
  // Refs
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Available columns configuration
  const availableColumns = [
    { key: 'amount', label: 'Amount', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'paymentDate', label: 'Payment Date', sortable: true },
    { key: 'paymentMethod', label: 'Payment Method', sortable: true },
    { key: 'notes', label: 'Notes', sortable: false },
    { key: 'currency', label: 'Currency', sortable: true },
    { key: 'transactionId', label: 'Transaction ID', sortable: false },
    { key: 'processingFee', label: 'Processing Fee', sortable: true },
    { key: 'netAmount', label: 'Net Amount', sortable: true },
    { key: 'createdAt', label: 'Created', sortable: true },
    { key: 'updatedAt', label: 'Updated', sortable: true }
  ]

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch payments when dependencies change
  useEffect(() => {
    if (client && project && invoice) {
      fetchPayments()
    }
  }, [client, project, invoice, debouncedSearchQuery, sortBy, sortOrder, filters, currentPage, pageSize])

  // Utility functions
  const fetchPayments = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sortBy,
        sortOrder,
      })

      if (debouncedSearchQuery) {
        params.append('search', debouncedSearchQuery)
      }

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch payments: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setPayments(data.data || [])
        setTotalPages(Math.ceil((data.total || 0) / pageSize))
      } else {
        throw new Error(data.error || 'Failed to fetch payments')
      }
    } catch (err) {
      console.error('Error fetching payments:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setPayments([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = async (data: any) => {
    try {
      setActionLoading('create')
      const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('Failed to create payment')
      }

      await fetchPayments()
      setIsCreateModalOpen(false)
    } catch (error) {
      console.error('Error creating payment:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleUpdate = async (id: string, data: any) => {
    try {
      setActionLoading(`edit-${id}`)
      const response = await fetch(`/api/admin/payments/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error('Failed to update payment')
      }

      await fetchPayments()
      setIsEditModalOpen(false)
      setEditingPayment(null)
    } catch (error) {
      console.error('Error updating payment:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this payment?')) return

    try {
      setActionLoading(`delete-${id}`)
      const response = await fetch(`/api/admin/payments/${id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete payment')
      }

      await fetchPayments()
    } catch (error) {
      console.error('Error deleting payment:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleAction = (action: string, payment: Payment) => {
    switch (action) {
      case 'edit':
        setEditingPayment(payment)
        setIsEditModalOpen(true)
        break
      case 'delete':
        handleDelete(String(payment.id))
        break
      case 'refund':
        // TODO: Implement refund functionality
        console.log('Refund payment:', payment.id)
        break
      case 'download':
        // TODO: Implement download functionality
        console.log('Download receipt:', payment.id)
        break
      default:
        break
    }
  }

  const handleBulkAction = async (action: string, paymentIds: string[]) => {
    try {
      setActionLoading(`bulk-${action}`)
      
      const response = await fetch('/api/admin/payments/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, paymentIds })
      })

      if (!response.ok) {
        throw new Error(`Failed to ${action} payments`)
      }

      await fetchPayments()
      setSelectedPayments([])
    } catch (error) {
      console.error(`Error ${action} payments:`, error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleSelectPayment = (paymentId: string) => {
    setSelectedPayments(prev =>
      prev.includes(paymentId)
        ? prev.filter(id => id !== paymentId)
        : [...prev, paymentId]
    )
  }

  const handleSelectAll = () => {
    if (selectedPayments.length === payments.length) {
      setSelectedPayments([])
    } else {
      setSelectedPayments(payments.map(p => String(p.id)))
    }
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    setCurrentPage(1)
  }

  const clearFilters = () => {
    setFilters({})
    setSearchQuery('')
    setCurrentPage(1)
  }

  const toggleColumnVisibility = (columnKey: string) => {
    setVisibleColumns(prev =>
      prev.includes(columnKey)
        ? prev.filter(key => key !== columnKey)
        : [...prev, columnKey]
    )
  }

  // Utility functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-blue-100 text-blue-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return <CheckCircleIcon className="h-3 w-3" />
      case 'pending':
        return <ClockIcon className="h-3 w-3" />
      case 'failed':
        return <XCircleIcon className="h-3 w-3" />
      case 'refunded':
        return <ArrowPathIcon className="h-3 w-3" />
      case 'cancelled':
        return <XCircleIcon className="h-3 w-3" />
      default:
        return <ClockIcon className="h-3 w-3" />
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method?.toLowerCase()) {
      case 'card':
      case 'credit_card':
      case 'stripe':
        return <CreditCardIcon className="h-4 w-4" />
      case 'paypal':
        return <CurrencyDollarIcon className="h-4 w-4" />
      case 'bank_transfer':
        return <DocumentTextIcon className="h-4 w-4" />
      default:
        return <CreditCardIcon className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <div className="text-center">
            <XCircleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error loading payments</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
            <div className="mt-6">
              <button
                onClick={fetchPayments}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Payment Management</h3>
            <p className="mt-1 text-sm text-gray-500">
              Manage payments for invoice #{invoice.id} - {client.companyName} / {project.name}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Payment
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Controls Bar */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search payments..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>

            {/* Filters Toggle */}
            <div className="relative">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                  showFilters || Object.values(filters).some(v => v)
                    ? 'bg-blue-50 text-blue-700 border-blue-300'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                <FunnelIcon className="h-4 w-4 mr-2" />
                Filters
                {Object.values(filters).some(v => v) && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {Object.values(filters).filter(v => v).length}
                  </span>
                )}
              </button>

              {/* Filters Panel */}
              {showFilters && (
                <div className="absolute top-full left-0 mt-2 w-80 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900">Filters</h4>
                      <button
                        onClick={clearFilters}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        Clear all
                      </button>
                    </div>

                    {/* Status Filter */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        value={filters.status || ''}
                        onChange={(e) => handleFilterChange('status', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">All statuses</option>
                        <option value="completed">Completed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                        <option value="refunded">Refunded</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>

                    {/* Payment Method Filter */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Method
                      </label>
                      <select
                        value={filters.paymentMethod || ''}
                        onChange={(e) => handleFilterChange('paymentMethod', e.target.value)}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">All methods</option>
                        <option value="card">Card</option>
                        <option value="paypal">PayPal</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="crypto">Cryptocurrency</option>
                        <option value="apple_pay">Apple Pay</option>
                        <option value="google_pay">Google Pay</option>
                      </select>
                    </div>

                    {/* Amount Range Filter */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Amount Range
                      </label>
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="number"
                          placeholder="Min"
                          value={filters.minAmount || ''}
                          onChange={(e) => handleFilterChange('minAmount', e.target.value)}
                          className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                        <input
                          type="number"
                          placeholder="Max"
                          value={filters.maxAmount || ''}
                          onChange={(e) => handleFilterChange('maxAmount', e.target.value)}
                          className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-4">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-white border border-gray-300 rounded-md">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 text-sm font-medium transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List View"
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 text-sm font-medium transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Grid View"
              >
                <Squares2X2Icon className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('card')}
                className={`p-2 text-sm font-medium transition-colors ${
                  viewMode === 'card'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Card View"
              >
                <RectangleStackIcon className="h-4 w-4" />
              </button>
            </div>

            {/* Display Density */}
            <div className="flex items-center bg-white border border-gray-300 rounded-md">
              <button
                onClick={() => setDisplayDensity('compact')}
                className={`px-3 py-2 text-xs font-medium transition-colors ${
                  displayDensity === 'compact'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Compact
              </button>
              <button
                onClick={() => setDisplayDensity('comfortable')}
                className={`px-3 py-2 text-xs font-medium transition-colors ${
                  displayDensity === 'comfortable'
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Comfortable
              </button>
            </div>

            {/* Column Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnControls(!showColumnControls)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium bg-white text-gray-700 hover:bg-gray-50"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                Columns
              </button>

              {showColumnControls && (
                <div className="absolute top-full right-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="p-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-3">Show/Hide Columns</h4>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {availableColumns.map((column) => (
                        <label key={column.key} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={visibleColumns.includes(column.key)}
                            onChange={() => toggleColumnVisibility(column.key)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {selectedPayments.length > 0 && (
        <div className="px-6 py-3 bg-blue-50 border-t border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-sm font-medium text-blue-900">
                {selectedPayments.length} payment{selectedPayments.length !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedPayments([])}
                className="text-sm text-blue-700 hover:text-blue-900"
              >
                Clear selection
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleBulkAction('refund', selectedPayments)}
                disabled={actionLoading?.startsWith('bulk-')}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 disabled:opacity-50"
              >
                <ArrowPathIcon className="h-3 w-3 mr-1" />
                Refund
              </button>
              <button
                onClick={() => handleBulkAction('download', selectedPayments)}
                disabled={actionLoading?.startsWith('bulk-')}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50"
              >
                <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
                Download
              </button>
              <button
                onClick={() => {
                  if (confirm(`Are you sure you want to delete ${selectedPayments.length} payment${selectedPayments.length !== 1 ? 's' : ''}?`)) {
                    handleBulkAction('delete', selectedPayments)
                  }
                }}
                disabled={actionLoading?.startsWith('bulk-')}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
              >
                <TrashIcon className="h-3 w-3 mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Stats Overview */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{payments.length}</div>
            <div className="text-sm text-gray-600">Total Payments</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {payments.filter(p => p.status === 'completed').length}
            </div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {payments.filter(p => p.status === 'pending').length}
            </div>
            <div className="text-sm text-gray-600">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {payments.reduce((sum, p) => sum + p.amount, 0).toLocaleString('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
              })}
            </div>
            <div className="text-sm text-gray-600">Total Amount</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {payments.length === 0 ? (
          <div className="text-center py-12">
            <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No payments found</h3>
            <p className="mt-1 text-sm text-gray-500">
              {debouncedSearchQuery || Object.values(filters).some(v => v)
                ? 'Try adjusting your search or filters'
                : 'Get started by creating a new payment for this invoice'
              }
            </p>
            {!debouncedSearchQuery && !Object.values(filters).some(v => v) && (
              <div className="mt-6">
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Payment
                </button>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* List View */}
            {viewMode === 'list' && (
              <div className="overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left">
                        <input
                          type="checkbox"
                          checked={selectedPayments.length === payments.length && payments.length > 0}
                          onChange={handleSelectAll}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                      </th>
                      {availableColumns
                        .filter(col => visibleColumns.includes(col.key))
                        .map((column) => (
                          <th
                            key={column.key}
                            className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                            onClick={() => column.sortable && handleSort(column.key)}
                          >
                            <div className="flex items-center space-x-1">
                              <span>{column.label}</span>
                              {column.sortable && sortBy === column.key && (
                                sortOrder === 'asc' ? (
                                  <ArrowUpIcon className="h-3 w-3" />
                                ) : (
                                  <ArrowDownIcon className="h-3 w-3" />
                                )
                              )}
                            </div>
                          </th>
                        ))}
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {payments.map((payment) => (
                      <PaymentRow
                        key={payment.id}
                        payment={payment}
                        isSelected={selectedPayments.includes(String(payment.id))}
                        onSelect={() => handleSelectPayment(String(payment.id))}
                        onAction={(action) => handleAction(action, payment)}
                        onPaymentSelect={onPaymentSelect}
                        selectedPayment={selectedPayment}
                        visibleColumns={visibleColumns}
                        displayDensity={displayDensity}
                        viewMode={viewMode}
                        actionLoading={actionLoading}
                        formatDate={formatDate}
                        formatCurrency={formatCurrency}
                        getStatusColor={getStatusColor}
                        getStatusIcon={getStatusIcon}
                        getPaymentMethodIcon={getPaymentMethodIcon}
                      />
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Grid View */}
            {viewMode === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {payments.map((payment) => (
                  <PaymentRow
                    key={payment.id}
                    payment={payment}
                    isSelected={selectedPayments.includes(String(payment.id))}
                    onSelect={() => handleSelectPayment(String(payment.id))}
                    onAction={(action) => handleAction(action, payment)}
                    onPaymentSelect={onPaymentSelect}
                    selectedPayment={selectedPayment}
                    visibleColumns={visibleColumns}
                    displayDensity={displayDensity}
                    viewMode={viewMode}
                    actionLoading={actionLoading}
                    formatDate={formatDate}
                    formatCurrency={formatCurrency}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPaymentMethodIcon={getPaymentMethodIcon}
                  />
                ))}
              </div>
            )}

            {/* Card View */}
            {viewMode === 'card' && (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <PaymentRow
                    key={payment.id}
                    payment={payment}
                    isSelected={selectedPayments.includes(String(payment.id))}
                    onSelect={() => handleSelectPayment(String(payment.id))}
                    onAction={(action) => handleAction(action, payment)}
                    onPaymentSelect={onPaymentSelect}
                    selectedPayment={selectedPayment}
                    visibleColumns={visibleColumns}
                    displayDensity={displayDensity}
                    viewMode={viewMode}
                    actionLoading={actionLoading}
                    formatDate={formatDate}
                    formatCurrency={formatCurrency}
                    getStatusColor={getStatusColor}
                    getStatusIcon={getStatusIcon}
                    getPaymentMethodIcon={getPaymentMethodIcon}
                  />
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Show</span>
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value))
                      setCurrentPage(1)
                    }}
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value={10}>10</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                  <span className="text-sm text-gray-700">per page</span>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-1 border rounded-md text-sm font-medium ${
                            currentPage === page
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      )
                    })}
                  </div>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      {isCreateModalOpen && (
        <EnhancedPaymentFormModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSubmit={handleCreate}
          invoice={invoice}
          client={client}
          project={project}
        />
      )}

      {isEditModalOpen && editingPayment && (
        <EnhancedPaymentFormModal
          isOpen={isEditModalOpen}
          onClose={() => {
            setIsEditModalOpen(false)
            setEditingPayment(null)
          }}
          onSubmit={(data) => handleUpdate(String(editingPayment.id), data)}
          invoice={invoice}
          client={client}
          project={project}
          initialData={editingPayment}
        />
      )}
    </div>
  )
}
