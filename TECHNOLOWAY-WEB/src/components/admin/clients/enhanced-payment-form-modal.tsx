'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Elements } from '@stripe/react-stripe-js'
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import axios from 'axios'
import {
  XMarkIcon,
  CreditCardIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  ChevronDownIcon,
  ShieldCheckIcon,
  LockClosedIcon,
  EnvelopeIcon,
  TagIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { stripePromise, CURRENCIES, DEFAULT_CURRENCY } from '@/services/payment/stripe'
import { paymentFormSchema, PaymentFormData, PROMO_CODES } from '@/services/payment/payment-schema'
import { transformPaymentFormData, validatePaymentMethodFields, calculateProcessingFee } from '@/services/payment/payment-utils'

interface EnhancedPaymentFormModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: {
    id: string | number
    totalAmount: number
    description?: string
    status?: string
    dueDate?: string
  }
  project?: {
    id: string | number
    name: string
  }
  client?: {
    id: string | number
    companyName: string
    contactEmail?: string
  }
  onSubmit: (paymentData: any) => void
}

interface PaymentMethod {
  id: string
  name: string
  icon: React.ComponentType<{ className?: string }>
  description: string
  processingFee?: string
  processingTime: string
  requiresStripe?: boolean
  fields: string[]
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'stripe_card',
    name: 'Credit/Debit Card',
    icon: CreditCardIcon,
    description: 'Visa, Mastercard, American Express',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: ['cardNumber', 'expiryDate', 'cvv', 'cardholderName']
  },
  {
    id: 'paypal',
    name: 'PayPal',
    icon: GlobeAltIcon,
    description: 'PayPal account or guest checkout',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    fields: ['paypalEmail']
  },
  {
    id: 'bank_transfer',
    name: 'Bank Transfer',
    icon: BuildingLibraryIcon,
    description: 'Direct bank account transfer',
    processingFee: '$0.80',
    processingTime: '1-3 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'wire_transfer',
    name: 'Wire Transfer',
    icon: BuildingLibraryIcon,
    description: 'International wire transfer',
    processingFee: '$15-25',
    processingTime: '1-5 business days',
    fields: ['bankName', 'swiftCode', 'accountNumber', 'accountHolderName', 'bankAddress']
  },
  {
    id: 'ach_transfer',
    name: 'ACH Transfer',
    icon: BuildingLibraryIcon,
    description: 'Automated Clearing House',
    processingFee: '$0.80',
    processingTime: '3-5 business days',
    fields: ['bankName', 'accountNumber', 'routingNumber', 'accountHolderName']
  },
  {
    id: 'apple_pay',
    name: 'Apple Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Apple Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'google_pay',
    name: 'Google Pay',
    icon: DevicePhoneMobileIcon,
    description: 'Google Pay digital wallet',
    processingFee: '2.9% + $0.30',
    processingTime: 'Instant',
    requiresStripe: true,
    fields: []
  },
  {
    id: 'check',
    name: 'Check',
    icon: BanknotesIcon,
    description: 'Physical or electronic check',
    processingTime: '3-7 business days',
    fields: ['checkNumber', 'bankName']
  },
  {
    id: 'cash',
    name: 'Cash',
    icon: BanknotesIcon,
    description: 'Cash payment in person',
    processingTime: 'Instant',
    fields: []
  }
]

export function EnhancedPaymentFormModal({
  isOpen,
  onClose,
  invoice,
  project,
  client,
  onSubmit
}: EnhancedPaymentFormModalProps) {
  const stripe = useStripe()
  const elements = useElements()
  const [loading, setLoading] = useState(false)
  const [remainingAmount, setRemainingAmount] = useState(0)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>(paymentMethods[0])
  const [appliedPromo, setAppliedPromo] = useState<any>(null)
  const [clientSecret, setClientSecret] = useState('')
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)
  const [stripeLoading, setStripeLoading] = useState(false)
  const [creatingPaymentIntent, setCreatingPaymentIntent] = useState(false)

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: {
      amount: 0,
      currency: DEFAULT_CURRENCY,
      paymentMethod: 'stripe_card',
      paymentDate: new Date().toISOString().split('T')[0],
      notes: '',
      promoCode: '',
      emailReceipt: false,
      receiptEmail: client?.contactEmail || '',
      termsAccepted: false
    }
  })

  const watchedAmount = watch('amount')
  const watchedCurrency = watch('currency')
  const watchedPromoCode = watch('promoCode')

  // Calculate remaining amount
  useEffect(() => {
    const fetchRemainingAmount = async () => {
      try {
        const response = await fetch(`/api/admin/invoices-new/${invoice.id}/payments`)
        if (response.ok) {
          const data = await response.json()
          const totalPaid = data.data?.reduce((sum: number, payment: any) => sum + Number(payment.amount), 0) || 0
          setRemainingAmount(Number(invoice.totalAmount) - totalPaid)
          setValue('amount', Number(invoice.totalAmount) - totalPaid)
        } else {
          setRemainingAmount(Number(invoice.totalAmount))
          setValue('amount', Number(invoice.totalAmount))
        }
      } catch (error) {
        console.error('Error fetching payments:', error)
        setRemainingAmount(Number(invoice.totalAmount))
        setValue('amount', Number(invoice.totalAmount))
      }
    }

    if (isOpen) {
      fetchRemainingAmount()
    }
  }, [isOpen, invoice.id, invoice.totalAmount, setValue])

  // Handle promo code validation
  useEffect(() => {
    if (watchedPromoCode) {
      const promo = PROMO_CODES.find(p => p.code === watchedPromoCode.toUpperCase())
      setAppliedPromo(promo || null)
    } else {
      setAppliedPromo(null)
    }
  }, [watchedPromoCode])

  // Auto-create payment intent for Stripe payments
  useEffect(() => {
    if (isOpen && selectedPaymentMethod.requiresStripe && watchedAmount > 0) {
      createPaymentIntent()
    }
  }, [isOpen, selectedPaymentMethod.id, watchedAmount])

  // Create payment intent function
  const createPaymentIntent = async (): Promise<string | null> => {
    setCreatingPaymentIntent(true)
    try {
      const totalAmount = calculateTotal()

      // Validate minimum amount for Stripe
      if (totalAmount < 0.50) {
        throw new Error(`Payment amount must be at least $0.50 USD. Current total: $${totalAmount.toFixed(2)}`)
      }

      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: totalAmount,
          currency: watchedCurrency,
          metadata: {
            invoiceId: invoice.id,
            projectId: project?.id || '',
            clientId: client?.id || '',
            paymentMethod: selectedPaymentMethod.id,
            promoCode: appliedPromo?.code || '',
            discount: appliedPromo ? (appliedPromo.type === 'percentage'
              ? ((typeof watchedAmount === 'string' ? parseFloat(watchedAmount) || 0 : watchedAmount || 0) * (typeof appliedPromo.discount === 'string' ? parseFloat(appliedPromo.discount) || 0 : appliedPromo.discount || 0) / 100)
              : (typeof appliedPromo.discount === 'string' ? parseFloat(appliedPromo.discount) || 0 : appliedPromo.discount || 0)) : 0
          }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create payment intent')
      }

      const result = await response.json()
      const { clientSecret, mock } = result

      if (mock) {
        console.log('Mock payment mode - Stripe not configured')
        setErrorMessage('Payment system is in development mode. Configure Stripe keys for real payments.')
      }

      setClientSecret(clientSecret)
      return clientSecret
    } catch (error) {
      console.log('Error creating payment intent:', error)
      setErrorMessage('Failed to initialize payment. Please try again.')
      return null
    } finally {
      setCreatingPaymentIntent(false)
    }
  }

  const calculateTotal = () => {
    let total = typeof watchedAmount === 'string' ? parseFloat(watchedAmount) || 0 : watchedAmount || 0
    
    if (appliedPromo) {
      const discount = typeof appliedPromo.discount === 'string' ? parseFloat(appliedPromo.discount) || 0 : appliedPromo.discount || 0
      if (appliedPromo.type === 'percentage') {
        total = total * (1 - discount / 100)
      } else {
        total = Math.max(0, total - discount)
      }
    }

    // Add processing fee
    if (selectedPaymentMethod.processingFee) {
      const fee = selectedPaymentMethod.processingFee
      if (fee.includes('%')) {
        const percentage = parseFloat(fee.match(/(\d+\.?\d*)%/)?.[1] || '0')
        const fixed = parseFloat(fee.match(/\$(\d+\.?\d*)/)?.[1] || '0')
        total = total * (1 + percentage / 100) + fixed
      } else if (fee.includes('$')) {
        const fixed = parseFloat(fee.match(/\$(\d+\.?\d*)/)?.[1] || '0')
        total = total + fixed
      }
    }

    return total
  }

  const formatCurrency = (amount: number | string, currency: string = DEFAULT_CURRENCY) => {
    const currencyData = CURRENCIES.find(c => c.code === currency)
    const numericAmount = typeof amount === 'string' ? parseFloat(amount) || 0 : amount || 0
    return `${currencyData?.symbol || '$'}${numericAmount.toFixed(2)}`
  }

  const handleFormSubmit = async (data: any) => {
    setLoading(true)
    setErrorMessage(null)
    setSuccessMessage(null)

    try {
      // Handle Stripe payments
      if (selectedPaymentMethod.requiresStripe) {
        if (!stripe || !elements) {
          setErrorMessage('Stripe is not properly loaded. Please refresh the page and try again.')
          setLoading(false)
          return
        }

        if (!clientSecret) {
          setErrorMessage('Payment not properly initialized. Please try again.')
          setLoading(false)
          return
        }

        setStripeLoading(true)

        // Get the CardElement
        const cardElement = elements.getElement('card')
        if (!cardElement) {
          setErrorMessage('Card information is required.')
          setLoading(false)
          setStripeLoading(false)
          return
        }

        // Confirm the payment with Stripe
        const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
          payment_method: {
            card: cardElement,
          }
        })

        setStripeLoading(false)

        if (error) {
          console.log('Stripe payment failed:', error)
          setErrorMessage(error.message || 'Payment failed. Please try again.')
          setLoading(false)
          return
        }

        if (paymentIntent && paymentIntent.status === 'succeeded') {
          // Payment successful, save to database
          const paymentData = transformPaymentFormData(
            {
              ...data,
              amount: calculateTotal(),
              paymentDate: new Date().toISOString().split('T')[0],
              notes: `Stripe payment - ${paymentIntent.id}`,
              status: 'completed'
            },
            selectedPaymentMethod,
            appliedPromo,
            invoice,
            project,
            client
          )

          // Add Stripe specific data
          paymentData.processingFee = calculateProcessingFee(selectedPaymentMethod.id, paymentData.amount)
          paymentData.paymentType = 'stripe_card'
          paymentData.stripePaymentIntentId = paymentIntent.id
          paymentData.stripeStatus = paymentIntent.status
          paymentData.clientSecret = clientSecret

          // Save to database
          await onSubmit(paymentData)
          setSuccessMessage('Payment processed successfully!')

          // Reset form and close modal after delay
          reset()
          setTimeout(() => {
            onClose()
          }, 2000)
          setLoading(false)
          return
        } else {
          setErrorMessage('Payment was not completed successfully. Please try again.')
          setLoading(false)
          return
        }
      }

      // Collect payment method specific details from form
      const formElement = document.querySelector('form')
      const formFieldData: any = { ...data }

      if (formElement) {
        const formData = new FormData(formElement)
        selectedPaymentMethod.fields.forEach((field: string) => {
          const value = formData.get(field) as string
          if (value) {
            formFieldData[field] = value
          }
        })
      }

      // Validate payment method specific fields
      const validation = validatePaymentMethodFields(selectedPaymentMethod, formFieldData)
      if (!validation.isValid) {
        setErrorMessage(`Please fix the following errors: ${validation.errors.join(', ')}`)
        return
      }

      // Transform form data to payment API format
      const paymentData = transformPaymentFormData(
        { ...formFieldData, amount: calculateTotal() },
        selectedPaymentMethod,
        appliedPromo,
        invoice,
        project,
        client
      )

      // Add processing fee
      paymentData.processingFee = calculateProcessingFee(selectedPaymentMethod.id, paymentData.amount)

      await onSubmit(paymentData)
      setSuccessMessage('Payment processed successfully!')
      reset()
      setTimeout(() => {
        onClose()
      }, 2000)
    } catch (error) {
      console.log('Payment submission error:', error)
      setErrorMessage(error instanceof Error ? error.message : 'Failed to process payment. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative w-full max-w-4xl bg-white rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">Secure Payment</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Invoice #{invoice.id} - {project?.name} ({client?.companyName})
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <XMarkIcon className="h-6 w-6 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                {/* Success Message */}
                {successMessage && (
                  <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-green-800">{successMessage}</p>
                      </div>
                      <div className="ml-auto pl-3">
                        <div className="-mx-1.5 -my-1.5">
                          <button
                            type="button"
                            onClick={() => setSuccessMessage(null)}
                            className="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {errorMessage && (
                  <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-red-800">{errorMessage}</p>
                      </div>
                      <div className="ml-auto pl-3">
                        <div className="-mx-1.5 -my-1.5">
                          <button
                            type="button"
                            onClick={() => setErrorMessage(null)}
                            className="inline-flex bg-red-50 rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-red-50 focus:ring-red-600"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}



                {/* Amount and Currency */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Amount *
                    </label>
                    <Controller
                      name="amount"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <CurrencyDollarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          <input
                            {...field}
                            type="number"
                            step="0.01"
                            min="0"
                            max={remainingAmount}
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            placeholder="0.00"
                          />
                        </div>
                      )}
                    />
                    {errors.amount && (
                      <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Currency
                    </label>
                    <Controller
                      name="currency"
                      control={control}
                      render={({ field }) => (
                        <select
                          {...field}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        >
                          {CURRENCIES.map((currency) => (
                            <option key={currency.code} value={currency.code}>
                              {currency.symbol} {currency.name} ({currency.code})
                            </option>
                          ))}
                        </select>
                      )}
                    />
                  </div>
                </div>

                {/* Payment Method Selection */}
                <div className="relative payment-method-dropdown">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    Payment Method *
                  </label>
                  <div className="relative">
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="relative w-full cursor-pointer rounded-lg border border-gray-300 bg-white py-3 pl-4 pr-10 text-left shadow-sm focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                    >
                      <div className="flex items-center">
                        <selectedPaymentMethod.icon className="h-5 w-5 text-gray-400 mr-3" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="block truncate font-medium text-gray-900">
                              {selectedPaymentMethod.name}
                            </span>
                            {selectedPaymentMethod.processingFee && (
                              <span className="text-sm text-gray-500 ml-2">
                                {selectedPaymentMethod.processingFee}
                              </span>
                            )}
                          </div>
                          <span className="block truncate text-sm text-gray-500">
                            {selectedPaymentMethod.description} • {selectedPaymentMethod.processingTime}
                          </span>
                        </div>
                      </div>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronDownIcon
                          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${
                            isDropdownOpen ? 'rotate-180' : ''
                          }`}
                        />
                      </span>
                    </button>

                    {/* Dropdown Options */}
                    {isDropdownOpen && (
                      <div className="absolute z-10 mt-1 w-full rounded-lg bg-white shadow-lg border border-gray-200 max-h-60 overflow-auto">
                        {paymentMethods.map((method) => (
                          <button
                            key={method.id}
                            type="button"
                            onClick={() => {
                              setSelectedPaymentMethod(method)
                              setIsDropdownOpen(false)
                            }}
                            className={`w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none ${
                              selectedPaymentMethod.id === method.id ? 'bg-purple-50 border-l-4 border-purple-500' : ''
                            }`}
                          >
                            <div className="flex items-center">
                              <method.icon className="h-5 w-5 text-gray-400 mr-3" />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-gray-900">{method.name}</span>
                                  {method.processingFee && (
                                    <span className="text-sm text-gray-500">{method.processingFee}</span>
                                  )}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {method.description} • {method.processingTime}
                                </div>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Stripe Payment Loading */}
                {selectedPaymentMethod.requiresStripe && creatingPaymentIntent && (
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
                      <div className="text-sm text-blue-700">
                        <p className="font-medium">Initializing Secure Payment</p>
                        <p className="mt-1">Setting up Stripe payment intent...</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Stripe Payment Ready */}
                {selectedPaymentMethod.requiresStripe && clientSecret && !creatingPaymentIntent && (
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200 mb-4">
                    <div className="flex">
                      <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2 mt-0.5" />
                      <div className="text-sm text-green-700">
                        <p className="font-medium">Payment Ready</p>
                        <p className="mt-1">Stripe payment intent created successfully.</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Stripe Card Element */}
                {selectedPaymentMethod.requiresStripe && clientSecret && !creatingPaymentIntent && (
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h4 className="text-sm font-semibold text-gray-900 mb-3">
                      Secure Card Payment
                    </h4>
                    <div className="bg-white rounded-lg p-3 border border-gray-300">
                      <CardElement
                        options={{
                          style: {
                            base: {
                              fontSize: '16px',
                              color: '#424770',
                              '::placeholder': {
                                color: '#aab7c4',
                              },
                            },
                            invalid: {
                              color: '#9e2146',
                            },
                          },
                        }}
                      />
                    </div>
                    <div className="mt-2 text-xs text-gray-500 flex items-center">
                      <svg className="h-3 w-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                      </svg>
                      Secured by Stripe
                    </div>
                  </div>
                )}

                {/* Dynamic Payment Method Fields */}
                {((selectedPaymentMethod.requiresStripe && !clientSecret) || !selectedPaymentMethod.requiresStripe) && selectedPaymentMethod.fields.length > 0 && (
                  <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                    <h4 className="text-lg font-semibold text-gray-900 mb-4">
                      {selectedPaymentMethod.name} Details
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedPaymentMethod.fields.map((field) => (
                        <div key={field}>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            {field === 'cardNumber' && 'Card Number *'}
                            {field === 'expiryDate' && 'Expiry Date *'}
                            {field === 'cvv' && 'CVV *'}
                            {field === 'cardholderName' && 'Cardholder Name *'}
                            {field === 'paypalEmail' && 'PayPal Email'}
                            {field === 'bankName' && 'Bank Name *'}
                            {field === 'accountNumber' && 'Account Number *'}
                            {field === 'routingNumber' && 'Routing Number *'}
                            {field === 'accountHolderName' && 'Account Holder Name *'}
                            {field === 'swiftCode' && 'SWIFT Code *'}
                            {field === 'bankAddress' && 'Bank Address *'}
                            {field === 'checkNumber' && 'Check Number *'}
                          </label>
                          <input
                            name={field}
                            type={field === 'paypalEmail' ? 'email' : 'text'}
                            placeholder={
                              field === 'cardNumber' ? '1234 5678 9012 3456' :
                              field === 'expiryDate' ? 'MM/YY' :
                              field === 'cvv' ? '123' :
                              field === 'paypalEmail' ? '<EMAIL>' :
                              ''
                            }
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            required={field !== 'paypalEmail'}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Promo Code */}
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <TagIcon className="inline h-4 w-4 mr-1" />
                    Promo Code (Optional)
                  </label>
                  <Controller
                    name="promoCode"
                    control={control}
                    render={({ field }) => (
                      <div className="relative">
                        <input
                          {...field}
                          type="text"
                          placeholder="Enter promo code"
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent uppercase"
                        />
                        {appliedPromo && (
                          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <CheckCircleIcon className="h-5 w-5 text-green-500" />
                          </div>
                        )}
                      </div>
                    )}
                  />
                  {appliedPromo && (
                    <p className="mt-1 text-sm text-green-600">
                      ✓ {appliedPromo.type === 'percentage'
                        ? `${appliedPromo.discount}% discount applied`
                        : `$${appliedPromo.discount} discount applied`
                      }
                    </p>
                  )}
                </div>

                {/* Payment Date and Notes */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Payment Date *
                    </label>
                    <Controller
                      name="paymentDate"
                      control={control}
                      render={({ field }) => (
                        <div className="relative">
                          <CalendarIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          <input
                            {...field}
                            type="date"
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                          />
                        </div>
                      )}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      Notes (Optional)
                    </label>
                    <Controller
                      name="notes"
                      control={control}
                      render={({ field }) => (
                        <textarea
                          {...field}
                          rows={3}
                          placeholder="Add payment notes..."
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                        />
                      )}
                    />
                  </div>
                </div>

                {/* Email Receipt */}
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-start space-x-3">
                    <Controller
                      name="emailReceipt"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="checkbox"
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                      )}
                    />
                    <div className="flex-1">
                      <label className="text-sm font-medium text-gray-900">
                        Email payment receipt
                      </label>
                      <p className="text-sm text-gray-600">
                        Send a payment confirmation to the client's email address
                      </p>
                      {watch('emailReceipt') && (
                        <div className="mt-2">
                          <Controller
                            name="receiptEmail"
                            control={control}
                            render={({ field }) => (
                              <div className="relative">
                                <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                <input
                                  {...field}
                                  type="email"
                                  placeholder="<EMAIL>"
                                  className="w-full pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                                />
                              </div>
                            )}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Security Badges */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <ShieldCheckIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">SSL Secured</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <LockClosedIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">PCI-DSS Compliant</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">256-bit Encryption</span>
                    </div>
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Controller
                      name="termsAccepted"
                      control={control}
                      render={({ field }) => (
                        <input
                          {...field}
                          type="checkbox"
                          className="mt-1 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                      )}
                    />
                    <div className="flex-1">
                      <label className="text-sm font-medium text-gray-900">
                        I accept the Terms & Conditions *
                      </label>
                      <p className="text-sm text-gray-600 mt-1">
                        By proceeding with this payment, you agree to our{' '}
                        <a href="#" className="text-purple-600 hover:text-purple-700 underline">
                          Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href="#" className="text-purple-600 hover:text-purple-700 underline">
                          Privacy Policy
                        </a>
                      </p>
                    </div>
                  </div>
                  {errors.termsAccepted && (
                    <p className="mt-2 text-sm text-red-600">{errors.termsAccepted.message}</p>
                  )}
                </div>

                {/* Compact Payment Summary & Actions */}
                <div className="bg-gray-50 rounded-lg p-4 border-t border-gray-200 mt-6">
                  {/* Payment Summary */}
                  <div className="mb-4">
                    <div className="flex justify-between items-center text-sm mb-2">
                      <span className="text-gray-600">Payment Amount:</span>
                      <span className="font-medium">{formatCurrency(watchedAmount || 0, watchedCurrency)}</span>
                    </div>
                    {appliedPromo && (
                      <div className="flex justify-between items-center text-sm text-green-600 mb-2">
                        <span>Discount ({appliedPromo.code}):</span>
                        <span>
                          -{appliedPromo.type === 'percentage'
                            ? `${appliedPromo.discount}%`
                            : formatCurrency(appliedPromo.discount, watchedCurrency)
                          }
                        </span>
                      </div>
                    )}
                    {selectedPaymentMethod.processingFee && (
                      <div className="flex justify-between items-center text-sm text-gray-500 mb-2">
                        <span>Processing Fee:</span>
                        <span>+{formatCurrency(calculateTotal() - (watchedAmount || 0), watchedCurrency)}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center text-lg font-bold border-t pt-2">
                      <span>Total:</span>
                      <span className={`${calculateTotal() < 0.50 ? 'text-red-600' : 'text-purple-600'}`}>
                        {formatCurrency(calculateTotal(), watchedCurrency)}
                      </span>
                    </div>
                    {calculateTotal() < 0.50 && (
                      <div className="mt-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
                        ⚠️ Minimum payment amount is $0.50 USD for card payments.
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between space-x-3">
                    <button
                      type="button"
                      onClick={onClose}
                      className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      Cancel
                    </button>

                    {/* Submit Button for non-Stripe payments */}
                    {!selectedPaymentMethod.requiresStripe && (
                      <button
                        type="submit"
                        disabled={loading}
                        className="flex-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Processing...</span>
                          </>
                        ) : (
                          <>
                            <LockClosedIcon className="h-4 w-4" />
                            <span>Pay {formatCurrency(calculateTotal(), watchedCurrency)}</span>
                          </>
                        )}
                      </button>
                    )}

                    {/* Setup Button for Stripe payments without client secret */}
                    {selectedPaymentMethod.requiresStripe && !clientSecret && (
                      <button
                        type="submit"
                        disabled={loading || calculateTotal() < 0.50}
                        className="flex-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        {loading ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Setting up...</span>
                          </>
                        ) : (
                          <>
                            <CreditCardIcon className="h-4 w-4" />
                            <span>Setup Payment</span>
                          </>
                        )}
                      </button>
                    )}

                    {/* Pay Button for Stripe payments with client secret */}
                    {selectedPaymentMethod.requiresStripe && clientSecret && (
                      <button
                        type="submit"
                        disabled={loading || stripeLoading || calculateTotal() < 0.50}
                        className="flex-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                      >
                        {(loading || stripeLoading) ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Processing...</span>
                          </>
                        ) : (
                          <>
                            <LockClosedIcon className="h-4 w-4" />
                            <span>Pay {formatCurrency(calculateTotal(), watchedCurrency)}</span>
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}

// Wrapper component that provides Stripe Elements context
export function EnhancedPaymentFormModalWrapper(props: EnhancedPaymentFormModalProps) {
  return (
    <Elements
      stripe={stripePromise}
      options={{
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#7c3aed',
          },
        },
      }}
    >
      <EnhancedPaymentFormModal {...props} />
    </Elements>
  )
}
