'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  MagnifyingGlassIcon,
  DocumentCheckIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PowerIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  TagIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'
import { ProjectModal } from './project-modal'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  createdat: string
  updatedat?: string
  _count?: {
    contracts: number
    invoices: number
    messages: number
  }
}

interface ProjectManagementProps {
  client: Client
  selectedProject: Project | null
  onProjectSelect: (project: Project | null) => void
}

export function EnhancedProjectManagement({ client, selectedProject, onProjectSelect }: ProjectManagementProps) {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'name', 'status', 'projstartdate', 'estimatecost', 'estimatetime', 'updatedat'
  ])
  const [sortBy, setSortBy] = useState('updatedat')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [filters, setFilters] = useState<Record<string, string>>({})
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnControls, setShowColumnControls] = useState(false)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch projects
  const fetchProjects = async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
      })

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      // Add client filter to params
      params.append('filter', JSON.stringify({ clientid: client.id }))

      const response = await fetch(`/api/admin/projects?${params}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success) {
        setProjects(data.data || [])
        setTotalPages(Math.ceil((data.total || 0) / 20))
      } else {
        throw new Error(data.error || 'Failed to fetch projects')
      }
      setError(null)
    } catch (err) {
      console.error('Error fetching projects:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      setProjects([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (client) {
      const isSearching = debouncedSearchQuery !== ''
      fetchProjects(isSearching)
    }
  }, [client, currentPage, debouncedSearchQuery, sortBy, sortOrder, filters])

  // Available columns for display
  const availableColumns = [
    { key: 'name', label: 'Project Name', sortable: true },
    { key: 'status', label: 'Status', sortable: true },
    { key: 'projstartdate', label: 'Start Date', sortable: true },
    { key: 'projcompletiondate', label: 'Completion Date', sortable: true },
    { key: 'estimatecost', label: 'Estimated Cost', sortable: true },
    { key: 'estimatetime', label: 'Estimated Time', sortable: false },
    { key: 'tags', label: 'Tags', sortable: false },
    { key: 'updatedat', label: 'Last Updated', sortable: true },
  ]

  // Utility functions
  const handleSelectProject = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId) 
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    )
  }

  const handleSelectAll = () => {
    if (selectedProjects.length === projects.length) {
      setSelectedProjects([])
    } else {
      setSelectedProjects(projects.map(p => String(p.id)))
    }
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const handleBulkAction = async (action: string, projectIds: string[]) => {
    try {
      setActionLoading(`bulk-${action}`)
      
      const response = await fetch('/api/admin/projects/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, projectIds })
      })

      if (!response.ok) {
        throw new Error('Failed to perform bulk action')
      }

      await fetchProjects(true)
      setSelectedProjects([])
    } catch (error) {
      console.error('Bulk action error:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleAction = async (action: string, project: Project) => {
    try {
      setActionLoading(`${action}-${project.id}`)
      
      switch (action) {
        case 'edit':
          setEditingProject(project)
          setIsEditModalOpen(true)
          break
        case 'delete':
          if (confirm('Are you sure you want to delete this project?')) {
            const response = await fetch(`/api/admin/projects/${project.id}`, {
              method: 'DELETE'
            })
            if (response.ok) {
              await fetchProjects(true)
            }
          }
          break
        case 'toggle-status':
          const newStatus = project.status === 'active' ? 'inactive' : 'active'
          const response = await fetch(`/api/admin/projects/${project.id}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: newStatus })
          })
          if (response.ok) {
            await fetchProjects(true)
          }
          break
      }
    } catch (error) {
      console.error('Action error:', error)
    } finally {
      setActionLoading(null)
    }
  }

  const handleCreate = async (data: any) => {
    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...data, clientId: client.id })
      })
      
      if (response.ok) {
        await fetchProjects(true)
        setIsCreateModalOpen(false)
      }
    } catch (error) {
      console.error('Create error:', error)
    }
  }

  const handleUpdate = async (projectId: string, data: any) => {
    try {
      const response = await fetch(`/api/admin/projects/${projectId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })
      
      if (response.ok) {
        await fetchProjects(true)
        setIsEditModalOpen(false)
        setEditingProject(null)
      }
    } catch (error) {
      console.error('Update error:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'planning':
        return 'bg-yellow-100 text-yellow-800'
      case 'on_hold':
        return 'bg-orange-100 text-orange-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'in_progress':
        return <PlayIcon className="h-4 w-4" />
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'on_hold':
        return <PauseIcon className="h-4 w-4" />
      case 'cancelled':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-gray-100 rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <h3 className="text-lg font-medium text-red-900 mb-2">Error Loading Projects</h3>
          <p className="text-sm text-red-700 mb-4">{error}</p>
          <button
            onClick={() => fetchProjects()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Projects for {client.companyName}</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage projects for this client ({projects.length} total)
          </p>
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Project
        </button>
      </div>

      {/* Enhanced Controls Bar */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search projects..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-3">
              {/* Filters */}
              <div className="relative">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium transition-colors ${
                    showFilters || Object.values(filters).some(v => v)
                      ? 'bg-blue-50 text-blue-700 border-blue-300'
                      : 'bg-white text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FunnelIcon className="h-4 w-4 mr-2" />
                  Filters
                  {Object.values(filters).some(v => v) && (
                    <span className="ml-2 bg-blue-100 text-blue-800 text-xs rounded-full px-2 py-0.5">
                      {Object.values(filters).filter(v => v).length}
                    </span>
                  )}
                </button>
              </div>

              {/* View Mode */}
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 text-sm font-medium transition-colors ${
                    viewMode === 'list'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                    viewMode === 'grid'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setViewMode('card')}
                  className={`p-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                    viewMode === 'card'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                  title="Card view"
                >
                  <RectangleStackIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Density */}
              <div className="flex items-center border border-gray-300 rounded-md">
                <button
                  onClick={() => setDisplayDensity('compact')}
                  className={`px-3 py-2 text-xs font-medium transition-colors ${
                    displayDensity === 'compact'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Compact
                </button>
                <button
                  onClick={() => setDisplayDensity('comfortable')}
                  className={`px-3 py-2 text-xs font-medium transition-colors border-l border-gray-300 ${
                    displayDensity === 'comfortable'
                      ? 'bg-blue-50 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Comfortable
                </button>
              </div>

              {/* Column Controls */}
              <div className="relative">
                <button
                  onClick={() => setShowColumnControls(!showColumnControls)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                  Columns
                </button>
                {showColumnControls && (
                  <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                    <div className="p-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Show columns</h4>
                      <div className="space-y-2">
                        {availableColumns.map((column) => (
                          <label key={column.key} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={visibleColumns.includes(column.key)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setVisibleColumns(prev => [...prev, column.key])
                                } else {
                                  setVisibleColumns(prev => prev.filter(col => col !== column.key))
                                }
                              }}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Filters Panel */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All statuses</option>
                    <option value="planning">Planning</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="on_hold">On Hold</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                  <select
                    value={filters.dateRange || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
                    className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">All dates</option>
                    <option value="last_week">Last week</option>
                    <option value="last_month">Last month</option>
                    <option value="last_quarter">Last quarter</option>
                    <option value="last_year">Last year</option>
                  </select>
                </div>
                <div className="flex items-end">
                  <button
                    onClick={() => setFilters({})}
                    className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                  >
                    Clear filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bulk Actions Bar */}
        {selectedProjects.length > 0 && (
          <div className="px-6 py-3 bg-blue-50 border-t border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-blue-900">
                  {selectedProjects.length} project{selectedProjects.length !== 1 ? 's' : ''} selected
                </span>
                <button
                  onClick={() => setSelectedProjects([])}
                  className="text-sm text-blue-700 hover:text-blue-900"
                >
                  Clear selection
                </button>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleBulkAction('activate', selectedProjects)}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 disabled:opacity-50"
                >
                  <PowerIcon className="h-3 w-3 mr-1" />
                  Activate
                </button>
                <button
                  onClick={() => handleBulkAction('deactivate', selectedProjects)}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-orange-700 bg-orange-100 hover:bg-orange-200 disabled:opacity-50"
                >
                  <EyeSlashIcon className="h-3 w-3 mr-1" />
                  Deactivate
                </button>
                <button
                  onClick={() => {
                    if (confirm(`Are you sure you want to delete ${selectedProjects.length} project${selectedProjects.length !== 1 ? 's' : ''}?`)) {
                      handleBulkAction('delete', selectedProjects)
                    }
                  }}
                  disabled={actionLoading?.startsWith('bulk-')}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Overview */}
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{projects.length}</div>
              <div className="text-sm text-gray-600">Total Projects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {projects.filter(p => p.status === 'in_progress').length}
              </div>
              <div className="text-sm text-gray-600">In Progress</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {projects.filter(p => p.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {projects.reduce((sum, p) => sum + (p.estimatecost || 0), 0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 0,
                  maximumFractionDigits: 0
                })}
              </div>
              <div className="text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">
          {projects.length === 0 ? (
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No projects found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {debouncedSearchQuery || Object.values(filters).some(v => v)
                  ? 'Try adjusting your search or filters'
                  : 'Get started by creating a new project for this client'
                }
              </p>
              {!debouncedSearchQuery && !Object.values(filters).some(v => v) && (
                <div className="mt-6">
                  <button
                    onClick={() => setIsCreateModalOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Add Project
                  </button>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* List View */}
              {viewMode === 'list' && (
                <div className="overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left">
                          <input
                            type="checkbox"
                            checked={selectedProjects.length === projects.length && projects.length > 0}
                            onChange={handleSelectAll}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                        </th>
                        {availableColumns
                          .filter(col => visibleColumns.includes(col.key))
                          .map((column) => (
                            <th
                              key={column.key}
                              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                              onClick={() => column.sortable && handleSort(column.key)}
                            >
                              <div className="flex items-center space-x-1">
                                <span>{column.label}</span>
                                {column.sortable && sortBy === column.key && (
                                  sortOrder === 'asc' ? (
                                    <ArrowUpIcon className="h-3 w-3" />
                                  ) : (
                                    <ArrowDownIcon className="h-3 w-3" />
                                  )
                                )}
                              </div>
                            </th>
                          ))}
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {projects.map((project) => (
                        <ProjectRow
                          key={project.id}
                          project={project}
                          isSelected={selectedProjects.includes(String(project.id))}
                          onSelect={() => handleSelectProject(String(project.id))}
                          onAction={(action) => handleAction(action, project)}
                          onProjectSelect={onProjectSelect}
                          selectedProject={selectedProject}
                          visibleColumns={visibleColumns}
                          displayDensity={displayDensity}
                          viewMode={viewMode}
                          actionLoading={actionLoading}
                          formatDate={formatDate}
                          formatCurrency={formatCurrency}
                          getStatusColor={getStatusColor}
                          getStatusIcon={getStatusIcon}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Grid View */}
              {viewMode === 'grid' && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {projects.map((project) => (
                    <ProjectRow
                      key={project.id}
                      project={project}
                      isSelected={selectedProjects.includes(String(project.id))}
                      onSelect={() => handleSelectProject(String(project.id))}
                      onAction={(action) => handleAction(action, project)}
                      onProjectSelect={onProjectSelect}
                      selectedProject={selectedProject}
                      visibleColumns={visibleColumns}
                      displayDensity={displayDensity}
                      viewMode={viewMode}
                      actionLoading={actionLoading}
                      formatDate={formatDate}
                      formatCurrency={formatCurrency}
                      getStatusColor={getStatusColor}
                      getStatusIcon={getStatusIcon}
                    />
                  ))}
                </div>
              )}

              {/* Card View */}
              {viewMode === 'card' && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {projects.map((project) => (
                    <ProjectRow
                      key={project.id}
                      project={project}
                      isSelected={selectedProjects.includes(String(project.id))}
                      onSelect={() => handleSelectProject(String(project.id))}
                      onAction={(action) => handleAction(action, project)}
                      onProjectSelect={onProjectSelect}
                      selectedProject={selectedProject}
                      visibleColumns={visibleColumns}
                      displayDensity={displayDensity}
                      viewMode={viewMode}
                      actionLoading={actionLoading}
                      formatDate={formatDate}
                      formatCurrency={formatCurrency}
                      getStatusColor={getStatusColor}
                      getStatusIcon={getStatusIcon}
                    />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-6 flex items-center justify-between border-t border-gray-200 pt-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing page <span className="font-medium">{currentPage}</span> of{' '}
                        <span className="font-medium">{totalPages}</span>
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Previous
                        </button>
                        {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                          const page = i + 1
                          return (
                            <button
                              key={page}
                              onClick={() => setCurrentPage(page)}
                              className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                currentPage === page
                                  ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                              }`}
                            >
                              {page}
                            </button>
                          )
                        })}
                        <button
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          Next
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <ProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create New Project"
        client={client}
      />

      <ProjectModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingProject(null)
        }}
        onSubmit={(data) => editingProject && handleUpdate(String(editingProject.id), data)}
        title="Edit Project"
        project={editingProject}
        client={client}
      />
    </div>
  )
}

// ProjectRow Component
interface ProjectRowProps {
  project: Project
  isSelected: boolean
  onSelect: () => void
  onAction: (action: string) => void
  onProjectSelect: (project: Project | null) => void
  selectedProject: Project | null
  visibleColumns: string[]
  displayDensity: string
  viewMode: string
  actionLoading: string | null
  formatDate: (date: string) => string
  formatCurrency: (amount: number) => string
  getStatusColor: (status: string) => string
  getStatusIcon: (status: string) => React.ReactNode
}

function ProjectRow({
  project,
  isSelected,
  onSelect,
  onAction,
  onProjectSelect,
  selectedProject,
  visibleColumns,
  displayDensity,
  viewMode,
  actionLoading,
  formatDate,
  formatCurrency,
  getStatusColor,
  getStatusIcon
}: ProjectRowProps) {
  const isCompact = displayDensity === 'compact'
  const isCurrentProject = selectedProject?.id === project.id

  if (viewMode === 'list') {
    return (
      <tr
        className={`hover:bg-gray-50 transition-colors cursor-pointer ${isCurrentProject ? 'bg-blue-50' : ''} ${
          isCompact ? 'h-12' : 'h-16'
        }`}
        onClick={(e) => {
          // Don't trigger row click if clicking on checkbox or action buttons
          if (e.target instanceof HTMLInputElement || e.target instanceof HTMLButtonElement ||
              (e.target as HTMLElement).closest('button') || (e.target as HTMLElement).closest('input')) {
            return;
          }
          onProjectSelect(project);
        }}
      >
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onSelect}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            onClick={(e) => e.stopPropagation()}
          />
        </td>

        {visibleColumns.includes('name') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-8 w-8">
                {project.imageUrl ? (
                  <img className="h-8 w-8 rounded-lg object-cover" src={project.imageUrl} alt="" />
                ) : (
                  <div className="h-8 w-8 rounded-lg bg-gray-200 flex items-center justify-center">
                    <DocumentTextIcon className="h-4 w-4 text-gray-500" />
                  </div>
                )}
              </div>
              <div className="ml-4">
                <button
                  onClick={() => onProjectSelect(project)}
                  className="text-sm font-medium text-gray-900 hover:text-blue-600 text-left"
                >
                  {project.name}
                </button>
                {!isCompact && project.description && (
                  <div className="text-sm text-gray-500 truncate max-w-xs">
                    {project.description}
                  </div>
                )}
              </div>
            </div>
          </td>
        )}

        {visibleColumns.includes('status') && (
          <td className="px-6 py-4 whitespace-nowrap">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status || '')}`}>
              {getStatusIcon(project.status || '')}
              <span className="ml-1 capitalize">{project.status?.replace('_', ' ')}</span>
            </span>
          </td>
        )}

        {visibleColumns.includes('projstartdate') && (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {project.projstartdate ? formatDate(project.projstartdate) : '-'}
          </td>
        )}

        {visibleColumns.includes('projcompletiondate') && (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {project.projcompletiondate ? formatDate(project.projcompletiondate) : '-'}
          </td>
        )}

        {visibleColumns.includes('estimatecost') && (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {project.estimatecost ? formatCurrency(project.estimatecost) : '-'}
          </td>
        )}

        {visibleColumns.includes('estimatetime') && (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            {project.estimatetime || '-'}
          </td>
        )}

        {visibleColumns.includes('tags') && (
          <td className="px-6 py-4 whitespace-nowrap">
            {project.tags ? (
              <div className="flex flex-wrap gap-1">
                {project.tags.split(',').slice(0, 2).map((tag, index) => (
                  <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {tag.trim()}
                  </span>
                ))}
                {project.tags.split(',').length > 2 && (
                  <span className="text-xs text-gray-500">+{project.tags.split(',').length - 2}</span>
                )}
              </div>
            ) : (
              <span className="text-sm text-gray-500">-</span>
            )}
          </td>
        )}

        {visibleColumns.includes('updatedat') && (
          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
            {project.updatedat ? formatDate(project.updatedat) : formatDate(project.createdat)}
          </td>
        )}

        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex items-center justify-end space-x-2">
            <button
              onClick={() => onProjectSelect(project)}
              className="text-blue-600 hover:text-blue-900 p-1"
              title="View details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onAction('edit')}
              disabled={actionLoading === `edit-${project.id}`}
              className="text-gray-600 hover:text-gray-900 p-1 disabled:opacity-50"
              title="Edit project"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onAction('delete')}
              disabled={actionLoading === `delete-${project.id}`}
              className="text-red-600 hover:text-red-900 p-1 disabled:opacity-50"
              title="Delete project"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    )
  }

  if (viewMode === 'grid') {
    return (
      <div
        className={`bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer ${
          isCurrentProject ? 'ring-2 ring-blue-500' : ''
        }`}
        onClick={(e) => {
          // Don't trigger card click if clicking on checkbox or action buttons
          if (e.target instanceof HTMLInputElement || e.target instanceof HTMLButtonElement ||
              (e.target as HTMLElement).closest('button') || (e.target as HTMLElement).closest('input')) {
            return;
          }
          onProjectSelect(project);
        }}
      >
        <div className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={onSelect}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex-shrink-0">
                {project.imageUrl ? (
                  <img className="h-10 w-10 rounded-lg object-cover" src={project.imageUrl} alt="" />
                ) : (
                  <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                    <DocumentTextIcon className="h-5 w-5 text-gray-500" />
                  </div>
                )}
              </div>
            </div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status || '')}`}>
              {getStatusIcon(project.status || '')}
              <span className="ml-1 capitalize">{project.status?.replace('_', ' ')}</span>
            </span>
          </div>

          <div className="mb-3">
            <button
              onClick={() => onProjectSelect(project)}
              className="text-sm font-medium text-gray-900 hover:text-blue-600 text-left block"
            >
              {project.name}
            </button>
            {project.description && (
              <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                {project.description}
              </p>
            )}
          </div>

          <div className="space-y-2 text-xs text-gray-600">
            {project.projstartdate && (
              <div className="flex items-center">
                <CalendarIcon className="h-3 w-3 mr-1" />
                Start: {formatDate(project.projstartdate)}
              </div>
            )}
            {project.estimatecost && (
              <div className="flex items-center">
                <CurrencyDollarIcon className="h-3 w-3 mr-1" />
                {formatCurrency(project.estimatecost)}
              </div>
            )}
            {project.estimatetime && (
              <div className="flex items-center">
                <ClockIcon className="h-3 w-3 mr-1" />
                {project.estimatetime}
              </div>
            )}
          </div>

          {project.tags && (
            <div className="mt-3 flex flex-wrap gap-1">
              {project.tags.split(',').slice(0, 3).map((tag, index) => (
                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  {tag.trim()}
                </span>
              ))}
            </div>
          )}

          <div className="mt-4 flex items-center justify-between">
            <div className="text-xs text-gray-500">
              Updated {project.updatedat ? formatDate(project.updatedat) : formatDate(project.createdat)}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onProjectSelect(project)}
                className="text-blue-600 hover:text-blue-900 p-1"
                title="View details"
              >
                <EyeIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => onAction('edit')}
                disabled={actionLoading === `edit-${project.id}`}
                className="text-gray-600 hover:text-gray-900 p-1 disabled:opacity-50"
                title="Edit project"
              >
                <PencilIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => onAction('delete')}
                disabled={actionLoading === `delete-${project.id}`}
                className="text-red-600 hover:text-red-900 p-1 disabled:opacity-50"
                title="Delete project"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (viewMode === 'card') {
    return (
      <div
        className={`bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer ${
          isCurrentProject ? 'ring-2 ring-blue-500' : ''
        }`}
        onClick={(e) => {
          // Don't trigger card click if clicking on checkbox or action buttons
          if (e.target instanceof HTMLInputElement || e.target instanceof HTMLButtonElement ||
              (e.target as HTMLElement).closest('button') || (e.target as HTMLElement).closest('input')) {
            return;
          }
          onProjectSelect(project);
        }}
      >
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-4">
              <input
                type="checkbox"
                checked={isSelected}
                onChange={onSelect}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex-shrink-0">
                {project.imageUrl ? (
                  <img className="h-12 w-12 rounded-lg object-cover" src={project.imageUrl} alt="" />
                ) : (
                  <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                    <DocumentTextIcon className="h-6 w-6 text-gray-500" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <button
                  onClick={() => onProjectSelect(project)}
                  className="text-lg font-medium text-gray-900 hover:text-blue-600 text-left block"
                >
                  {project.name}
                </button>
                {project.description && (
                  <p className="text-sm text-gray-500 mt-1 line-clamp-2">
                    {project.description}
                  </p>
                )}
              </div>
            </div>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(project.status || '')}`}>
              {getStatusIcon(project.status || '')}
              <span className="ml-1 capitalize">{project.status?.replace('_', ' ')}</span>
            </span>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <div className="text-sm font-medium text-gray-500">Start Date</div>
              <div className="text-sm text-gray-900">
                {project.projstartdate ? formatDate(project.projstartdate) : 'Not set'}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Completion Date</div>
              <div className="text-sm text-gray-900">
                {project.projcompletiondate ? formatDate(project.projcompletiondate) : 'Not set'}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Estimated Cost</div>
              <div className="text-sm text-gray-900">
                {project.estimatecost ? formatCurrency(project.estimatecost) : 'Not set'}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Estimated Time</div>
              <div className="text-sm text-gray-900">
                {project.estimatetime || 'Not set'}
              </div>
            </div>
          </div>

          {project.tags && (
            <div className="mb-4">
              <div className="text-sm font-medium text-gray-500 mb-2">Tags</div>
              <div className="flex flex-wrap gap-1">
                {project.tags.split(',').map((tag, index) => (
                  <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                    {tag.trim()}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="text-sm text-gray-500">
              Updated {project.updatedat ? formatDate(project.updatedat) : formatDate(project.createdat)}
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onProjectSelect(project)}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                title="View details"
              >
                <EyeIcon className="h-3 w-3 mr-1" />
                View
              </button>
              <button
                onClick={() => onAction('edit')}
                disabled={actionLoading === `edit-${project.id}`}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                title="Edit project"
              >
                <PencilIcon className="h-3 w-3 mr-1" />
                Edit
              </button>
              <button
                onClick={() => onAction('delete')}
                disabled={actionLoading === `delete-${project.id}`}
                className="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                title="Delete project"
              >
                <TrashIcon className="h-3 w-3 mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return null
}
