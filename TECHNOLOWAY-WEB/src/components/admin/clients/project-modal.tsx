'use client'

import React, { useState, useEffect } from 'react'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface Client {
  id: string | number
  companyName: string
  contactName: string
  contactEmail: string
}

interface Project {
  id: string | number
  clientId: string | number
  name: string
  description: string
  status?: string
  projStartDate?: string
  projCompletionDate?: string
  estimateCost?: number
  estimateTime?: string
  imageUrl?: string
  projectUrl?: string
  githubUrl?: string
  tags?: string
}

interface ProjectModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => void
  title: string
  project?: Project | null
  client: Client
}

export function ProjectModal({ isOpen, onClose, onSubmit, title, project, client }: ProjectModalProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'planning',
    projStartDate: '',
    projCompletionDate: '',
    estimateCost: '',
    estimateTime: '',
    imageUrl: '',
    projectUrl: '',
    githubUrl: '',
    tags: ''
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (project) {
      setFormData({
        name: project.name || '',
        description: project.description || '',
        status: project.status || 'planning',
        projStartDate: project.projStartDate ? project.projStartDate.split('T')[0] : '',
        projCompletionDate: project.projCompletionDate ? project.projCompletionDate.split('T')[0] : '',
        estimateCost: project.estimateCost ? project.estimateCost.toString() : '',
        estimateTime: project.estimateTime || '',
        imageUrl: project.imageUrl || '',
        projectUrl: project.projectUrl || '',
        githubUrl: project.githubUrl || '',
        tags: project.tags || ''
      })
    } else {
      setFormData({
        name: '',
        description: '',
        status: 'planning',
        projStartDate: '',
        projCompletionDate: '',
        estimateCost: '',
        estimateTime: '',
        imageUrl: '',
        projectUrl: '',
        githubUrl: '',
        tags: ''
      })
    }
    setErrors({})
  }, [project, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setErrors({})

    try {
      // Validate required fields
      const newErrors: Record<string, string> = {}
      if (!formData.name.trim()) {
        newErrors.name = 'Project name is required'
      }
      if (!formData.description.trim()) {
        newErrors.description = 'Description is required'
      }

      if (Object.keys(newErrors).length > 0) {
        setErrors(newErrors)
        setLoading(false)
        return
      }

      // Prepare data for submission
      const submitData = {
        ...formData,
        estimateCost: formData.estimateCost ? parseFloat(formData.estimateCost) : null,
        projStartDate: formData.projStartDate || null,
        projCompletionDate: formData.projCompletionDate || null,
        clientId: client.id
      }

      await onSubmit(submitData)
    } catch (error) {
      console.error('Error submitting project:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <div className="relative bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[95vh] overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">{title}</h2>
              <button
                type="button"
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Project Name */}
                    <div className="md:col-span-2">
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Project Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.name ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter project name"
                      />
                      {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
                    </div>

                    {/* Description */}
                    <div className="md:col-span-2">
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                        Description *
                      </label>
                      <textarea
                        id="description"
                        name="description"
                        rows={3}
                        value={formData.description}
                        onChange={handleChange}
                        className={`block w-full border rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          errors.description ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter project description"
                      />
                      {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
                    </div>

                    {/* Status */}
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="planning">Planning</option>
                        <option value="in_progress">In Progress</option>
                        <option value="completed">Completed</option>
                        <option value="on_hold">On Hold</option>
                        <option value="cancelled">Cancelled</option>
                      </select>
                    </div>

                    {/* Estimated Cost */}
                    <div>
                      <label htmlFor="estimateCost" className="block text-sm font-medium text-gray-700 mb-1">
                        Estimated Cost ($)
                      </label>
                      <input
                        type="number"
                        id="estimateCost"
                        name="estimateCost"
                        value={formData.estimateCost}
                        onChange={handleChange}
                        min="0"
                        step="0.01"
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Start Date */}
                    <div>
                      <label htmlFor="projStartDate" className="block text-sm font-medium text-gray-700 mb-1">
                        Start Date
                      </label>
                      <input
                        type="date"
                        id="projStartDate"
                        name="projStartDate"
                        value={formData.projStartDate}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    {/* Completion Date */}
                    <div>
                      <label htmlFor="projCompletionDate" className="block text-sm font-medium text-gray-700 mb-1">
                        Completion Date
                      </label>
                      <input
                        type="date"
                        id="projCompletionDate"
                        name="projCompletionDate"
                        value={formData.projCompletionDate}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    {/* Estimated Time */}
                    <div>
                      <label htmlFor="estimateTime" className="block text-sm font-medium text-gray-700 mb-1">
                        Estimated Time
                      </label>
                      <input
                        type="text"
                        id="estimateTime"
                        name="estimateTime"
                        value={formData.estimateTime}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., 2-3 months, 40 hours"
                      />
                    </div>

                    {/* Tags */}
                    <div>
                      <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-1">
                        Tags
                      </label>
                      <input
                        type="text"
                        id="tags"
                        name="tags"
                        value={formData.tags}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., web, mobile, api (comma separated)"
                      />
                    </div>

                    {/* Image URL */}
                    <div className="md:col-span-2">
                      <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">
                        Project Image URL
                      </label>
                      <input
                        type="url"
                        id="imageUrl"
                        name="imageUrl"
                        value={formData.imageUrl}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://example.com/image.jpg"
                      />
                    </div>

                    {/* Project URL */}
                    <div>
                      <label htmlFor="projectUrl" className="block text-sm font-medium text-gray-700 mb-1">
                        Project URL
                      </label>
                      <input
                        type="url"
                        id="projectUrl"
                        name="projectUrl"
                        value={formData.projectUrl}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://project-demo.com"
                      />
                    </div>

                    {/* GitHub URL */}
                    <div>
                      <label htmlFor="githubUrl" className="block text-sm font-medium text-gray-700 mb-1">
                        GitHub URL
                      </label>
                      <input
                        type="url"
                        id="githubUrl"
                        name="githubUrl"
                        value={formData.githubUrl}
                        onChange={handleChange}
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="https://github.com/user/repo"
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {loading ? 'Saving...' : project ? 'Update Project' : 'Create Project'}
                    </button>
                  </div>
                </form>
          </div>
        </div>
      </div>
    </div>
  )
}
