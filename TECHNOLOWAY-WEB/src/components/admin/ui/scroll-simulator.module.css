/* Scroll container styles */
.scrollContainer {
  scrollbar-gutter: stable both-edges;
  scroll-behavior: smooth;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar styling for better visibility */
.scrollContainer::-webkit-scrollbar {
  width: 12px;
}

.scrollContainer::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
}

.scrollContainer::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 6px;
  border: 2px solid #f1f5f9;
}

.scrollContainer::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar styling */
.scrollContainer {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Ensure content has enough height to be scrollable */
.scrollContent {
  min-height: 800px; /* Force minimum height to ensure scrolling */
  padding-bottom: 2rem;
}

/* Scroll simulator positioning */
.scrollSimulator {
  position: fixed;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 50;
}

/* Animation for scroll indicators */
@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.scrollIndicator {
  animation: scrollPulse 0.6s ease-in-out;
}

/* Smooth transitions for buttons */
.scrollButton {
  transition: all 0.2s ease-in-out;
}

.scrollButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.scrollButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Mouse wheel animation */
@keyframes wheelSpin {
  0% { transform: translateX(-50%) rotate(0deg); }
  100% { transform: translateX(-50%) rotate(360deg); }
}

.wheelSpinning {
  animation: wheelSpin 0.3s ease-in-out;
}
