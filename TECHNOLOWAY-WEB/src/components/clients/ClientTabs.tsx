'use client'

import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { 
  DocumentTextIcon, 
  ChartBarIcon, 
  CurrencyDollarIcon, 
  CreditCardIcon,
  HomeIcon
} from '@heroicons/react/24/outline'

interface ClientTabsProps {
  clientId: string
  counts?: {
    projects: number
    contracts: number
    invoices: number
    payments: number
  }
}

export function ClientTabs({ clientId, counts }: ClientTabsProps) {
  const pathname = usePathname()

  const tabs = [
    {
      name: 'Overview',
      href: `/clients/${clientId}`,
      icon: HomeIcon,
      count: undefined,
      current: pathname === `/clients/${clientId}`,
    },
    {
      name: 'Projects',
      href: `/clients/${clientId}/projects`,
      icon: DocumentTextIcon,
      count: counts?.projects,
      current: pathname?.startsWith(`/clients/${clientId}/projects`) || false,
    },
    {
      name: 'Contracts',
      href: `/clients/${clientId}/contracts`,
      icon: ChartBarIcon,
      count: counts?.contracts,
      current: pathname?.startsWith(`/clients/${clientId}/contracts`) || false,
    },
    {
      name: 'Invoices',
      href: `/clients/${clientId}/invoices`,
      icon: CurrencyDollarIcon,
      count: counts?.invoices,
      current: pathname?.startsWith(`/clients/${clientId}/invoices`) || false,
    },
    {
      name: 'Payments',
      href: `/clients/${clientId}/payments`,
      icon: CreditCardIcon,
      count: counts?.payments,
      current: pathname?.startsWith(`/clients/${clientId}/payments`) || false,
    },
  ]

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <Link
                key={tab.name}
                href={tab.href}
                className={`${
                  tab.current
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
                aria-current={tab.current ? 'page' : undefined}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.name}</span>
                {tab.count !== undefined && (
                  <span
                    className={`${
                      tab.current
                        ? 'bg-blue-100 text-blue-600'
                        : 'bg-gray-100 text-gray-900'
                    } ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium`}
                  >
                    {tab.count}
                  </span>
                )}
              </Link>
            )
          })}
        </nav>
      </div>
    </div>
  )
}
