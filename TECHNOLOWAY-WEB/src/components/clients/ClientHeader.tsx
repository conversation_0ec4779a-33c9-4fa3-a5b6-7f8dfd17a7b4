'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { 
  BuildingOfficeIcon, 
  EnvelopeIcon, 
  PhoneIcon, 
  GlobeAltIcon,
  MapPinIcon,
  CalendarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline'
import { ClientWithCounts } from '@/lib/fetchers/client'

interface ClientHeaderProps {
  client: ClientWithCounts
}

export function ClientHeader({ client }: ClientHeaderProps) {
  const [imageError, setImageError] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="py-6">
          {/* Main Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              {/* Company Logo */}
              <div className="flex-shrink-0">
                {client.logoUrl && !imageError ? (
                  <Image
                    src={client.logoUrl}
                    alt={`${client.companyName} logo`}
                    width={80}
                    height={80}
                    className="rounded-lg border border-gray-200"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center border border-gray-200">
                    <BuildingOfficeIcon className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Company Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-3">
                  <h1 className="text-2xl font-bold text-gray-900 truncate">
                    {client.companyName}
                  </h1>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      client.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {client.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>

                <div className="mt-2 flex flex-col sm:flex-row sm:flex-wrap sm:space-x-6">
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <EnvelopeIcon className="flex-shrink-0 mr-1.5 h-4 w-4" />
                    <a
                      href={`mailto:${client.contactEmail}`}
                      className="hover:text-gray-700"
                    >
                      {client.contactEmail}
                    </a>
                  </div>
                  {client.contactPhone && (
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <PhoneIcon className="flex-shrink-0 mr-1.5 h-4 w-4" />
                      <a
                        href={`tel:${client.contactPhone}`}
                        className="hover:text-gray-700"
                      >
                        {client.contactPhone}
                      </a>
                    </div>
                  )}
                  {client.website && (
                    <div className="mt-2 flex items-center text-sm text-gray-500">
                      <GlobeAltIcon className="flex-shrink-0 mr-1.5 h-4 w-4" />
                      <a
                        href={client.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:text-gray-700"
                      >
                        {client.website}
                      </a>
                    </div>
                  )}
                </div>

                {/* Contact Person */}
                <div className="mt-3">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium">Contact:</span> {client.contactName}
                  </p>
                </div>

                {/* Address */}
                {(client.address || client.city || client.state || client.country) && (
                  <div className="mt-2 flex items-start text-sm text-gray-500">
                    <MapPinIcon className="flex-shrink-0 mr-1.5 h-4 w-4 mt-0.5" />
                    <div>
                      {client.address && <div>{client.address}</div>}
                      <div>
                        {[client.city, client.state, client.zipCode, client.country]
                          .filter(Boolean)
                          .join(', ')}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              <Link
                href={`/admin/clients`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Back to Clients
              </Link>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DocumentTextIcon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Projects
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {client._count.projects}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Contracts
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {client._count.contracts}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Invoices
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {client._count.invoices}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg border border-gray-200">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CreditCardIcon className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Payments
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {client._count.payments}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {client.notes && (
            <div className="mt-6">
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Notes</h3>
                <p className="text-sm text-gray-700">{client.notes}</p>
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="mt-4 flex items-center space-x-6 text-xs text-gray-500">
            <div className="flex items-center">
              <CalendarIcon className="flex-shrink-0 mr-1 h-3 w-3" />
              Created {formatDate(client.createdAt)}
            </div>
            {client.updatedAt && (
              <div className="flex items-center">
                <CalendarIcon className="flex-shrink-0 mr-1 h-3 w-3" />
                Updated {formatDate(client.updatedAt)}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
