'use client'

import { useState } from 'react'
import { 
  CurrencyDollarIcon, 
  CalendarIcon, 
  ClockIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import { InvoiceListItem } from '@/lib/fetchers/invoice'

interface InvoiceListProps {
  invoices: InvoiceListItem[]
  loading?: boolean
  onSearch?: (query: string) => void
  onFilter?: (filters: any) => void
  searchQuery?: string
  filters?: any
}

export function InvoiceList({ 
  invoices, 
  loading = false, 
  onSearch, 
  onFilter,
  searchQuery = '',
  filters = {}
}: InvoiceListProps) {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  const [showFilters, setShowFilters] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'sent':
        return 'bg-blue-100 text-blue-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'overdue':
        return 'bg-red-100 text-red-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return <CheckCircleIcon className="h-4 w-4" />
      case 'overdue':
        return <ExclamationTriangleIcon className="h-4 w-4" />
      default:
        return <ClockIcon className="h-4 w-4" />
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    return new Date(dueDate) < new Date() && status.toLowerCase() !== 'paid'
  }

  const isDueSoon = (dueDate: string, status: string) => {
    const due = new Date(dueDate)
    const now = new Date()
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    return due <= sevenDaysFromNow && due > now && status.toLowerCase() !== 'paid'
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch?.(localSearchQuery)
  }

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value }
    onFilter?.(newFilters)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white shadow rounded-lg p-6 animate-pulse">
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={localSearchQuery}
                onChange={(e) => setLocalSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Search invoices..."
              />
            </div>
          </form>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  <option value="Pending">Pending</option>
                  <option value="Sent">Sent</option>
                  <option value="Paid">Paid</option>
                  <option value="Overdue">Overdue</option>
                  <option value="Cancelled">Cancelled</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Invoices List */}
      {invoices.length === 0 ? (
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <CurrencyDollarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
          <p className="mt-1 text-sm text-gray-500">
            This client doesn't have any invoices yet.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {invoices.map((invoice) => (
            <div key={invoice.id} className="bg-white shadow rounded-lg hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium text-gray-900">
                        {formatCurrency(invoice.totalAmount)}
                      </h3>
                      
                      {/* Status Badge */}
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                        {getStatusIcon(invoice.status)}
                        <span className="ml-1">{invoice.status}</span>
                      </span>

                      {/* Overdue Warning */}
                      {isOverdue(invoice.dueDate, invoice.status) && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                          Overdue
                        </span>
                      )}

                      {/* Due Soon Warning */}
                      {isDueSoon(invoice.dueDate, invoice.status) && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          <ClockIcon className="h-3 w-3 mr-1" />
                          Due Soon
                        </span>
                      )}
                    </div>

                    {/* Invoice Description */}
                    {invoice.description && (
                      <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                        {invoice.description}
                      </p>
                    )}

                    {/* Invoice Details */}
                    <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center text-gray-600">
                        <CalendarIcon className="h-4 w-4 mr-2 text-blue-600" />
                        <span>Due {formatDate(invoice.dueDate)}</span>
                      </div>

                      {invoice.subtotal && (
                        <div className="flex items-center text-gray-600">
                          <CurrencyDollarIcon className="h-4 w-4 mr-2 text-green-600" />
                          <span>Subtotal: {formatCurrency(invoice.subtotal)}</span>
                        </div>
                      )}

                      {invoice.taxAmount > 0 && (
                        <div className="flex items-center text-gray-600">
                          <DocumentTextIcon className="h-4 w-4 mr-2 text-purple-600" />
                          <span>Tax: {formatCurrency(invoice.taxAmount)} ({invoice.taxRate}%)</span>
                        </div>
                      )}

                      {invoice.paidAt && (
                        <div className="flex items-center text-gray-600">
                          <CheckCircleIcon className="h-4 w-4 mr-2 text-green-600" />
                          <span>Paid {formatDate(invoice.paidAt)}</span>
                        </div>
                      )}
                    </div>

                    {/* Related Contract/Project/Order */}
                    {(invoice.contract || invoice.project || invoice.order) && (
                      <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-gray-500">
                        {invoice.contract && (
                          <div>
                            <span className="font-medium">Contract:</span> {invoice.contract.contName}
                          </div>
                        )}
                        {invoice.project && (
                          <div>
                            <span className="font-medium">Project:</span> {invoice.project.name}
                          </div>
                        )}
                        {invoice.order && (
                          <div>
                            <span className="font-medium">Order:</span> {invoice.order.orderTitle}
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Invoice Amount Breakdown */}
                  <div className="ml-6 text-right">
                    <div className="text-2xl font-bold text-gray-900">
                      {formatCurrency(invoice.totalAmount)}
                    </div>
                    {invoice.subtotal && invoice.subtotal !== invoice.totalAmount && (
                      <div className="text-sm text-gray-500">
                        Subtotal: {formatCurrency(invoice.subtotal)}
                      </div>
                    )}
                  </div>
                </div>

                {/* Invoice Dates */}
                <div className="mt-6 border-t border-gray-200 pt-4">
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div>
                      Created {formatDate(invoice.createdAt)}
                    </div>
                    {invoice.updatedAt && (
                      <div>
                        Updated {formatDate(invoice.updatedAt)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
