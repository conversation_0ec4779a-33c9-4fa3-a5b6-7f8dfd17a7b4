'use client'

import {
  XMarkIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PaperAirplaneIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'

interface QuoteRequest {
  id: number
  servicename?: string
  description?: string
  budget?: number
  timeline?: string
  status?: string
  requestdate?: string
  responsedate?: string
  quotedamount?: number
  estimatedtime?: string
  notes?: string
  budgetrange?: string
}

interface QuotationViewModalProps {
  isOpen: boolean
  onClose: () => void
  quotation: QuoteRequest
}

const getStatusColor = (status?: string) => {
  if (!status || typeof status !== 'string') return 'bg-gray-100 text-gray-800 border-gray-200'
  switch (status.toLowerCase()) {
    case 'approved':
    case 'accepted':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
    case 'submitted':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'rejected':
    case 'declined':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'quoted':
    case 'responded':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status?: string) => {
  if (!status || typeof status !== 'string') return ClockIcon
  switch (status.toLowerCase()) {
    case 'approved':
    case 'accepted':
      return CheckCircleIcon
    case 'pending':
    case 'submitted':
      return ClockIcon
    case 'rejected':
    case 'declined':
      return ExclamationTriangleIcon
    case 'draft':
      return DocumentTextIcon
    case 'quoted':
    case 'responded':
      return PaperAirplaneIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount?: number) => {
  if (!amount && amount !== 0) return 'N/A'
  if (typeof amount !== 'number') return 'N/A'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString?: string) => {
  if (!dateString || typeof dateString !== 'string') return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatServiceType = (serviceType?: string) => {
  if (!serviceType || typeof serviceType !== 'string') return 'N/A'
  return serviceType.split(' ').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

export default function QuotationViewModal({
  isOpen,
  onClose,
  quotation
}: QuotationViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(quotation.status)

  const handleDownloadQuotation = async (quotationId: number) => {
    try {
      const response = await fetch(`/api/quotations/${quotationId}/pdf`, {
        method: 'GET',
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.download = `quotation-${quotationId}.pdf`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('Failed to download quotation PDF')
      }
    } catch (error) {
      console.error('Error downloading PDF:', error)
      alert('Error downloading quotation PDF')
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={onClose} />
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20">
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Quotation Details</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <StatusIcon className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm font-medium text-gray-900">Status</span>
              </div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(quotation.status)}`}>
                {quotation.status || 'Unknown'}
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Service Type</label>
                <p className="mt-1 text-sm text-gray-900">{formatServiceType(quotation.servicename)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Request Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {quotation.requestdate ? formatDate(quotation.requestdate) : 'N/A'}
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Budget</label>
                <p className="mt-1 text-sm text-gray-900">
                  {quotation.budgetrange || 'Not specified'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Timeline</label>
                <p className="mt-1 text-sm text-gray-900">{quotation.timeline || 'Not specified'}</p>
              </div>
            </div>
            {(quotation.quotedamount || quotation.estimatedtime || quotation.responsedate) && (
              <div className="border-t border-gray-200 pt-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Quote Response</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {quotation.quotedamount && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Quoted Amount</label>
                      <p className="mt-1 text-sm text-gray-900">{formatCurrency(quotation.quotedamount)}</p>
                    </div>
                  )}
                  {quotation.estimatedtime && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Estimated Time</label>
                      <p className="mt-1 text-sm text-gray-900">{quotation.estimatedtime}</p>
                    </div>
                  )}
                  {quotation.responsedate && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700">Response Date</label>
                      <p className="mt-1 text-sm text-gray-900">{formatDate(quotation.responsedate)}</p>
                    </div>
                  )}
                </div>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                {quotation.description || 'No description provided'}
              </p>
            </div>
            {quotation.notes && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Notes</label>
                <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{quotation.notes}</p>
              </div>
            )}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <span className="text-sm text-gray-500">Quote Request #{quotation.id}</span>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleDownloadQuotation(quotation.id)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                  Download Quote
                </button>
                {quotation.status?.toLowerCase() === 'quoted' && (
                  <>
                    <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                      <XMarkIcon className="h-4 w-4 mr-1" />
                      Decline
                    </button>
                    <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                      Accept Quote
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
