'use client'

import {
  XMarkIcon,
  CreditCardIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'

interface Payment {
  id: number
  amount: number
  paymentmethod: string
  status: string
  paymentdate?: string
  transactionid?: string
  description?: string
  invoiceid?: number
}

interface PaymentViewModalProps {
  isOpen: boolean
  onClose: () => void
  payment: Payment
}

const getStatusColor = (status: string) => {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200'
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'failed':
    case 'declined':
    case 'error':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'cancelled':
    case 'refunded':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  if (!status) return ClockIcon
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'paid':
      return CheckCircleIcon
    case 'pending':
    case 'processing':
      return ClockIcon
    case 'failed':
    case 'declined':
    case 'error':
      return ExclamationTriangleIcon
    case 'cancelled':
    case 'refunded':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const getPaymentMethodIcon = (method: string) => {
  if (!method) return CreditCardIcon
  switch (method.toLowerCase()) {
    case 'credit card':
    case 'debit card':
    case 'card':
    case 'stripe':
      return CreditCardIcon
    case 'bank transfer':
    case 'wire transfer':
    case 'ach':
      return BanknotesIcon
    case 'paypal':
      return CurrencyDollarIcon
    default:
      return CreditCardIcon
  }
}

const formatCurrency = (amount: number) => {
  if (!amount && amount !== 0) return 'N/A'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatPaymentMethod = (method: string) => {
  if (!method) return 'N/A'
  return method.split(' ').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

const handleDownloadReceipt = async (paymentId: number) => {
  try {
    const response = await fetch(`/api/payments/${paymentId}/receipt`, {
      method: 'GET',
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `payment-receipt-${paymentId}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } else {
      alert('Failed to download payment receipt')
    }
  } catch (error) {
    console.error('Error downloading receipt:', error)
    alert('Error downloading payment receipt')
  }
}

export default function PaymentViewModal({
  isOpen,
  onClose,
  payment
}: PaymentViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(payment.status)
  const PaymentMethodIcon = getPaymentMethodIcon(payment.paymentmethod)

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Background overlay */}
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" />

      {/* Modal container */}
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20">
        <div
          className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <CreditCardIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Payment Details</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="space-y-6">
            {/* Payment Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-medium text-gray-900">Payment #{payment.id}</h4>
                <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(payment.status)}`}>
                  <StatusIcon className="h-4 w-4 inline mr-1" />
                  {payment.status}
                </div>
              </div>
              <div className="text-center py-4">
                <p className="text-2xl font-bold text-gray-900 mb-1">
                  {formatCurrency(payment.amount)}
                </p>
                <p className="text-sm text-gray-600">
                  {payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success' ? 'Payment Successful' : `Payment ${payment.status}`}
                </p>
              </div>
            </div>

            {/* Payment Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Payment Information */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Information</label>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <PaymentMethodIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Method: </span>
                    <span className="ml-1 text-gray-900">{formatPaymentMethod(payment.paymentmethod)}</span>
                  </div>
                  {payment.paymentdate && (
                    <div className="flex items-center text-sm">
                      <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Date: </span>
                      <span className="ml-1 text-gray-900">{formatDate(payment.paymentdate)}</span>
                    </div>
                  )}
                  {payment.invoiceid && (
                    <div className="flex items-center text-sm">
                      <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Invoice: </span>
                      <span className="ml-1 text-blue-600 font-medium">#{payment.invoiceid}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Transaction Details */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Transaction Details</label>
                <div className="space-y-2">
                  {payment.transactionid && (
                    <div className="flex items-center text-sm">
                      <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <span className="text-gray-600">Transaction ID: </span>
                      <span className="ml-1 text-gray-900 font-mono text-xs break-all">{payment.transactionid}</span>
                    </div>
                  )}
                  <div className="flex items-center text-sm">
                    <CheckCircleIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Status: </span>
                    <span className={`ml-1 font-medium ${
                      payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success'
                        ? 'text-green-600'
                        : payment.status?.toLowerCase() === 'failed' || payment.status?.toLowerCase() === 'declined'
                        ? 'text-red-600'
                        : 'text-yellow-600'
                    }`}>
                      {payment.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            {payment.description && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <div className="bg-gray-50 rounded-lg p-3">
                  <p className="text-sm text-gray-700">{payment.description}</p>
                </div>
              </div>
            )}

            {/* Status Messages */}
            {(payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success') && (
              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                  <p className="text-sm text-green-700 font-medium">
                    Payment processed successfully
                  </p>
                </div>
              </div>
            )}

            {(payment.status?.toLowerCase() === 'failed' || payment.status?.toLowerCase() === 'declined') && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mr-2" />
                  <p className="text-sm text-red-700 font-medium">
                    Payment failed to process
                  </p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <span className="text-sm text-gray-500">Payment #{payment.id}</span>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleDownloadReceipt(payment.id)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  <ArrowDownTrayIcon className="h-4 w-4 mr-1" />
                  Download Receipt
                </button>
                {payment.invoiceid && (
                  <button className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                    <DocumentTextIcon className="h-4 w-4 mr-1" />
                    View Invoice
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
