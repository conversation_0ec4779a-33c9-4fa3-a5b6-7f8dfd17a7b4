'use client'

import {
  XMarkIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ClockIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  TagIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  FolderIcon
} from '@heroicons/react/24/outline'

interface Project {
  id: number
  name: string
  description: string
  status: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
}

interface ProjectViewModalProps {
  isOpen: boolean
  onClose: () => void
  project: Project
}

const getStatusColor = (status: string) => {
  if (!status) return 'bg-gray-100 text-gray-800 border-gray-200'
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'in progress':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'planning':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'on hold':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  if (!status) return ClockIcon
  switch (status.toLowerCase()) {
    case 'completed':
      return CheckCircleIcon
    case 'in progress':
      return PlayIcon
    case 'planning':
      return ClockIcon
    case 'on hold':
      return ExclamationTriangleIcon
    case 'cancelled':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount: number) => {
  if (!amount && amount !== 0) return 'N/A'
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export default function ProjectViewModal({
  isOpen,
  onClose,
  project
}: ProjectViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(project.status)
  const tags = project.tags ? project.tags.split(',').map(tag => tag.trim()) : []

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Background overlay */}
      <div className="fixed inset-0 bg-gray-500 bg-opacity-75" />

      {/* Modal container */}
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20">
        <div
          className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto p-6"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <FolderIcon className="h-6 w-6 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Project Details</h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="space-y-6">
            {/* Project Name and Status */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-lg font-medium text-gray-900">{project.name}</h4>
                <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status)}`}>
                  <StatusIcon className="h-4 w-4 inline mr-1" />
                  {project.status}
                </div>
              </div>
              {project.description && (
                <p className="text-sm text-gray-600">{project.description}</p>
              )}
            </div>

            {/* Project Image */}
            {project.imageurl && (
              <div>
                <img
                  src={project.imageurl}
                  alt={project.name}
                  className="w-full h-48 object-cover rounded-lg shadow-sm"
                />
              </div>
            )}

            {/* Project Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Timeline */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Timeline</label>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Start: </span>
                    <span className="ml-1 text-gray-900">
                      {project.projstartdate ? formatDate(project.projstartdate) : 'Not set'}
                    </span>
                  </div>
                  <div className="flex items-center text-sm">
                    <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">End: </span>
                    <span className="ml-1 text-gray-900">
                      {project.projcompletiondate ? formatDate(project.projcompletiondate) : 'Not set'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Cost & Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Estimates</label>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <CurrencyDollarIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Cost: </span>
                    <span className="ml-1 text-gray-900">
                      {project.estimatecost ? formatCurrency(project.estimatecost) : 'Not specified'}
                    </span>
                  </div>
                  <div className="flex items-center text-sm">
                    <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Time: </span>
                    <span className="ml-1 text-gray-900">
                      {project.estimatetime || 'Not specified'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Links */}
            {(project.projecturl || project.githuburl) && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Links</label>
                <div className="space-y-2">
                  {project.projecturl && (
                    <div className="flex items-center">
                      <GlobeAltIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a
                        href={project.projecturl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 break-all"
                      >
                        {project.projecturl}
                      </a>
                    </div>
                  )}
                  {project.githuburl && (
                    <div className="flex items-center">
                      <CodeBracketIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a
                        href={project.githuburl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 break-all"
                      >
                        {project.githuburl}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Tags */}
            {tags.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      <TagIcon className="h-3 w-3 mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t border-gray-200">
              <span className="text-sm text-gray-500">Project #{project.id}</span>
              <div className="flex space-x-3">
                {project.projecturl && (
                  <a
                    href={project.projecturl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <GlobeAltIcon className="h-4 w-4 mr-1" />
                    View Project
                  </a>
                )}
                {project.githuburl && (
                  <a
                    href={project.githuburl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900"
                  >
                    <CodeBracketIcon className="h-4 w-4 mr-1" />
                    View Code
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
