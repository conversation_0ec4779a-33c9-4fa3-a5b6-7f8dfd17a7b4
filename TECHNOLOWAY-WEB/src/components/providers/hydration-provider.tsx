'use client';

import { useEffect, ReactNode } from 'react';

interface HydrationProviderProps {
  children: ReactNode;
}

export function HydrationProvider({ children }: HydrationProviderProps) {
  useEffect(() => {
    // Handle browser extension interference
    const handleBrowserExtensions = () => {
      // Suppress hydration warnings caused by browser extensions
      const originalError = console.error;
      console.error = function(...args) {
        const message = args[0];
        if (typeof message === 'string') {
          // Suppress specific hydration errors caused by browser extensions
          if (
            message.includes('hydrated but some attributes') ||
            message.includes('abId') ||
            message.includes('browser extension') ||
            message.includes('A tree hydrated but some attributes of the server rendered HTML') ||
            message.includes('This can happen if the client has a browser extension installed') ||
            message.includes('NewsletterForm') ||
            message.includes('FormWrapper') ||
            message.includes('max-w-md mx-auto') ||
            message.includes('flex flex-col sm:flex-row gap-4')
          ) {
            return; // Suppress these errors
          }
        }
        originalError.apply(console, args);
      };

      // Clean up browser extension artifacts
      const cleanupExtensionArtifacts = () => {
        // Remove random IDs added by A/B testing tools
        const elementsWithRandomIds = document.querySelectorAll('[id^="abId"]');
        elementsWithRandomIds.forEach(element => {
          if (element.id.match(/^abId\d+\.\d+$/)) {
            element.removeAttribute('id');
          }
        });

        // Remove other common extension attributes
        const extensionAttributes = [
          'abframeid',
          'abineguid',
          'data-pwm-inline',
          'data-ab-test',
          'data-analytics-id'
        ];

        extensionAttributes.forEach(attr => {
          const elements = document.querySelectorAll(`[${attr}]`);
          elements.forEach(element => {
            element.removeAttribute(attr);
          });
        });
      };

      // Run cleanup multiple times to catch extensions that load later
      cleanupExtensionArtifacts();
      setTimeout(cleanupExtensionArtifacts, 100);
      setTimeout(cleanupExtensionArtifacts, 500);
      setTimeout(cleanupExtensionArtifacts, 1000);

      // Set up a mutation observer to handle dynamic extension injections
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes') {
            const target = mutation.target as Element;
            const attributeName = mutation.attributeName;
            
            // Remove problematic attributes added by extensions
            if (attributeName && (
              attributeName.startsWith('ab') ||
              attributeName.includes('pwm') ||
              attributeName.includes('analytics')
            )) {
              target.removeAttribute(attributeName);
            }
            
            // Remove random IDs
            if (attributeName === 'id' && target.id && target.id.match(/^abId\d+\.\d+$/)) {
              target.removeAttribute('id');
            }
          }
        });
      });

      // Start observing
      observer.observe(document.body, {
        attributes: true,
        subtree: true,
        attributeFilter: ['id', 'abframeid', 'abineguid', 'data-pwm-inline', 'data-ab-test']
      });

      // Cleanup function
      return () => {
        observer.disconnect();
        console.error = originalError;
      };
    };

    const cleanup = handleBrowserExtensions();
    
    return cleanup;
  }, []);

  return <>{children}</>;
}
