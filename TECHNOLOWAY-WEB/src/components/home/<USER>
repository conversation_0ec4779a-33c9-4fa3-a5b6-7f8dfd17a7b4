'use client'

import Link from 'next/link';
import {
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CalendarIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { useStaticContent } from '@/lib/hooks/use-static-content';

export function ContactSection() {
  const { getContent } = useStaticContent();
  return (
    <section id="contact" className="py-24 bg-white">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {getContent('home', 'contact', 'title', 'Get in')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'contact', 'title_highlight', 'Touch')}</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {getContent('home', 'contact', 'subtitle', 'Ready to start your project? Contact us today for a free consultation.')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Info */}
          <div className="service-card space-y-8">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-8">{getContent('home', 'contact', 'info_title', 'Contact Information')}</h3>
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-14 h-14 bg-[#d0ebff]/30 rounded-xl">
                    <EnvelopeIcon className="w-7 h-7 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{getContent('home', 'contact', 'email_label', 'Email')}</p>
                    <a href={`mailto:${getContent('home', 'contact', 'email', '<EMAIL>')}`} className="text-lg text-gray-900 hover:text-blue-600 transition-colors">
                      {getContent('home', 'contact', 'email', '<EMAIL>')}
                    </a>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-14 h-14 bg-[#e0c3fc]/30 rounded-xl">
                    <PhoneIcon className="w-7 h-7 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{getContent('home', 'contact', 'phone_label', 'Phone')}</p>
                    <a href={`tel:${getContent('home', 'contact', 'phone', '+15551234567').replace(/\D/g, '')}`} className="text-lg text-gray-900 hover:text-purple-600 transition-colors">
                      {getContent('home', 'contact', 'phone', '+****************')}
                    </a>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-14 h-14 bg-[#d0ebff]/30 rounded-xl">
                    <MapPinIcon className="w-7 h-7 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">{getContent('home', 'contact', 'location_label', 'Location')}</p>
                    <p className="text-lg text-gray-900">{getContent('home', 'contact', 'location', 'San Francisco, CA')}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-8">
              <Link
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                <CalendarIcon className="mr-2 h-5 w-5" />
                Schedule a Call
              </Link>
            </div>

            {/* Live Chat Widget */}
            <div className="bg-gradient-to-br from-[#d0ebff]/10 to-[#e0c3fc]/10 p-6 rounded-2xl border border-gray-200">
              <div className="flex items-center mb-4">
                <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600 mr-3" />
                <h4 className="font-semibold text-gray-900">Live Chat Support</h4>
              </div>
              <p className="text-gray-600 mb-4">
                Need immediate assistance? Our team is available 24/7 to help with your questions.
              </p>
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Start Chat
              </button>
            </div>
          </div>

          {/* Contact Form */}
          <div className="service-card bg-gradient-to-br from-white to-[#d0ebff]/5 p-8 rounded-3xl border border-gray-200 shadow-lg">
            <form className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                    placeholder="John"
                  />
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                    placeholder="Doe"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  id="company"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                  placeholder="Your Company"
                />
              </div>

              <div>
                <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Budget
                </label>
                <select
                  id="budget"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                >
                  <option value="">Select budget range</option>
                  <option value="5k-15k">$5,000 - $15,000</option>
                  <option value="15k-50k">$15,000 - $50,000</option>
                  <option value="50k-100k">$50,000 - $100,000</option>
                  <option value="100k+">$100,000+</option>
                </select>
              </div>

              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Details
                </label>
                <textarea
                  id="message"
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                  placeholder="Tell us about your project requirements..."
                />
              </div>

              <button
                type="submit"
                className="w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                Send Message
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}
