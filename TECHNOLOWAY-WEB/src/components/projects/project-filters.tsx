'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ChevronDownIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface ProjectFiltersProps {
  selectedFilters: {
    service: string
    technology: string
    featured: boolean
  }
  onFiltersChange: (filters: any) => void
}

export default function ProjectFilters({
  selectedFilters,
  onFiltersChange
}: ProjectFiltersProps) {
  const [services, setServices] = useState<Array<{ id: string; name: string }>>([])
  const [technologies, setTechnologies] = useState<Array<{ id: string; name: string }>>([])
  const [loading, setLoading] = useState(true)

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const [servicesRes, technologiesRes] = await Promise.all([
          fetch('/api/services'),
          fetch('/api/technologies')
        ])

        if (servicesRes.ok) {
          const servicesData = await servicesRes.json()
          setServices(servicesData.data || [])
        }

        if (technologiesRes.ok) {
          const technologiesData = await technologiesRes.json()
          setTechnologies(technologiesData.data || [])
        }
      } catch (error) {
        console.error('Error fetching filter options:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFilterOptions()
  }, [])

  const handleFilterChange = (key: string, value: any) => {
    onFiltersChange({
      ...selectedFilters,
      [key]: value
    })
  }

  const clearAllFilters = () => {
    onFiltersChange({
      service: '',
      technology: '',
      featured: false
    })
  }

  const hasActiveFilters = selectedFilters.service || selectedFilters.technology || selectedFilters.featured

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white border border-gray-200 rounded-lg p-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Filter Projects</h3>
        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="flex items-center space-x-1 text-sm text-blue-600 hover:text-blue-800"
          >
            <XMarkIcon className="h-4 w-4" />
            <span>Clear all</span>
          </button>
        )}
      </div>

      {/* Filters Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Service Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Service Type
          </label>
          <div className="relative">
            <select
              value={selectedFilters.service}
              onChange={(e) => handleFilterChange('service', e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md appearance-none"
            >
              <option value="">All Services</option>
              {services.map((service) => (
                <option key={service.id} value={service.name}>
                  {service.name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDownIcon className="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Technology Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Technology
          </label>
          <div className="relative">
            <select
              value={selectedFilters.technology}
              onChange={(e) => handleFilterChange('technology', e.target.value)}
              className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md appearance-none"
            >
              <option value="">All Technologies</option>
              {technologies.map((technology) => (
                <option key={technology.id} value={technology.name}>
                  {technology.name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <ChevronDownIcon className="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        {/* Featured Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Project Type
          </label>
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={selectedFilters.featured}
                onChange={(e) => handleFilterChange('featured', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-sm text-gray-700">Featured only</span>
            </label>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {selectedFilters.service && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                Service: {selectedFilters.service}
                <button
                  onClick={() => handleFilterChange('service', '')}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </span>
            )}
            {selectedFilters.technology && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                Tech: {selectedFilters.technology}
                <button
                  onClick={() => handleFilterChange('technology', '')}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </span>
            )}
            {selectedFilters.featured && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                Featured
                <button
                  onClick={() => handleFilterChange('featured', false)}
                  className="ml-2 text-yellow-600 hover:text-yellow-800"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </motion.div>
  )
}
