'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'
import {
  StarIcon,
  EyeIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline'
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid'
import { makeAbsoluteUrl } from '@/lib/utils/url-utils'

interface ProjectCardProps {
  project: {
    id: string
    slug: string
    name: string
    description: string
    excerpt?: string
    imageUrl?: string
    projectUrl?: string
    githubUrl?: string
    startDate?: string
    completionDate?: string
    estimatedCost?: number
    isFeatured: boolean
    tagsArray?: string[]
    averageRating?: number
    client?: {
      companyName: string
    }
    services?: Array<{
      id: string
      name: string
    }>
    technologies?: Array<{
      id: string
      name: string
      iconUrl?: string
    }>
    _count?: {
      feedbacks: number
    }
  }
  variant?: 'default' | 'featured' | 'compact'
  showActions?: boolean
}

export default function ProjectCard({ 
  project, 
  variant = 'default',
  showActions = true 
}: ProjectCardProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i}>
        {i < Math.floor(rating) ? (
          <StarIconSolid className="h-4 w-4 text-yellow-400" />
        ) : (
          <StarIcon className="h-4 w-4 text-gray-300" />
        )}
      </span>
    ))
  }

  const cardVariants = {
    default: 'bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300',
    featured: 'bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-blue-200',
    compact: 'bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300'
  }

  return (
    <motion.div
      className={cardVariants[variant]}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      {/* Featured Badge */}
      {project.isFeatured && variant !== 'compact' && (
        <div className="absolute top-4 right-4 z-10">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            ⭐ Featured
          </span>
        </div>
      )}

      {/* Project Image */}
      <div className="relative h-48 w-full overflow-hidden rounded-t-xl">
        {project.imageUrl && !imageError ? (
          <Image
            src={makeAbsoluteUrl(project.imageUrl) || project.imageUrl}
            alt={project.name}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="h-full w-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <CodeBracketIcon className="h-16 w-16 text-gray-400" />
          </div>
        )}
        
        {/* Overlay with actions */}
        {showActions && (
          <div className={`absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center space-x-3 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
            <Link
              href={`/projects/${project.slug}`}
              className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
              title="View Details"
            >
              <EyeIcon className="h-5 w-5" />
            </Link>
            {project.projectUrl && (
              <a
                href={project.projectUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                title="Visit Project"
              >
                <ArrowTopRightOnSquareIcon className="h-5 w-5" />
              </a>
            )}
          </div>
        )}
      </div>

      {/* Project Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <Link href={`/projects/${project.slug}`}>
            <h3 className="text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2">
              {project.name}
            </h3>
          </Link>
          
          {project.client && (
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <BuildingOfficeIcon className="h-4 w-4 mr-1" />
              <span>{project.client.companyName}</span>
            </div>
          )}
        </div>

        {/* Description */}
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">
          {project.excerpt || project.description}
        </p>

        {/* Services */}
        {project.services && project.services.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {project.services.slice(0, 2).map((service) => (
                <span
                  key={service.id}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {service.name}
                </span>
              ))}
              {project.services.length > 2 && (
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-600">
                  +{project.services.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Technologies */}
        {project.technologies && project.technologies.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {project.technologies.slice(0, 4).map((tech) => (
                <div key={tech.id} className="flex items-center space-x-1">
                  {tech.iconUrl ? (
                    <Image
                      src={makeAbsoluteUrl(tech.iconUrl) || tech.iconUrl}
                      alt={tech.name}
                      width={16}
                      height={16}
                      className="rounded"
                    />
                  ) : (
                    <div className="w-4 h-4 bg-gray-300 rounded"></div>
                  )}
                  <span className="text-xs text-gray-600">{tech.name}</span>
                </div>
              ))}
              {project.technologies.length > 4 && (
                <span className="text-xs text-gray-500">
                  +{project.technologies.length - 4} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Tags */}
        {project.tagsArray && Array.isArray(project.tagsArray) && project.tagsArray.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {project.tagsArray.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700"
                >
                  #{tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          {/* Rating */}
          <div className="flex items-center space-x-2">
            {project.averageRating && project.averageRating > 0 && (
              <>
                <div className="flex items-center space-x-1">
                  {renderStars(project.averageRating)}
                </div>
                <span className="text-sm text-gray-600">
                  {project.averageRating.toFixed(1)} ({project._count?.feedbacks || 0})
                </span>
              </>
            )}
          </div>

          {/* Timeline */}
          {project.completionDate && (
            <div className="flex items-center text-sm text-gray-500">
              <CalendarIcon className="h-4 w-4 mr-1" />
              <span>{formatDate(project.completionDate)}</span>
            </div>
          )}
        </div>

        {/* Budget (for featured projects) */}
        {variant === 'featured' && project.estimatedCost && (
          <div className="mt-4 pt-4 border-t border-blue-200">
            <div className="text-center">
              <span className="text-sm text-gray-600">Project Value</span>
              <div className="text-lg font-bold text-blue-600">
                {formatCurrency(project.estimatedCost)}
              </div>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}
