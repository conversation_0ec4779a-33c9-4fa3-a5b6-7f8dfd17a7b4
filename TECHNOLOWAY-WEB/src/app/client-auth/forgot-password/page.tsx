'use client'

import { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import {
  EnvelopeIcon,
  ArrowLeftIcon,
  BuildingOfficeIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { toast, Toaster } from 'react-hot-toast'

const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .transform(val => val.toLowerCase().trim()),
})

type ForgotPasswordForm = z.infer<typeof forgotPasswordSchema>

export default function ForgotPasswordPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [emailHasValue, setEmailHasValue] = useState(false)
  const { theme, setTheme } = useTheme()
  const emailRef = useRef<HTMLInputElement>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ForgotPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
  })

  const watchedEmail = watch('email')

  const checkInputValue = (input: HTMLInputElement) => {
    return input.value.length > 0
  }

  // Check input values on mount and when they change
  useEffect(() => {
    if (emailRef.current) {
      setEmailHasValue(checkInputValue(emailRef.current))
    }
  }, [])

  // Also check when watched values change
  useEffect(() => {
    setEmailHasValue(!!watchedEmail && watchedEmail.length > 0)
  }, [watchedEmail])

  useEffect(() => {
    setMounted(true)
  }, [])

  const onSubmit = async (data: ForgotPasswordForm) => {
    setIsLoading(true)

    try {
      const response = await fetch('/api/user/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (response.ok) {
        setIsSubmitted(true)
        toast.success('Password reset instructions sent to your email')
      } else {
        toast.error(result.error || 'Failed to send reset email. Please try again.')
      }
    } catch (error) {
      console.error('Forgot password error:', error)
      toast.error('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
      <Toaster position="top-right" />
      
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-700/25 bg-[size:20px_20px] opacity-60" />
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent dark:via-slate-900/50" />
      
      {/* Theme Toggle */}
      <div className="absolute top-4 right-4 z-10">
        <div className="flex items-center space-x-2 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg p-1 border border-slate-200/50 dark:border-slate-700/50">
          <button
            onClick={() => setTheme('light')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'light' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <SunIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'dark' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <MoonIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => setTheme('system')}
            className={`p-2 rounded-md transition-colors ${
              theme === 'system' 
                ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400' 
                : 'text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200'
            }`}
          >
            <ComputerDesktopIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative max-w-md w-full space-y-8">
        {/* Forgot Password Card */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-slate-700/50 p-8 space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <BuildingOfficeIcon className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              {isSubmitted ? 'Check Your Email' : 'Forgot Password?'}
            </h2>
            <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">
              {isSubmitted 
                ? 'We\'ve sent password reset instructions to your email address'
                : 'Enter your email address and we\'ll send you a link to reset your password'
              }
            </p>
          </div>

          {!isSubmitted ? (
            <>
              {/* Form */}
              <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
                {/* Email Field */}
                <div className="space-y-1">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <EnvelopeIcon className="h-5 w-5 text-slate-400" />
                    </div>
                    <input
                      {...register('email', {
                        onChange: () => {
                          if (emailRef.current) {
                            setEmailHasValue(checkInputValue(emailRef.current))
                          }
                        }
                      })}
                      ref={(e) => {
                        emailRef.current = e
                        register('email').ref(e)
                      }}
                      type="email"
                      autoComplete="email"
                      className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-white/50 dark:bg-slate-700/50 backdrop-blur-sm ${
                        errors.email
                          ? 'border-red-300 dark:border-red-600'
                          : 'border-slate-300 dark:border-slate-600'
                      }`}
                    />
                    <label 
                      className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                        emailHasValue || watchedEmail
                          ? '-top-2 text-xs bg-white dark:bg-slate-800 px-1 text-blue-600 dark:text-blue-400'
                          : 'top-3 text-sm text-slate-500 dark:text-slate-400'
                      }`}
                    >
                      Email Address
                    </label>
                  </div>
                  {errors.email && (
                    <p className="text-sm text-red-600 dark:text-red-400">{errors.email.message}</p>
                  )}
                </div>

                {/* Submit Button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                    {isLoading ? (
                      <svg className="animate-spin h-5 w-5 text-white/70" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    ) : (
                      <EnvelopeIcon className="h-5 w-5 text-white/70 group-hover:text-white transition-colors" />
                    )}
                  </span>
                  {isLoading ? 'Sending...' : 'Send Reset Link'}
                </button>
              </form>
            </>
          ) : (
            <div className="text-center space-y-4">
              <div className="mx-auto h-12 w-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                If an account with that email exists, you'll receive password reset instructions shortly.
              </p>
            </div>
          )}

          {/* Back to Sign In Link */}
          <div className="text-center">
            <Link 
              href="/client-auth/signin" 
              className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Sign In
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
