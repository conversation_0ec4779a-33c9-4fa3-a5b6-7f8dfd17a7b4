'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRightIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  UsersIcon,
  CpuChipIcon,
  RocketLaunchIcon,
  CheckCircleIcon,
  PlayIcon,
  StarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CalendarIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  NewspaperIcon,
  ChatBubbleLeftRightIcon,
  TrophyIcon,
  AcademicCapIcon,
  BoltIcon,
  SparklesIcon,
  HeartIcon,
  CurrencyDollarIcon,
  PaintBrushIcon,
  BeakerIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" }
};

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

// Data structures
const stats = [
  { name: 'Projects Delivered', value: '500+', icon: RocketLaunchIcon },
  { name: 'Happy Clients', value: '200+', icon: UsersIcon },
  { name: 'Years Experience', value: '10+', icon: ChartBarIcon },
  { name: 'Team Members', value: '50+', icon: CpuChipIcon },
];

const clientLogos = [
  { name: 'Microsoft', logo: 'https://logos-world.net/wp-content/uploads/2020/09/Microsoft-Logo.png', url: '#' },
  { name: 'Google', logo: 'https://logos-world.net/wp-content/uploads/2020/09/Google-Logo.png', url: '#' },
  { name: 'Amazon', logo: 'https://logos-world.net/wp-content/uploads/2020/04/Amazon-Logo.png', url: '#' },
  { name: 'Apple', logo: 'https://logos-world.net/wp-content/uploads/2020/04/Apple-Logo.png', url: '#' },
  { name: 'Meta', logo: 'https://logos-world.net/wp-content/uploads/2021/10/Meta-Logo.png', url: '#' },
  { name: 'Netflix', logo: 'https://logos-world.net/wp-content/uploads/2020/04/Netflix-Logo.png', url: '#' },
];

// Removed mock data - now using real data from database

const pricingPlans = [
  {
    name: 'Startup',
    price: '$5,000',
    period: 'per project',
    description: 'Perfect for small businesses and startups',
    features: [
      'Custom web application',
      'Responsive design',
      'Basic SEO optimization',
      '3 months support',
      'Source code included'
    ],
    popular: false
  },
  {
    name: 'Business',
    price: '$15,000',
    period: 'per project',
    description: 'Ideal for growing businesses',
    features: [
      'Full-stack application',
      'Advanced UI/UX design',
      'Database integration',
      'API development',
      '6 months support',
      'Performance optimization'
    ],
    popular: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: 'quote',
    description: 'For large-scale applications',
    features: [
      'Scalable architecture',
      'Microservices design',
      'Cloud deployment',
      'Security audit',
      '12 months support',
      'Dedicated team'
    ],
    popular: false
  }
];

// Mock testimonials removed - using real data from database

export default function Home() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [services, setServices] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [testimonials, setTestimonials] = useState<any[]>([]);
  const [technologies, setTechnologies] = useState<any[]>([]);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [blogPosts, setBlogPosts] = useState<any[]>([]);
  const [isNewsletterSubmitted, setIsNewsletterSubmitted] = useState(false);

  useEffect(() => {
    // Fetch services
    const fetchServices = async () => {
      try {
        const response = await fetch('/api/services?limit=6&filter=active');
        const result = await response.json();
        if (result.success) {
          setServices(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch services:', error);
      }
    };

    // Fetch featured projects
    const fetchProjects = async () => {
      try {
        const response = await fetch('/api/projects?featured=true&limit=3');
        const result = await response.json();
        if (result.success) {
          setProjects(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch projects:', error);
      }
    };

    // Fetch testimonials
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials?limit=5');
        const result = await response.json();
        if (result.success) {
          setTestimonials(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch testimonials:', error);
      }
    };

    // Fetch technologies
    const fetchTechnologies = async () => {
      try {
        const response = await fetch('/api/technologies?filter=active&limit=8');
        const result = await response.json();
        if (result.success) {
          setTechnologies(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch technologies:', error);
      }
    };

    // Fetch team members
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/team?filter=active&limit=4');
        const result = await response.json();
        if (result.success) {
          setTeamMembers(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error);
      }
    };

    // Fetch recent blog posts
    const fetchBlogPosts = async () => {
      try {
        const response = await fetch('/api/blog?filter=published&limit=3');
        const result = await response.json();
        if (result.success) {
          setBlogPosts(result.data || []);
        }
      } catch (error) {
        console.error('Failed to fetch blog posts:', error);
      }
    };

    fetchServices();
    fetchProjects();
    fetchTestimonials();
    fetchTechnologies();
    fetchTeamMembers();
    fetchBlogPosts();
  }, []);

  // Auto-rotate testimonials
  useEffect(() => {
    if (testimonials.length > 0) {
      const timer = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }, 5000);
      return () => clearInterval(timer);
    }
  }, [testimonials]);

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsNewsletterSubmitted(true);
    setTimeout(() => setIsNewsletterSubmitted(false), 3000);
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main>
        {/* 1. Hero Section (Above the Fold) */}
        <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
          {/* Background Image */}
          <div className="absolute inset-0">
            <Image
              src="https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1920&h=1080&fit=crop&crop=center"
              alt="Technology and innovation background"
              fill
              className="object-cover"
              priority
            />
            {/* Overlay for better text readability */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/80 via-purple-900/70 to-blue-800/80"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
          </div>

          {/* Enhanced 3D Floating Elements */}
          <div className="absolute inset-0 overflow-hidden">
            {/* Large floating orb with 3D effect */}
            <motion.div
              animate={{
                y: [0, -30, 0],
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute top-1/4 left-1/4 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-full blur-2xl"
              style={{
                background: 'radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), transparent)',
                filter: 'blur(20px)',
              }}
            />

            {/* Medium floating cube with rotation */}
            <motion.div
              animate={{
                y: [0, 40, 0],
                rotateX: [0, 360],
                rotateY: [0, 180],
              }}
              transition={{
                duration: 10,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2
              }}
              className="absolute top-1/3 right-1/4 w-32 h-32"
              style={{
                background: 'linear-gradient(45deg, rgba(168, 85, 247, 0.2), rgba(59, 130, 246, 0.2))',
                borderRadius: '20px',
                filter: 'blur(15px)',
                transform: 'perspective(1000px) rotateX(45deg) rotateY(45deg)',
              }}
            />

            {/* Small floating triangle */}
            <motion.div
              animate={{
                y: [0, -25, 0],
                x: [0, 15, 0],
                rotate: [0, 120, 240, 360],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 4
              }}
              className="absolute bottom-1/4 left-1/3 w-24 h-24"
              style={{
                background: 'conic-gradient(from 0deg, rgba(34, 197, 94, 0.2), rgba(59, 130, 246, 0.2), rgba(168, 85, 247, 0.2))',
                clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                filter: 'blur(12px)',
              }}
            />

            {/* Additional floating elements for depth */}
            <motion.div
              animate={{
                y: [0, 20, 0],
                x: [0, -10, 0],
                rotateZ: [0, 180, 360],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 1
              }}
              className="absolute top-1/2 right-1/3 w-16 h-16 bg-gradient-to-tr from-emerald-400/20 to-blue-500/20 rounded-full blur-xl"
            />

            <motion.div
              animate={{
                y: [0, -35, 0],
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 9,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 6
              }}
              className="absolute bottom-1/3 right-1/4 w-28 h-28"
              style={{
                background: 'radial-gradient(ellipse at center, rgba(236, 72, 153, 0.2), transparent)',
                filter: 'blur(18px)',
              }}
            />
          </div>

          <div className="container relative z-10 px-6 mx-auto">
            <div className="text-center max-w-6xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="mb-8"
              >
                <span className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm px-6 py-3 text-sm font-medium text-white border border-white/20">
                  <SparklesIcon className="w-4 h-4 mr-2 text-blue-300" />
                  Crafting Digital Excellence Since 2014
                </span>
              </motion.div>

              {/* Bold headline & sub headline */}
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight text-white mb-8"
              >
                Build the Future
                <br />
                <span className="bg-gradient-to-r from-blue-300 via-purple-300 to-blue-200 bg-clip-text text-transparent">
                  Today
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-lg md:text-xl text-gray-200 max-w-2xl mx-auto lg:mx-0 mb-12 leading-relaxed"
              >
                We transform innovative ideas into powerful software solutions that drive business growth.
                From startups to enterprises, we deliver cutting-edge technology with exceptional user experiences.
              </motion.p>

              {/* CTA buttons */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="flex flex-col sm:flex-row gap-6 justify-center mb-16"
              >
                <Link
                  href="#contact"
                  className="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:-translate-y-1"
                >
                  Get a Quote
                  <ArrowRightIcon className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Link>
                <Link
                  href="#team"
                  className="group inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/20 hover:bg-white/20 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                >
                  <UsersIcon className="mr-2 h-5 w-5" />
                  Talk to an Expert
                </Link>
              </motion.div>

              {/* Stats counters */}
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="grid grid-cols-2 md:grid-cols-4 gap-8"
              >
                {stats.map((stat, index) => {
                  const Icon = stat.icon;
                  return (
                    <motion.div
                      key={stat.name}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
                      className="group text-center"
                    >
                      <div className="relative p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                        <div className="inline-flex p-3 bg-gradient-to-br from-blue-400 to-purple-500 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                          <Icon className="h-6 w-6 text-white" />
                        </div>
                        <div className="text-3xl font-bold text-white mb-2">
                          {stat.value}
                        </div>
                        <div className="text-sm text-gray-300 font-medium">
                          {stat.name}
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </motion.div>
            </div>
          </div>
        </section>

        {/* 2. Client Logos / Trust Signals */}
        <section className="py-16 bg-gradient-to-br from-gray-50 to-blue-50/30">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <p className="text-sm font-semibold text-gray-600 mb-8">
                Trusted by industry leaders worldwide
              </p>
            </motion.div>

            <div className="overflow-hidden">
              <motion.div
                animate={{ x: [0, -100 * clientLogos.length] }}
                transition={{
                  x: {
                    repeat: Infinity,
                    repeatType: "loop",
                    duration: 20,
                    ease: "linear",
                  },
                }}
                className="flex space-x-12"
              >
                {[...clientLogos, ...clientLogos].map((client, index) => (
                  <Link
                    key={`${client.name}-${index}`}
                    href={client.url}
                    className="flex-shrink-0 group"
                  >
                    <div className="w-32 h-16 bg-white rounded-lg border border-gray-200 flex items-center justify-center hover:shadow-lg hover:border-[#d0ebff] transition-all duration-300 hover:-translate-y-1 p-4">
                      <Image
                        src={client.logo}
                        alt={client.name}
                        width={120}
                        height={60}
                        className="max-w-full max-h-full object-contain filter grayscale group-hover:grayscale-0 transition-all duration-300"
                      />
                    </div>
                  </Link>
                ))}
              </motion.div>
            </div>
          </div>
        </section>

        {/* 3. Services Overview */}
        <section className="py-24 bg-gradient-to-br from-white via-blue-50/20 to-[#d0ebff]/10">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Services</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                From concept to deployment, we provide end-to-end software development services
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => {
                // Map service categories to icons
                const getServiceIcon = (category: string | null | undefined) => {
                  if (!category || typeof category !== 'string') {
                    return CodeBracketIcon;
                  }

                  switch (category.toLowerCase()) {
                    case 'web development':
                    case 'web':
                      return CodeBracketIcon;
                    case 'mobile development':
                    case 'mobile':
                      return DevicePhoneMobileIcon;
                    case 'ui/ux design':
                    case 'design':
                      return PaintBrushIcon;
                    case 'ai/ml':
                    case 'artificial intelligence':
                      return BeakerIcon;
                    case 'cloud':
                    case 'cloud solutions':
                      return CloudIcon;
                    case 'security':
                    case 'cybersecurity':
                      return ShieldCheckIcon;
                    default:
                      return CodeBracketIcon;
                  }
                };

                const Icon = getServiceIcon(service.category || service.name);

                return (
                  <motion.div
                    key={service.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Link
                      href={`/services/${service.slug || service.id}`}
                      className="group block h-full p-8 bg-white rounded-2xl border border-gray-200 hover:border-[#d0ebff] hover:shadow-xl transition-all duration-300 hover:-translate-y-2"
                    >
                      <div className="flex flex-col h-full">
                        <div className="inline-flex p-4 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl mb-6 group-hover:scale-110 transition-transform duration-300 w-fit">
                          <Icon className="h-8 w-8 text-blue-700" />
                        </div>

                        <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                          {service.name}
                        </h3>

                        <p className="text-gray-600 leading-relaxed mb-6 flex-grow">
                          {service.description || service.excerpt}
                        </p>

                        <div className="flex items-center text-blue-600 font-semibold group-hover:translate-x-2 transition-transform duration-300 mt-auto">
                          Learn more
                          <ArrowRightIcon className="ml-2 h-4 w-4" />
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* 4. Case Studies / Featured Projects */}
        <section className="py-24 bg-gradient-to-br from-purple-50/30 to-white">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Featured <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Case Studies</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Real projects, real results. See how we've helped businesses transform with technology.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
              {projects.slice(0, 2).map((project, index) => {
                // Get project icon based on category or type
                const getProjectIcon = (category: string | null | undefined) => {
                  if (!category || typeof category !== 'string') {
                    return RocketLaunchIcon;
                  }

                  switch (category.toLowerCase()) {
                    case 'healthcare':
                    case 'health':
                      return HeartIcon;
                    case 'finance':
                    case 'fintech':
                    case 'banking':
                      return CurrencyDollarIcon;
                    case 'ecommerce':
                    case 'retail':
                      return GlobeAltIcon;
                    case 'education':
                      return AcademicCapIcon;
                    default:
                      return RocketLaunchIcon;
                  }
                };

                const Icon = getProjectIcon(project.category || '');
                const gradientColors = index === 0
                  ? 'from-[#d0ebff] to-[#e0c3fc]'
                  : 'from-[#e0c3fc] to-[#d0ebff]';
                const textColor = index === 0 ? 'text-blue-700' : 'text-purple-700';
                const hoverColor = index === 0 ? 'group-hover:text-blue-600' : 'group-hover:text-purple-600';

                return (
                  <motion.div
                    key={project.id}
                    initial={{ opacity: 0, x: index === 0 ? -20 : 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  >
                    <Link href={`/projects/${project.slug || project.id}`} className="group block">
                      <div className="bg-gradient-to-br from-white to-[#d0ebff]/10 rounded-3xl border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-500 hover:-translate-y-2">
                        {project.imageUrl ? (
                          <div className="h-64 relative overflow-hidden">
                            <Image
                              src={project.imageUrl}
                              alt={project.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-500"
                            />
                            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                            <div className="absolute bottom-4 left-4">
                              <h3 className="text-2xl font-bold text-white">{project.name}</h3>
                            </div>
                          </div>
                        ) : (
                          <div className={`h-64 bg-gradient-to-br ${gradientColors} flex items-center justify-center`}>
                            <div className="text-center p-8">
                              <div className="inline-flex p-6 bg-white/20 backdrop-blur-sm rounded-2xl mb-4">
                                <Icon className={`h-16 w-16 ${textColor}`} />
                              </div>
                              <h3 className={`text-2xl font-bold ${textColor.replace('text-', 'text-').replace('-700', '-900')}`}>
                                {project.name}
                              </h3>
                            </div>
                          </div>
                        )}

                        <div className="p-8">
                          <div className="mb-6">
                            {project.category && (
                              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[${index === 0 ? '#d0ebff' : '#e0c3fc'}]/30 ${textColor} mb-4`}>
                                {project.category} • {project.type || 'Web Platform'}
                              </span>
                            )}
                            <h3 className={`text-2xl font-bold text-gray-900 mb-4 ${hoverColor} transition-colors duration-300`}>
                              {project.excerpt || project.name}
                            </h3>
                            <p className="text-gray-600 leading-relaxed mb-6">
                              {project.description}
                            </p>
                          </div>

                          {project.metrics && (
                            <div className="grid grid-cols-3 gap-4 mb-6">
                              {project.metrics.slice(0, 3).map((metric: any, metricIndex: number) => (
                                <div key={metricIndex} className="text-center">
                                  <div className={`text-2xl font-bold ${index === 0 ? 'text-blue-600' : 'text-purple-600'}`}>
                                    {metric.value}
                                  </div>
                                  <div className="text-sm text-gray-600">{metric.label}</div>
                                </div>
                              ))}
                            </div>
                          )}

                          <div className={`flex items-center ${index === 0 ? 'text-blue-600' : 'text-purple-600'} font-semibold group-hover:translate-x-2 transition-transform duration-300`}>
                            Read Case Study
                            <ArrowRightIcon className="ml-2 h-5 w-5" />
                          </div>
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                );
              })}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <Link
                href="/case-studies"
                className="inline-flex items-center px-8 py-4 bg-white border border-gray-200 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                View All Case Studies
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
            </motion.div>
          </div>
        </section>

        {/* 5. Testimonials / Client Quotes */}
        <section className="py-24 bg-gradient-to-br from-[#d0ebff]/8 via-blue-50/40 to-white">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                What Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Clients Say</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it. Here's what our clients have to say about working with us.
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto">
              {testimonials.length > 0 ? (
                <div className="relative">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentTestimonial}
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -50 }}
                      transition={{ duration: 0.5 }}
                      className="bg-white p-12 rounded-3xl border border-gray-200 shadow-lg"
                    >
                      <div className="text-center">
                        <div className="flex justify-center mb-6">
                          {[...Array(testimonials[currentTestimonial]?.rating || 5)].map((_, i) => (
                            <StarIconSolid key={i} className="w-6 h-6 text-yellow-400" />
                          ))}
                        </div>

                        <blockquote className="text-2xl text-gray-700 mb-8 leading-relaxed font-medium">
                          "{testimonials[currentTestimonial]?.content}"
                        </blockquote>

                        <div className="flex items-center justify-center">
                          {testimonials[currentTestimonial]?.clientPhotoUrl ? (
                            <Image
                              src={testimonials[currentTestimonial].clientPhotoUrl}
                              alt={testimonials[currentTestimonial].clientName}
                              width={64}
                              height={64}
                              className="rounded-full mr-4"
                            />
                          ) : (
                            <div className="w-16 h-16 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] rounded-full flex items-center justify-center mr-4">
                              <span className="text-blue-700 font-bold text-xl">
                                {testimonials[currentTestimonial]?.clientName?.charAt(0)}
                              </span>
                            </div>
                          )}
                          <div className="text-left">
                            <div className="font-bold text-gray-900 text-lg">
                              {testimonials[currentTestimonial]?.clientName}
                            </div>
                            <div className="text-gray-600">
                              {testimonials[currentTestimonial]?.clientTitle} at {testimonials[currentTestimonial]?.clientCompany}
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </AnimatePresence>

                  {/* Navigation */}
                  {testimonials.length > 1 && (
                    <>
                      <div className="flex justify-center mt-8 space-x-4">
                        <button
                          onClick={() => setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length)}
                          className="p-3 bg-white border border-gray-200 rounded-full hover:bg-[#d0ebff]/20 hover:border-[#d0ebff] transition-all duration-300"
                        >
                          <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
                        </button>
                        <button
                          onClick={() => setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)}
                          className="p-3 bg-white border border-gray-200 rounded-full hover:bg-[#d0ebff]/20 hover:border-[#d0ebff] transition-all duration-300"
                        >
                          <ChevronRightIcon className="h-5 w-5 text-gray-600" />
                        </button>
                      </div>

                      {/* Dots indicator */}
                      <div className="flex justify-center mt-6 space-x-2">
                        {testimonials.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentTestimonial(index)}
                            className={`w-3 h-3 rounded-full transition-all duration-300 ${
                              index === currentTestimonial
                                ? 'bg-blue-600'
                                : 'bg-gray-300 hover:bg-gray-400'
                            }`}
                          />
                        ))}
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div className="bg-white p-12 rounded-3xl border border-gray-200 shadow-lg text-center">
                  <p className="text-gray-600">No testimonials available at the moment.</p>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* 6. Technology Stack / Tools We Use */}
        <section className="py-24 bg-gradient-to-br from-gray-50 via-purple-50/20 to-white">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Tech Stack</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                We use cutting-edge technologies to build scalable, maintainable solutions
              </p>
            </motion.div>

            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
              {technologies.map((tech, index) => (
                <motion.div
                  key={tech.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group relative"
                >
                  <div className="bg-white p-6 rounded-2xl border border-gray-200 hover:border-[#d0ebff] hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                    {tech.iconUrl ? (
                      <div className="w-12 h-12 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                        <Image
                          src={tech.iconUrl}
                          alt={tech.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-contain"
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl flex items-center justify-center mb-3 mx-auto group-hover:scale-110 transition-transform duration-300">
                        <span className="text-blue-700 font-bold text-lg">{tech.name.charAt(0)}</span>
                      </div>
                    )}
                    <h3 className="text-sm font-semibold text-gray-900 text-center">{tech.name}</h3>
                    <p className="text-xs text-gray-500 text-center mt-1">{tech.category}</p>
                    {tech.proficiencyLevel && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-purple-600 h-1 rounded-full transition-all duration-300"
                            style={{ width: `${tech.proficiencyLevel}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                    {tech.name} - {tech.category}
                    {tech.proficiencyLevel && ` (${tech.proficiencyLevel}%)`}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* 7. About Us / Meet the Team */}
        <section id="team" className="py-24 bg-gradient-to-br from-[#e0c3fc]/8 via-purple-50/30 to-blue-50/20">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Meet Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Team</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Passionate experts dedicated to bringing your vision to life
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembers.map((member, index) => (
                <motion.div
                  key={member.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <div className="bg-white p-8 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 text-center h-full flex flex-col">
                    <div className="relative mb-6">
                      {member.photoUrl ? (
                        <Image
                          src={member.photoUrl}
                          alt={`${member.firstName} ${member.lastName}`}
                          width={120}
                          height={120}
                          className="rounded-full mx-auto group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="w-30 h-30 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] rounded-full flex items-center justify-center mx-auto group-hover:scale-105 transition-transform duration-300">
                          <span className="text-blue-700 font-bold text-3xl">
                            {member.firstName?.charAt(0)}{member.lastName?.charAt(0)}
                          </span>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-br from-[#d0ebff]/20 to-[#e0c3fc]/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    <h3 className="text-xl font-bold text-gray-900 mb-2">
                      {member.firstName} {member.lastName}
                    </h3>
                    <p className="text-blue-600 font-semibold mb-4">{member.position}</p>
                    {member.department && (
                      <p className="text-gray-500 text-sm mb-4">{member.department}</p>
                    )}
                    {member.bio && (
                      <p className="text-gray-600 text-sm leading-relaxed mb-6 flex-grow">{member.bio}</p>
                    )}

                    <div className="flex justify-center space-x-3 mt-auto">
                      {member.linkedInUrl && (
                        <Link
                          href={member.linkedInUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors duration-300"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clipRule="evenodd" />
                          </svg>
                        </Link>
                      )}
                      {member.githubUrl && (
                        <Link
                          href={member.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-gray-600 hover:text-gray-700 transition-colors duration-300"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                          </svg>
                        </Link>
                      )}
                      {member.twitterUrl && (
                        <Link
                          href={member.twitterUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-400 hover:text-blue-500 transition-colors duration-300"
                        >
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84" />
                          </svg>
                        </Link>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* 8. Blog / Insights Preview */}
        <section className="py-24 bg-gradient-to-br from-white via-gray-50/50 to-blue-50/30">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Latest <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Insights</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Stay updated with the latest trends, tips, and insights from our team
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              {blogPosts.length > 0 ? (
                blogPosts.map((post, index) => (
                  <motion.article
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <Link href={`/blog/${post.slug}`} className="group block h-full">
                      <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full flex flex-col">
                        {post.featuredImageUrl ? (
                          <div className="h-48 relative overflow-hidden">
                            <Image
                              src={post.featuredImageUrl}
                              alt={post.title}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          </div>
                        ) : (
                          <div className="h-48 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] flex items-center justify-center">
                            <NewspaperIcon className="h-16 w-16 text-blue-700" />
                          </div>
                        )}
                        <div className="p-6 flex flex-col flex-grow">
                          <div className="flex items-center text-sm text-gray-500 mb-3">
                            <span>
                              {new Date(post.publishedAt || post.createdAt).toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                              })}
                            </span>
                            <span className="mx-2">•</span>
                            <span>{post.readTime || '5 min read'}</span>
                          </div>
                          <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                            {post.title}
                          </h3>
                          <p className="text-gray-600 mb-4 flex-grow">
                            {post.excerpt || post.content?.substring(0, 150) + '...'}
                          </p>
                          {post.categories && (
                            <div className="flex flex-wrap gap-2 mb-4">
                              {post.categories.split(',').slice(0, 2).map((category: string, catIndex: number) => (
                                <span
                                  key={catIndex}
                                  className="px-2 py-1 bg-[#d0ebff]/30 text-blue-700 text-xs rounded-full"
                                >
                                  {category.trim()}
                                </span>
                              ))}
                            </div>
                          )}
                          <div className="flex items-center text-blue-600 font-semibold group-hover:translate-x-2 transition-transform duration-300 mt-auto">
                            Read More
                            <ArrowRightIcon className="ml-2 h-4 w-4" />
                          </div>
                        </div>
                      </div>
                    </Link>
                  </motion.article>
                ))
              ) : (
                // Fallback content when no blog posts are available
                [1, 2, 3].map((index) => (
                  <motion.article
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className="bg-white rounded-2xl border border-gray-200 overflow-hidden">
                      <div className="h-48 bg-gradient-to-br from-[#d0ebff] to-[#e0c3fc] flex items-center justify-center">
                        <NewspaperIcon className="h-16 w-16 text-blue-700" />
                      </div>
                      <div className="p-6">
                        <div className="flex items-center text-sm text-gray-500 mb-3">
                          <span>Coming Soon</span>
                          <span className="mx-2">•</span>
                          <span>5 min read</span>
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-3">
                          Blog Post Coming Soon
                        </h3>
                        <p className="text-gray-600 mb-4">
                          We're working on exciting content for you. Stay tuned for our latest insights and updates.
                        </p>
                      </div>
                    </div>
                  </motion.article>
                ))
              )}
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <Link
                href="/blog"
                className="inline-flex items-center px-8 py-4 bg-white border border-gray-200 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                View All Articles
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Link>
            </motion.div>
          </div>
        </section>
        {/* 9. Pricing Preview / Engagement Models */}
        <section className="py-24 bg-gradient-to-br from-[#d0ebff]/10 via-purple-50/40 to-[#e0c3fc]/8">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Simple <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Pricing</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the perfect plan for your project needs
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingPlans.map((plan, index) => (
                <motion.div
                  key={plan.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`relative p-8 bg-white rounded-2xl border-2 transition-all duration-300 hover:-translate-y-2 h-full flex flex-col ${
                    plan.popular
                      ? 'border-blue-500 shadow-xl shadow-blue-500/20'
                      : 'border-gray-200 hover:border-[#d0ebff] hover:shadow-xl'
                  }`}
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
                        Most Popular
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                    <p className="text-gray-600 mb-4">{plan.description}</p>
                    <div className="mb-4">
                      <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                      <span className="text-gray-600 ml-2">{plan.period}</span>
                    </div>
                  </div>

                  <ul className="space-y-4 mb-8 flex-grow">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    href="#contact"
                    className={`block w-full text-center px-6 py-3 rounded-xl font-semibold transition-all duration-300 mt-auto ${
                      plan.popular
                        ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:shadow-lg hover:shadow-blue-500/25'
                        : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                    }`}
                  >
                    Get Started
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* 10. Newsletter Signup / Resources Download */}
        <section className="py-24 bg-white">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="max-w-4xl mx-auto text-center"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Stay in the <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Loop</span>
              </h2>
              <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
                Get the latest insights, tips, and updates delivered straight to your inbox.
                Join thousands of developers and business leaders.
              </p>

              <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto">
                <div className="flex flex-col sm:flex-row gap-4">
                  <input
                    type="email"
                    placeholder="Enter your email"
                    required
                    className="flex-1 px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                  />
                  <button
                    type="submit"
                    disabled={isNewsletterSubmitted}
                    className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isNewsletterSubmitted ? 'Subscribed!' : 'Subscribe'}
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-4">
                  No spam, unsubscribe at any time. We respect your privacy.
                </p>
              </form>
            </motion.div>
          </div>
        </section>

        {/* 11. Call to Action Section */}
        <section className="py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="container px-6 mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center max-w-4xl mx-auto"
            >
              <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Let's Build Something
                <br />
                <span className="text-blue-200">Great Together</span>
              </h2>
              <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto">
                Ready to transform your ideas into reality? Get in touch and let's discuss your next project.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link
                  href="#contact"
                  className="inline-flex items-center px-8 py-4 bg-white text-gray-900 font-semibold rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl"
                >
                  Start Your Project
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="/case-studies"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1"
                >
                  View Our Work
                </Link>
              </div>
            </motion.div>
          </div>
        </section>

        {/* 12. Contact Form / Chat Widget */}
        <section id="contact" className="py-24 bg-white">
          <div className="container px-6 mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Get in <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Touch</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Ready to start your project? Contact us today for a free consultation.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
              {/* Contact Info */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-8">Contact Information</h3>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-14 h-14 bg-[#d0ebff]/30 rounded-xl">
                        <EnvelopeIcon className="w-7 h-7 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Email</p>
                        <a href="mailto:<EMAIL>" className="text-lg text-gray-900 hover:text-blue-600 transition-colors">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-14 h-14 bg-[#e0c3fc]/30 rounded-xl">
                        <PhoneIcon className="w-7 h-7 text-purple-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Phone</p>
                        <a href="tel:+15551234567" className="text-lg text-gray-900 hover:text-purple-600 transition-colors">
                          +****************
                        </a>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4">
                      <div className="flex items-center justify-center w-14 h-14 bg-[#d0ebff]/30 rounded-xl">
                        <MapPinIcon className="w-7 h-7 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-500">Location</p>
                        <p className="text-lg text-gray-900">San Francisco, CA</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="pt-8">
                  <Link
                    href="/schedule-demo"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                  >
                    <CalendarIcon className="mr-2 h-5 w-5" />
                    Schedule a Call
                  </Link>
                </div>

                {/* Live Chat Widget */}
                <div className="bg-gradient-to-br from-[#d0ebff]/10 to-[#e0c3fc]/10 p-6 rounded-2xl border border-gray-200">
                  <div className="flex items-center mb-4">
                    <ChatBubbleLeftRightIcon className="h-6 w-6 text-blue-600 mr-3" />
                    <h4 className="font-semibold text-gray-900">Live Chat Support</h4>
                  </div>
                  <p className="text-gray-600 mb-4">
                    Need immediate assistance? Our team is available 24/7 to help with your questions.
                  </p>
                  <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Start Chat
                  </button>
                </div>
              </motion.div>

              {/* Contact Form */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-gradient-to-br from-white to-[#d0ebff]/5 p-8 rounded-3xl border border-gray-200 shadow-lg"
              >
                <form className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                        First Name
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email
                    </label>
                    <input
                      type="email"
                      id="email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                      Company
                    </label>
                    <input
                      type="text"
                      id="company"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                      placeholder="Your Company"
                    />
                  </div>

                  <div>
                    <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">
                      Project Budget
                    </label>
                    <select
                      id="budget"
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                    >
                      <option value="">Select budget range</option>
                      <option value="5k-15k">$5,000 - $15,000</option>
                      <option value="15k-50k">$15,000 - $50,000</option>
                      <option value="50k-100k">$50,000 - $100,000</option>
                      <option value="100k+">$100,000+</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Project Details
                    </label>
                    <textarea
                      id="message"
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors bg-white"
                      placeholder="Tell us about your project requirements..."
                    />
                  </div>

                  <button
                    type="submit"
                    className="w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                  >
                    Send Message
                  </button>
                </form>
              </motion.div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}