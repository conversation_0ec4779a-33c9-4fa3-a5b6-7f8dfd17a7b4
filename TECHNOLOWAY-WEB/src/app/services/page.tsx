import { <PERSON>ada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ServicesPageClient } from './services-page-client';

// Static data for better maintainability
const PROCESS_STEPS = [
  {
    step: '01',
    title: 'Discovery',
    description: 'We start by understanding your business needs, goals, and challenges through detailed consultation.',
  },
  {
    step: '02',
    title: 'Planning',
    description: 'Our team creates a comprehensive project plan with timelines, milestones, and resource allocation.',
  },
  {
    step: '03',
    title: 'Development',
    description: 'We build your solution using agile methodologies with regular updates and feedback loops.',
  },
  {
    step: '04',
    title: 'Delivery',
    description: 'We deploy your solution and provide ongoing support to ensure optimal performance.',
  },
] as const;

export const metadata: Metadata = {
  title: 'Our Services - Technoloway',
  description: 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.',
  keywords: [
    'software development services',
    'web development',
    'mobile app development',
    'custom software solutions',
    'digital transformation',
    'enterprise software',
    'cloud solutions',
    'API development'
  ],
  openGraph: {
    title: 'Our Services - Technoloway',
    description: 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.',
    url: '/services',
    siteName: 'Technoloway',
    images: [
      {
        url: '/images/og-services.svg',
        width: 1200,
        height: 630,
        alt: 'Technoloway Services - Software Development Solutions',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Services - Technoloway',
    description: 'Comprehensive software development services to help your business thrive in the digital world. From concept to deployment, we\'ve got you covered.',
    images: ['/images/og-services.svg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/services',
  },
};

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <ServicesPageClient processSteps={PROCESS_STEPS} />
      <Footer />
    </div>
  );
}
