'use client';

import { TechnologiesManager } from '@/components/admin/technologies/technologies-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Technology {
  id: number
  name: string
  description: string
  iconUrl?: string
  displayOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  _count?: {
    projectTechnologies: number
  }
}

const technologyConfig: CrudConfig<Technology> = {
  title: 'Technologies',
  description: 'Manage your technology stack, skills, and expertise levels',
  endpoint: 'technologies', // API endpoint

  // Table columns configuration
  columns: [
    {
      key: 'name',
      label: 'Technology',
      sortable: true,
      searchable: true,
      width: '25%'
    },
    {
      key: 'description',
      label: 'Description',
      sortable: false,
      searchable: true,
      width: '35%'
    },
    {
      key: 'displayOrder',
      label: 'Order',
      sortable: true,
      searchable: false,
      width: '10%'
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      searchable: false,
      width: '15%'
    },
    {
      key: 'isActive',
      label: 'Status',
      sortable: true,
      searchable: false,
      width: '10%'
    }
  ],

  // Filters configuration
  filters: [
    {
      key: 'isActive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' }
      ]
    }
  ],

  // Bulk actions configuration
  bulkActions: [
    {
      action: 'activate',
      label: 'Activate',
      icon: 'CheckIcon',
      variant: 'success',
      confirmationMessage: 'Are you sure you want to activate the selected technologies?'
    },
    {
      action: 'deactivate',
      label: 'Deactivate',
      icon: 'XMarkIcon',
      variant: 'warning',
      confirmationMessage: 'Are you sure you want to deactivate the selected technologies?'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      confirmationMessage: 'Are you sure you want to delete the selected technologies? This action cannot be undone.'
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View technology details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit technology'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate technology'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete technology'
    }
  ],

  fields: [
    {
      key: 'name',
      label: 'Technology Name',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., React, Node.js, PostgreSQL'
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Brief description of the technology and its use cases'
    },
    {
      key: 'iconUrl',
      label: 'Icon URL',
      type: 'url',
      searchable: false,
      placeholder: 'Enter icon URL or click Upload to select file'
    },
    {
      key: 'displayOrder',
      label: 'Display Order',
      type: 'number',
      defaultValue: 0,
      searchable: false,
      placeholder: '0'
    },
    {
      key: 'isActive',
      label: 'Active Status',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search technologies by name, description...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['name', 'description', 'displayOrder', 'updatedAt', 'isActive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['name', 'description']
      },
      {
        title: 'Display Settings',
        fields: ['iconUrl', 'displayOrder', 'isActive']
      }
    ]
  }
};

export default function TechnologiesPage() {
  return <TechnologiesManager config={technologyConfig} />;
}
