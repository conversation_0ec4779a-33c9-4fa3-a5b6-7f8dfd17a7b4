'use client';

import { UsersManager } from '@/components/admin/users/users-manager';
import { CrudConfig } from '@/components/admin/crud/types';
import Head from 'next/head'

interface User {
  id: number
  email: string
  firstname?: string
  lastname?: string
  imageurl?: string
  role: 'ADMIN' | 'USER' | 'CLIENT'
  isactive: boolean
  emailverified?: string
  createdat: string
  updatedat: string
  linkedclientid?: number
  linkedclient?: {
    id: number
    companyname: string
    contactname: string
  }
  _count?: {
    clients: number
    auditlogs: number
  }
}

const userConfig: CrudConfig<User> = {
  title: 'Users',
  description: 'Manage system users, roles, and permissions',
  endpoint: 'users', // API endpoint

  columns: [
    {
      key: 'email',
      label: 'Email',
      sortable: true,
      searchable: true,
      renderType: 'email',
      width: '250px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'firstname',
      label: 'First Name',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'lastname',
      label: 'Last Name',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'role',
      label: 'Role',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Admin',
        falseLabel: 'User/Client',
        statusColors: {
          true: 'bg-red-100 text-red-800',
          false: 'bg-blue-100 text-blue-800',
        },
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'linkedclient',
      label: 'Linked Client',
      sortable: false,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'emailverified',
      label: 'Email Verified',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Verified',
        falseLabel: 'Unverified',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-yellow-100 text-yellow-800'
        }
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'createdat',
      label: 'Created',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'updatedat',
      label: 'Last Active',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isactive',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Active',
        falseLabel: 'Inactive',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View user details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit user'
    },
    {
      action: 'toggle-status',
      label: 'Toggle Status',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Activate/Deactivate user'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete user',
      confirmationMessage: 'Are you sure you want to delete this user? This action cannot be undone and will remove all associated data.'
    }
  ],

  fields: [
    {
      key: 'email',
      label: 'Email Address',
      type: 'email',
      required: true,
      searchable: true,
      placeholder: '<EMAIL>'
    },
    {
      key: 'firstname',
      label: 'First Name',
      type: 'text',
      searchable: true,
      placeholder: 'John'
    },
    {
      key: 'lastname',
      label: 'Last Name',
      type: 'text',
      searchable: true,
      placeholder: 'Doe'
    },
    {
      key: 'role',
      label: 'User Role',
      type: 'select',
      required: true,
      options: [
        { value: 'USER', label: 'User' },
        { value: 'ADMIN', label: 'Administrator' },
        { value: 'CLIENT', label: 'Client' },
      ],
      defaultValue: 'USER',
      searchable: false,
    },
    {
      key: 'linkedclientid',
      label: 'Linked Client',
      type: 'select',
      options: [], // Will be populated dynamically
      required: false,
      placeholder: 'Select a client to link (optional)',
      searchable: false,
      // conditionalDisplay removed for linter compliance
    },
    {
      key: 'imageurl',
      label: 'Profile Image',
      type: 'url',
      searchable: false,
      placeholder: 'Enter image URL or click Upload to select file'
    },
    {
      key: 'password',
      label: 'Password',
      type: 'password',
      searchable: false,
      placeholder: 'Leave empty to keep current password'
    },
    {
      key: 'isactive',
      label: 'Active Status',
      type: 'boolean',
      defaultValue: true,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'role',
      label: 'Role',
      type: 'select',
      options: [
        { value: '', label: 'All Roles' },
        { value: 'ADMIN', label: 'Administrator' },
        { value: 'USER', label: 'User' },
        { value: 'CLIENT', label: 'Client' },
      ],
    },
    {
      key: 'isactive',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
    },
    {
      key: 'emailverified',
      label: 'Email Verification',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Verified' },
        { value: 'false', label: 'Unverified' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Activate Selected',
      action: 'activate',
      variant: 'success'
    },
    {
      label: 'Deactivate Selected',
      action: 'deactivate',
      variant: 'warning'
    },
    {
      label: 'Delete Selected',
      action: 'delete',
      variant: 'danger'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search users by email, first name, last name...',
  defaultSort: { field: 'createdat', direction: 'desc' }, // Sort by creation date (most recent first, stable)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['email', 'firstname', 'lastname', 'role', 'emailverified', 'createdat', 'isactive']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Account Information',
        fields: ['email', 'password']
      },
      {
        title: 'Personal Details',
        fields: ['firstname', 'lastname', 'imageurl']
      },
      {
        title: 'Access & Permissions',
        fields: ['role', 'linkedclientid', 'isactive']
      }
    ]
  }
};

// Helper: Role color class
const getRoleColorClass = (role: User['role']) => {
  switch (role) {
    case 'ADMIN': return 'bg-red-100 text-red-800'
    case 'CLIENT': return 'bg-green-100 text-green-800'
    default: return 'bg-blue-100 text-blue-800'
  }
}
// Helper: Status badge class
const getStatusBadgeClass = (isactive: boolean) =>
  isactive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'

export default function UsersPage() {
  return (
    <>
      <Head>
        <title>Admin Users | Technoloway</title>
      </Head>
      <UsersManager config={userConfig} aria-label="Users Manager Table" />
    </>
  )
}
