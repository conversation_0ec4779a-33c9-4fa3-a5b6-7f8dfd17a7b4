import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin,
  generateSlug
} from '@/services/api/api-utils'
import { createBlogPostSchema, updateBlogPostSchema } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/blog - List all blog posts with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['title', 'content', 'excerpt']))
  }
  
  // Add filter for published/draft posts
  if (filter === 'published') {
    where.ispublished = true
  } else if (filter === 'draft') {
    where.ispublished = false
  }

  // Get total count for pagination
  const total = await prisma.blogposts.count({ where })

  // Get blog posts with pagination
  const posts = await prisma.blogposts.findMany({
    where,
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  // Manually fetch author data for each post
  const postsWithAuthors = await Promise.all(
    posts.map(async (post) => {
      let author = null
      if (post.authorid) {
        try {
          author = await prisma.users.findUnique({
            where: { id: BigInt(post.authorid) },
            select: {
              id: true,
              firstname: true,
              lastname: true,
              email: true,
            },
          })
        } catch (error) {
          console.warn('Error fetching author:', error)
        }
      }
      return { ...post, author }
    })
  )

  // Transform the data for frontend
  const transformedPosts = postsWithAuthors.map(post => transformFromDbFields.blogPost(post))

  return paginatedResponse(transformedPosts, page, limit, total)
})

// POST /api/blog - Create a new blog post
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])
  
  const validate = validateRequest(createBlogPostSchema)
  const data = await validate(request)

  // Generate slug if not provided
  if (!data.slug) {
    data.slug = generateSlug(data.title)
  }

  // Check if slug already exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { slug: data.slug },
  })

  if (existingPost) {
    // Generate a unique slug
    let counter = 1
    let newSlug = `${data.slug}-${counter}`
    
    while (await prisma.blogposts.findUnique({ where: { slug: newSlug } })) {
      counter++
      newSlug = `${data.slug}-${counter}`
    }
    
    data.slug = newSlug
  }

  // Check if author exists
  const author = await prisma.users.findUnique({
    where: { id: data.authorid },
  })

  if (!author) {
    throw new Error('Author not found')
  }

  const post = await prisma.blogposts.create({
    data,
  })

  // Use the author data we already fetched
  const postWithAuthor = { ...post, author }

  return successResponse(postWithAuthor, 'Blog post created successfully', 201)
})

// PUT /api/blog - Bulk update blog posts (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  const validate = validateRequest(updateBlogPostSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedPosts = await prisma.blogposts.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedPosts.count },
    `${updatedPosts.count} blog posts updated successfully`
  )
})

// DELETE /api/blog - Bulk delete blog posts (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid blog post IDs provided')
  }

  const deletedPosts = await prisma.blogposts.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedPosts.count },
    `${deletedPosts.count} blog posts deleted successfully`
  )
})
