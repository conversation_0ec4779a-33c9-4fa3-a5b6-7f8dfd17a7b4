import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  ApiError
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteContext {
  params: {
    slug: string
  }
}

// GET /api/services/slug/[slug] - Get a service by slug (public endpoint)
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteContext) => {
  const { slug } = await params

  // Convert slug to service ID (assuming slug is the service ID for now)
  // In a real implementation, you might have a separate slug field
  const service = await prisma.services.findFirst({
    where: { 
      id: slug,
      isactive: true // Only show active services
    },
    include: {
      categories: {
        select: {
          id: true,
          categname: true,
          categdesc: true,
        },
      },
      serviceoptions: {
        where: { isactive: true },
        include: {
          serviceoptionfeatures: {
            orderBy: {
              createdat: 'asc',
            },
          },
        },
        orderBy: {
          optprice: 'asc',
        },
      },

      _count: {
        select: {
          orderdetails: true,
          serviceoptions: true,
        },
      },
    },
  })

  if (!service) {
    throw new ApiError('Service not found', 404, 'NOT_FOUND')
  }

  // Get related services from the same category
  const relatedServices = await prisma.services.findMany({
    where: {
      categoryId: service.categoryId,
      id: { not: service.id },
      isactive: true,
    },
    select: {
      id: true,
      name: true,
      description: true,
      iconclass: true,
      price: true,
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
    take: 3,
    orderBy: {
      displayorder: 'asc',
    },
  })

  // Get testimonials (general testimonials for now)
  let testimonials: any[] = []
  try {
    testimonials = await prisma.testimonials.findMany({
      where: {
        isactive: true,
        isfeatured: true,
      },
      select: {
        id: true,
        clientname: true,
        clienttitle: true,
        clientcompany: true,
        content: true,
        rating: true,
      },
      take: 2,
      orderBy: {
        createdat: 'desc',
      },
    })
  } catch (error) {
    console.warn('Testimonials table might not exist:', error)
    testimonials = []
  }

  return successResponse({
    service,
    relatedServices,
    testimonials,
  })
})
