import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/services - List all active services (public endpoint)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const categoryId = searchParams.get('categoryId') || ''

    const skip = (page - 1) * limit

    // Build where clause for active services only
    const where: any = { isactive: true }

    // Add category filter
    if (categoryId) {
      where.categid = BigInt(categoryId)
    }

    // Add search functionality
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }

    const [services, total] = await Promise.all([
      prisma.services.findMany({
        where,
        skip,
        take: limit,
        orderBy: { displayorder: 'asc' },
        include: {
          categories: {
            select: {
              id: true,
              categname: true,
              categdesc: true,
            },
          },
          serviceoptions: {
            where: { isactive: true },
            include: {
              serviceoptionfeatures: true,
            },
            orderBy: { optprice: 'asc' },
          },
          _count: {
            select: {
              serviceoptions: true,
            },
          },
        },
      }),
      prisma.services.count({ where }),
    ])

    // Transform the data for frontend
    const transformedServices = services.map(service => ({
      id: service.id.toString(),
      name: service.name,
      description: service.description,
      iconClass: service.iconclass,
      price: Number(service.price),
      discountRate: service.discountrate ? Number(service.discountrate) : undefined,
      totalDiscount: service.totaldiscount ? Number(service.totaldiscount) : undefined,
      manager: service.manager,
      isActive: service.isactive,
      displayOrder: service.displayorder,
      createdAt: service.createdat?.toISOString(),
      updatedAt: service.updatedat?.toISOString(),
      category: service.categories ? {
        id: service.categories.id.toString(),
        name: service.categories.categname,
        description: service.categories.categdesc,
      } : null,
      serviceOptions: service.serviceoptions?.map(option => ({
        id: option.id.toString(),
        name: option.optname,
        description: option.optdesc,
        price: option.optprice ? Number(option.optprice) : undefined,
        isActive: option.isactive,
        features: option.serviceoptionfeatures?.map(feature => ({
          id: feature.id.toString(),
          name: feature.featname,
          description: feature.featdesc,
          isIncluded: feature.isincluded,
        })) || []
      })) || [],
      _count: {
        serviceOptions: service._count.serviceoptions,
      }
    }))

    return NextResponse.json({
      success: true,
      data: transformedServices,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching services:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch services',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}


