import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/technologies/[id] - Get a specific technology
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { id } = await params

  const technology = await prisma.technologies.findUnique({
    where: { id },
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
          projstartdate: true,
          projcompletiondate: true,
          client: {
            select: {
              id: true,
              companyname: true,
            },
          },
        },
        orderBy: {
          createdat: 'desc',
        },
      },
      _count: {
        select: {
          projects: true,
        },
      },
    },
  })

  if (!technology) {
    throw new ApiError('Technology not found', 404)
  }

  return successResponse(technology)
})

// PUT /api/technologies/[id] - Update a technology
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const validate = validateRequest(schemas.technology.update)
  const data = await validate(request)

  // Check if technology exists
  const existingTechnology = await prisma.technologies.findUnique({
    where: { id },
  })

  if (!existingTechnology) {
    throw new ApiError('Technology not found', 404)
  }

  // Check if name is being changed and if it conflicts with another technology
  if (data.name && data.name !== existingTechnology.name) {
    const nameConflict = await prisma.technologies.findFirst({
      where: {
        name: data.name,
        id: { not: id }
      },
    })

    if (nameConflict) {
      throw new ApiError('A technology with this name already exists', 400)
    }
  }

  const technology = await prisma.technologies.update({
    where: { id },
    data,
    include: {
      _count: {
        select: {
          projects: true,
        },
      },
    },
  })

  return successResponse(technology, 'Technology updated successfully')
})

// DELETE /api/technologies/[id] - Delete a technology
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params

  // Check if technology exists
  const existingTechnology = await prisma.technologies.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          projects: true,
        },
      },
    },
  })

  if (!existingTechnology) {
    throw new ApiError('Technology not found', 404)
  }

  // Check if technology is being used in projects
  if (existingTechnology._count.projects > 0) {
    throw new ApiError('Cannot delete technology that is in use by projects. Please remove it from projects first.', 400)
  }

  await prisma.technologies.delete({
    where: { id },
  })

  return successResponse(null, 'Technology deleted successfully')
})

// PATCH /api/technologies/[id] - Partial update (e.g., toggle status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const body = await request.json()

  // Check if technology exists
  const existingTechnology = await prisma.technologies.findUnique({
    where: { id },
  })

  if (!existingTechnology) {
    throw new ApiError('Technology not found', 404)
  }

  // Only allow specific fields for PATCH
  const allowedFields = ['isactive', 'displayorder']
  const updateData: any = {}

  for (const field of allowedFields) {
    if (body[field] !== undefined) {
      if (field === 'isactive') {
        updateData[field] = body[field] === true || body[field] === 'true'
      } else if (field === 'displayorder') {
        updateData[field] = parseInt(body[field])
      } else {
        updateData[field] = body[field]
      }
    }
  }

  if (Object.keys(updateData).length === 0) {
    throw new ApiError('No valid fields to update', 400)
  }

  const technology = await prisma.technologies.update({
    where: { id },
    data: updateData,
    include: {
      _count: {
        select: {
          projects: true,
        },
      },
    },
  })

  return successResponse(technology, 'Technology updated successfully')
})
