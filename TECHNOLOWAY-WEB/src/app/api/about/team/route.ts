import { prisma } from '@/config/prisma';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    console.log('About team API: Starting to fetch team members...');

    const teamMembers = await prisma.teammembers.findMany({
      where: {
        isactive: true,
      },
      select: {
        id: true,
        name: true,
        position: true,
        bio: true,
        photourl: true,
        email: true,
        linkedinurl: true,
        twitterurl: true,
        githuburl: true,
        displayorder: true,
      },
      orderBy: {
        displayorder: 'asc',
      },
    });

    // Convert BigInt to string to fix JSON serialization
    const serializedTeamMembers = teamMembers.map(member => ({
      ...member,
      id: member.id.toString(),
    }));

    console.log('About team API: Successfully fetched', serializedTeamMembers.length, 'team members');
    return NextResponse.json(serializedTeamMembers);
  } catch (error) {
    console.error('About team API: Error details:', error);
    console.error('About team API: Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { error: 'Failed to fetch team members', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
