import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { prisma } from '@/config/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const invoiceId = parseInt(id)

    if (isNaN(invoiceId)) {
      return NextResponse.json(
        { success: false, message: 'Invalid invoice ID' },
        { status: 400 }
      )
    }

    // Get invoice details
    const invoice = await prisma.invoices.findUnique({
      where: { id: invoiceId },
      include: {
        clients: true,
        projects: true,
        invoiceitems: true
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { success: false, message: 'Invoice not found' },
        { status: 404 }
      )
    }

    // For now, return a simple PDF response
    // In a real implementation, you would generate a proper PDF using a library like puppeteer or jsPDF
    const pdfContent = generateInvoicePDF(invoice)
    
    return new NextResponse(pdfContent, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${invoiceId}.pdf"`
      }
    })

  } catch (error) {
    console.error('Error generating invoice PDF:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to generate PDF' },
      { status: 500 }
    )
  }
}

function generateInvoicePDF(invoice: any): Buffer {
  // This is a placeholder implementation
  // In a real application, you would use a PDF generation library
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 12 Tf
50 750 Td
(Invoice #${invoice.id}) Tj
0 -20 Td
(Amount: $${invoice.amount}) Tj
0 -20 Td
(Due Date: ${invoice.duedate}) Tj
0 -20 Td
(Client: ${invoice.clients?.companyname || invoice.clients?.contactname}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000526 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
623
%%EOF`

  return Buffer.from(pdfContent)
}
