import { NextRequest } from 'next/server'
import { getClientContracts, getContractStatusOptions } from '@/lib/fetchers/contract'
import { clientExists } from '@/lib/fetchers/client'
import {
  withErrorHandler,
  successResponse,
  paginatedResponse,
  ApiError,
  getQueryParams,
  getPaginationParams
} from '@/services/api/api-utils'

interface RouteParams {
  params: Promise<{ clientId: string }>
}

// GET /api/clients/[clientId]/contracts - Get contracts for a specific client
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const { page, limit, search, filter } = getQueryParams(request)
    const { skip, take } = getPaginationParams(page, limit)

    // Extract status filter
    const status = filter ? JSON.parse(filter).status : undefined

    const result = await getClientContracts(clientId, {
      page,
      limit: take,
      search,
      status,
    })

    return paginatedResponse(result.contracts, page, take, result.total)
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('Error fetching client contracts:', error)
    throw new ApiError('Failed to fetch client contracts', 500)
  }
})

// OPTIONS /api/clients/[clientId]/contracts - Get available options for contracts
export const OPTIONS = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if client exists
  const exists = await clientExists(clientId)
  if (!exists) {
    throw new ApiError('Client not found', 404)
  }

  try {
    const options = {
      statusOptions: getContractStatusOptions(),
      sortOptions: [
        { value: 'contName', label: 'Contract Name' },
        { value: 'contStatus', label: 'Status' },
        { value: 'contValue', label: 'Contract Value' },
        { value: 'contSignedDate', label: 'Signed Date' },
        { value: 'contExpiryDate', label: 'Expiry Date' },
        { value: 'createdAt', label: 'Created Date' },
      ],
      billingTypeOptions: [
        { value: 'HOURLY', label: 'Hourly' },
        { value: 'FIXED', label: 'Fixed Price' },
        { value: 'MONTHLY', label: 'Monthly' },
        { value: 'MILESTONE', label: 'Milestone-based' },
      ],
    }

    return successResponse(options)
  } catch (error) {
    console.error('Error fetching contract options:', error)
    throw new ApiError('Failed to fetch contract options', 500)
  }
})
