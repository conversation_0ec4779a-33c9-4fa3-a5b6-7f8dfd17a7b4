import { NextRequest, NextResponse } from 'next/server'
import { getClientDetails, getClient, clientExists } from '@/lib/fetchers/client'
import {
  withErrorHandler,
  successResponse,
  ApiError
} from '@/services/api/api-utils'

interface RouteParams {
  params: Promise<{ clientId: string }>
}

// GET /api/clients/[clientId] - Get a specific client with detailed information
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    throw new ApiError('Invalid client ID', 400)
  }

  // Check if we want detailed information or basic info
  const url = new URL(request.url)
  const detailed = url.searchParams.get('detailed') === 'true'

  try {
    if (detailed) {
      const client = await getClientDetails(clientId)
      if (!client) {
        throw new ApiError('Client not found', 404)
      }
      return successResponse(client)
    } else {
      const client = await getClient(clientId)
      if (!client) {
        throw new ApiError('Client not found', 404)
      }
      return successResponse(client)
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('Error fetching client:', error)
    throw new ApiError('Failed to fetch client', 500)
  }
})

// HEAD /api/clients/[clientId] - Check if client exists
export const HEAD = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const { clientId } = await params

  if (!clientId || isNaN(Number(clientId))) {
    return new NextResponse(null, { status: 400 })
  }

  try {
    const exists = await clientExists(clientId)
    return new NextResponse(null, { status: exists ? 200 : 404 })
  } catch (error) {
    console.error('Error checking client existence:', error)
    return new NextResponse(null, { status: 500 })
  }
})
