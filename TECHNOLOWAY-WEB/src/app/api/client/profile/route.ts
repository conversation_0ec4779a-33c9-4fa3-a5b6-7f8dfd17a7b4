import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/services/auth/auth-config'
import { prisma } from '@/config/prisma'
import { z } from 'zod'

// Profile update schema - matching actual database fields
const profileUpdateSchema = z.object({
  companyname: z.string().min(1, 'Company name is required'),
  contactname: z.string().min(1, 'Contact name is required'),
  contactemail: z.string().email('Please enter a valid email address'),
  contactphone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipcode: z.string().optional(),
  country: z.string().optional(),
  companywebsite: z.string().optional(), // Note: database field is companywebsite, not website
  notes: z.string().optional()
  // Note: industry and companysize fields don't exist in the database schema
})

// PUT /api/client/profile - Update client profile for authenticated user
export const PUT = async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return Response.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      )
    }

    if (session.user.role !== 'CLIENT') {
      return Response.json(
        { success: false, message: 'Access denied. Client role required.' },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validationResult = profileUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      return Response.json(
        { 
          success: false, 
          message: 'Validation failed',
          errors: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    const profileData = validationResult.data

    // Find the client record linked to this user
    const existingClient = await prisma.clients.findFirst({
      where: {
        userid: BigInt(session.user.id)
      }
    })

    if (!existingClient) {
      return Response.json(
        {
          success: false,
          message: 'No client account linked to your user account. Please contact support.'
        },
        { status: 404 }
      )
    }

    // Update the client profile
    const updatedClient = await prisma.clients.update({
      where: {
        id: existingClient.id
      },
      data: {
        companyname: profileData.companyname,
        contactname: profileData.contactname,
        contactemail: profileData.contactemail,
        contactphone: profileData.contactphone || null,
        address: profileData.address || null,
        city: profileData.city || null,
        state: profileData.state || null,
        zipcode: profileData.zipcode || null,
        country: profileData.country || null,
        companywebsite: profileData.companywebsite || null, // Use correct database field name
        notes: profileData.notes || null,
        updatedat: new Date()
      }
    })

    // Convert BigInt to number for JSON serialization
    const serializedClient = {
      ...updatedClient,
      id: Number(updatedClient.id),
      userid: updatedClient.userid ? Number(updatedClient.userid) : null
    }

    return Response.json({
      success: true,
      message: 'Profile updated successfully',
      data: serializedClient
    })

  } catch (error) {
    console.error('Client profile update API error:', error)
    return Response.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
