import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withError<PERSON>and<PERSON>,
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/categories - Get all active categories (public endpoint)
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause for active categories only
  const where: any = { isactive: true }
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, ['categname', 'categdesc']))
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy) {
    orderBy[sortBy] = sortOrder || 'asc'
  } else {
    orderBy.displayorder = 'asc'
  }

  const [categories, total] = await Promise.all([
    prisma.categories.findMany({
      where,
      skip,
      take,
      orderBy,
      select: {
        id: true,
        categname: true,
        categdesc: true,
        displayorder: true,
        isactive: true,
        _count: {
          select: {
            services: true,
          },
        },
      },
    }),
    prisma.categories.count({ where }),
  ])

  return paginatedResponse(categories, page, take, total, 'Categories retrieved successfully')
})
