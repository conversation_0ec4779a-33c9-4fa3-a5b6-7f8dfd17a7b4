import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  withE<PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse, 
  validateRequest,
  requireAdmin,
  ApiError,
  generateSlug
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

// GET /api/admin/blog/[id] - Get a specific blog post
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  const blogPost = await prisma.blogposts.findUnique({
    where: { id: BigInt(id) },
  })

  if (!blogPost) {
    throw new ApiError('Blog post not found', 404)
  }

  const transformedBlogPost = transformFromDbFields.blogPost(blogPost)
  return successResponse({
    ...transformedBlogPost,
    id: String(transformedBlogPost.id)
  })
})

// PUT /api/admin/blog/[id] - Update a specific blog post
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.blog.update)
  const data = await validate(request)

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404)
  }

  // If title is being updated and no slug provided, generate new slug
  if (data.title && !data.slug) {
    data.slug = generateSlug(data.title)
  }

  // If slug is being updated, check for conflicts
  if (data.slug && data.slug !== existingPost.slug) {
    const slugConflict = await prisma.blogposts.findFirst({
      where: {
        slug: data.slug,
        id: { not: id },
      },
    })

    if (slugConflict) {
      // Generate a unique slug
      let counter = 1
      let newSlug = `${data.slug}-${counter}`
      
      while (await prisma.blogposts.findFirst({ 
        where: { 
          slug: newSlug,
          id: { not: id }
        } 
      })) {
        counter++
        newSlug = `${data.slug}-${counter}`
      }
      
      data.slug = newSlug
    }
  }

  // Transform data to database format
  const dbData = transformToDbFields.blogPost(data)

  const updatedBlogPost = await prisma.blogposts.update({
    where: { id: BigInt(id) },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
  })

  const transformedBlogPost = transformFromDbFields.blogPost(updatedBlogPost)
  return successResponse({
    ...transformedBlogPost,
    id: String(transformedBlogPost.id)
  }, 'Blog post updated successfully')
})

// PATCH /api/admin/blog/[id] - Partial update (e.g., toggle published status)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const body = await request.json()

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404)
  }

  // Transform data to database format
  const dbData = transformToDbFields.blogPost(body)

  // Handle publishing logic
  if ('ispublished' in dbData && dbData.ispublished === true && !existingPost.publishedat) {
    dbData.publishedat = new Date()
  }

  const updatedBlogPost = await prisma.blogposts.update({
    where: { id: BigInt(id) },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
  })

  const transformedBlogPost = transformFromDbFields.blogPost(updatedBlogPost)
  return successResponse({
    ...transformedBlogPost,
    id: String(transformedBlogPost.id)
  }, 'Blog post updated successfully')
})

// DELETE /api/admin/blog/[id] - Delete a specific blog post
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if blog post exists
  const existingPost = await prisma.blogposts.findUnique({
    where: { id: BigInt(id) },
  })

  if (!existingPost) {
    throw new ApiError('Blog post not found', 404)
  }

  // Delete the blog post
  await prisma.blogposts.delete({
    where: { id: BigInt(id) },
  })

  return successResponse(null, 'Blog post deleted successfully')
})
