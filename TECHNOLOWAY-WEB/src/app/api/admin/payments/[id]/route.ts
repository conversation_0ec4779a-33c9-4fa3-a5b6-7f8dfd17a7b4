import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse, 
  ApiError,
  requireAdmin
} from '@/services/api/api-utils'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/payments/[id] - Get a specific payment
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const paymentId = parseInt(id)

  if (isNaN(paymentId)) {
    throw new ApiError('Invalid payment ID', 400)
  }

  const payment = await prisma.payments.findUnique({
    where: { id: paymentId },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true,
          duedate: true,
          description: true,
          projects: {
            select: {
              id: true,
              name: true
            }
          },
          clients: {
            select: {
              id: true,
              companyname: true
            }
          }
        }
      }
    }
  })

  if (!payment) {
    throw new ApiError('Payment not found', 404)
  }

  // Transform the response
  const transformedPayment = {
    id: Number(payment.id),
    amount: Number(payment.amount),
    paymentDate: payment.paymentdate.toISOString(),
    paymentMethod: payment.paymentmethod,
    status: payment.status,
    notes: payment.notes,
    reference: payment.reference,
    transactionId: payment.transactionid,
    processingFee: payment.processingfee ? Number(payment.processingfee) : null,
    invoiceId: Number(payment.invoiceid),
    createdAt: payment.createdat.toISOString(),
    updatedAt: payment.updatedat?.toISOString(),
    currency: payment.currency,
    promoCode: payment.promocode,
    discount: payment.discount ? Number(payment.discount) : 0,
    emailReceipt: payment.emailreceipt,
    receiptEmail: payment.receiptemail,
    termsAccepted: payment.termsaccepted,
    paymentDetails: payment.paymentdetails,
    stripePaymentIntentId: payment.stripepaymentintentid,
    stripeClientSecret: payment.stripeclientsecret,
    invoice: payment.invoices ? {
      id: Number(payment.invoices.id),
      totalAmount: Number(payment.invoices.totalamount),
      status: payment.invoices.status,
      dueDate: payment.invoices.duedate.toISOString(),
      description: payment.invoices.description,
      project: payment.invoices.projects ? {
        id: Number(payment.invoices.projects.id),
        name: payment.invoices.projects.name
      } : null,
      client: payment.invoices.clients ? {
        id: Number(payment.invoices.clients.id),
        companyname: payment.invoices.clients.companyname
      } : null
    } : null
  }

  return successResponse(transformedPayment)
})

// PUT /api/admin/payments/[id] - Update a payment
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const paymentId = parseInt(id)

  if (isNaN(paymentId)) {
    throw new ApiError('Invalid payment ID', 400)
  }

  const body = await request.json()
  const {
    amount,
    paymentMethod,
    paymentDate,
    notes,
    status,
    reference,
    transactionId,
    processingFee,
    currency,
    promoCode,
    discount,
    emailReceipt,
    receiptEmail,
    termsAccepted,
    paymentDetails
  } = body

  // Check if payment exists
  const existingPayment = await prisma.payments.findUnique({
    where: { id: paymentId },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true
        }
      }
    }
  })

  if (!existingPayment) {
    throw new ApiError('Payment not found', 404)
  }

  // Validate required fields
  if (amount !== undefined && amount <= 0) {
    throw new ApiError('Valid payment amount is required', 400)
  }

  // Update the payment
  const updatedPayment = await prisma.payments.update({
    where: { id: paymentId },
    data: {
      ...(amount !== undefined && { amount: amount }),
      ...(paymentMethod && { paymentmethod: paymentMethod }),
      ...(paymentDate && { paymentdate: new Date(paymentDate) }),
      ...(notes !== undefined && { notes: notes }),
      ...(status && { status: status }),
      ...(reference !== undefined && { reference: reference }),
      ...(transactionId !== undefined && { transactionid: transactionId }),
      ...(processingFee !== undefined && { processingfee: processingFee }),
      ...(currency && { currency: currency }),
      ...(promoCode !== undefined && { promocode: promoCode }),
      ...(discount !== undefined && { discount: discount }),
      ...(emailReceipt !== undefined && { emailreceipt: emailReceipt }),
      ...(receiptEmail !== undefined && { receiptemail: receiptEmail }),
      ...(termsAccepted !== undefined && { termsaccepted: termsAccepted }),
      ...(paymentDetails !== undefined && { paymentdetails: paymentDetails }),
    },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true,
          duedate: true,
          description: true,
          projects: {
            select: {
              id: true,
              name: true
            }
          },
          clients: {
            select: {
              id: true,
              companyname: true
            }
          }
        }
      }
    }
  })

  // Transform the response
  const transformedPayment = {
    id: Number(updatedPayment.id),
    amount: Number(updatedPayment.amount),
    paymentDate: updatedPayment.paymentdate.toISOString(),
    paymentMethod: updatedPayment.paymentmethod,
    status: updatedPayment.status,
    notes: updatedPayment.notes,
    reference: updatedPayment.reference,
    transactionId: updatedPayment.transactionid,
    processingFee: updatedPayment.processingfee ? Number(updatedPayment.processingfee) : null,
    invoiceId: Number(updatedPayment.invoiceid),
    createdAt: updatedPayment.createdat.toISOString(),
    updatedAt: updatedPayment.updatedat?.toISOString(),
    currency: updatedPayment.currency,
    promoCode: updatedPayment.promocode,
    discount: updatedPayment.discount ? Number(updatedPayment.discount) : 0,
    emailReceipt: updatedPayment.emailreceipt,
    receiptEmail: updatedPayment.receiptemail,
    termsAccepted: updatedPayment.termsaccepted,
    paymentDetails: updatedPayment.paymentdetails,
    stripePaymentIntentId: updatedPayment.stripepaymentintentid,
    stripeClientSecret: updatedPayment.stripeclientsecret,
    invoice: updatedPayment.invoices ? {
      id: Number(updatedPayment.invoices.id),
      totalAmount: Number(updatedPayment.invoices.totalamount),
      status: updatedPayment.invoices.status,
      dueDate: updatedPayment.invoices.duedate.toISOString(),
      description: updatedPayment.invoices.description,
      project: updatedPayment.invoices.projects ? {
        id: Number(updatedPayment.invoices.projects.id),
        name: updatedPayment.invoices.projects.name
      } : null,
      client: updatedPayment.invoices.clients ? {
        id: Number(updatedPayment.invoices.clients.id),
        companyname: updatedPayment.invoices.clients.companyname
      } : null
    } : null
  }

  return successResponse(transformedPayment, 'Payment updated successfully')
})

// DELETE /api/admin/payments/[id] - Delete a payment
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  
  const { id } = await params
  const paymentId = parseInt(id)

  if (isNaN(paymentId)) {
    throw new ApiError('Invalid payment ID', 400)
  }

  // Check if payment exists
  const existingPayment = await prisma.payments.findUnique({
    where: { id: paymentId },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true
        }
      }
    }
  })

  if (!existingPayment) {
    throw new ApiError('Payment not found', 404)
  }

  // Get the invoice to update its status after payment deletion
  const invoice = existingPayment.invoices
  
  // Delete the payment
  await prisma.payments.delete({
    where: { id: paymentId }
  })

  // Recalculate invoice status after payment deletion
  if (invoice) {
    const remainingPayments = await prisma.payments.findMany({
      where: { invoiceid: invoice.id },
      select: { amount: true }
    })

    const totalPaid = remainingPayments.reduce((sum, payment) => sum + Number(payment.amount), 0)
    const invoiceTotal = Number(invoice.totalamount)

    let newStatus = 'pending'
    if (totalPaid === 0) {
      newStatus = 'pending'
    } else if (totalPaid >= invoiceTotal) {
      newStatus = 'paid'
    } else {
      newStatus = 'partial'
    }

    // Update invoice status
    await prisma.invoices.update({
      where: { id: invoice.id },
      data: {
        status: newStatus,
        ...(newStatus !== 'paid' && { paidat: null })
      }
    })
  }

  return successResponse(null, 'Payment deleted successfully')
})
