import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withE<PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/categories/[id] - Get a specific category
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const category = await prisma.categories.findUnique({
    where: { id: BigInt(id) },
    include: {
      services: {
        select: {
          id: true,
          name: true,
          isactive: true,
          price: true
        }
      },
      _count: {
        select: {
          services: true
        }
      }
    }
  })

  if (!category) {
    throw new ApiError('Category not found', 404)
  }

  // Transform the response to match frontend expectations
  const transformedCategory = {
    id: String(category.id),
    categname: category.categname,
    categdesc: category.categdesc,
    parentid: category.parentid,
    isactive: category.isactive,
    displayorder: category.displayorder,
    createdat: category.createdat,
    updatedat: category.updatedat,
    services: category.services,
    _count: category._count
  }

  return successResponse(transformedCategory)
})

// PUT /api/admin/categories/[id] - Update a category
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const body = await request.json()
  const { id } = await params

  // Prevent setting parent to self or creating circular references
  if (body.parentid && Number(body.parentid) === Number(id)) {
    throw new ApiError('Category cannot be its own parent', 400)
  }

  // Transform frontend data to database field names
  const updateData: any = {
    updatedat: new Date()
  }

  if (body.categname !== undefined) updateData.categname = body.categname
  if (body.categdesc !== undefined) updateData.categdesc = body.categdesc
  if (body.parentid !== undefined) updateData.parentid = body.parentid ? Number(body.parentid) : 0
  if (body.isactive !== undefined) updateData.isactive = body.isactive
  if (body.displayorder !== undefined) updateData.displayorder = body.displayorder

  const category = await prisma.categories.update({
    where: { id: BigInt(id) },
    data: updateData,
    include: {
      services: {
        select: {
          id: true,
          name: true,
          isactive: true,
          price: true
        }
      },
      _count: {
        select: {
          services: true
        }
      }
    }
  })

  // Transform the response to match frontend expectations
  const transformedCategory = {
    id: String(category.id),
    categname: category.categname,
    categdesc: category.categdesc,
    parentid: category.parentid,
    isactive: category.isactive,
    displayorder: category.displayorder,
    createdat: category.createdat,
    updatedat: category.updatedat,
    services: category.services,
    _count: category._count
  }

  return successResponse(transformedCategory, 'Category updated successfully')
})

// DELETE /api/admin/categories/[id] - Delete a category
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if category has services using foreign key relationship
  const servicesCount = await prisma.services.count({
    where: {
      categid: BigInt(id),
      isactive: true
    }
  })

  if (servicesCount > 0) {
    throw new ApiError(
      `Cannot delete this category because it has ${servicesCount} active service(s) associated with it. Please move or delete the services first.`,
      400,
      'CATEGORY_HAS_SERVICES'
    )
  }

  // Check if category has children
  const childrenCount = await prisma.categories.count({
    where: { parentid: Number(id) }
  })

  if (childrenCount > 0) {
    throw new ApiError('Cannot delete category with child categories', 400)
  }

  await prisma.categories.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Category deleted successfully')
})
