import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// POST /api/admin/services/bulk-action - Perform bulk actions on services
export const POST = withError<PERSON>andler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { action, ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ApiError('Invalid or empty IDs array', 400)
  }

  if (!action) {
    throw new ApiError('Action is required', 400)
  }

  let result
  let message = ''

  switch (action) {
    case 'activate':
      result = await prisma.services.updateMany({
        where: { id: { in: ids } },
        data: { isactive: true },
      })
      message = `${result.count} service(s) activated successfully`
      break

    case 'deactivate':
      result = await prisma.services.updateMany({
        where: { id: { in: ids } },
        data: { isactive: false },
      })
      message = `${result.count} service(s) deactivated successfully`
      break

    case 'delete':
      // Check if any services are being used
      const servicesInUse = await prisma.services.findMany({
        where: {
          id: { in: ids },
          OR: [
            { projects: { some: {} } },
            { orderDetails: { some: {} } },
          ],
        },
        select: { id: true, name: true },
      })

      if (servicesInUse.length > 0) {
        throw new ApiError(
          `Cannot delete services that are in use: ${servicesInUse.map(s => s.name).join(', ')}`,
          400
        )
      }

      // Delete service options first
      await prisma.servicesOption.deleteMany({
        where: { serviceId: { in: ids } },
      })

      // Delete services
      result = await prisma.services.deleteMany({
        where: { id: { in: ids } },
      })
      message = `${result.count} service(s) deleted successfully`
      break

    case 'update-order':
      // Bulk update display order
      const { orderUpdates } = await request.json()
      if (!Array.isArray(orderUpdates)) {
        throw new ApiError('Order updates must be an array', 400)
      }

      const updatePromises = orderUpdates.map(({ id, displayOrder }) =>
        prisma.services.update({
          where: { id },
          data: { displayOrder },
        })
      )

      await Promise.all(updatePromises)
      result = { count: orderUpdates.length }
      message = `Display order updated for ${orderUpdates.length} service(s)`
      break

    default:
      throw new ApiError(`Unknown action: ${action}`, 400)
  }

  return successResponse(result, message)
})
