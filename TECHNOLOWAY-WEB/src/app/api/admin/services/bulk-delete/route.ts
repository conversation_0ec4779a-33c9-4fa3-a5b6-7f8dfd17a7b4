import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// POST /api/admin/services/bulk-delete - Bulk delete services
export const POST = withE<PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { ids } = await request.json()

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new ApiError('Invalid or empty IDs array', 400)
  }

  // Check if any services are being used in projects or orders
  const servicesInUse = await prisma.services.findMany({
    where: {
      id: { in: ids },
      OR: [
        { projects: { some: {} } },
        { orderDetails: { some: {} } },
      ],
    },
    select: { id: true, name: true },
  })

  if (servicesInUse.length > 0) {
    throw new ApiError(
      `Cannot delete services that are in use: ${servicesInUse.map(s => s.name).join(', ')}`,
      400
    )
  }

  // Delete service options first (cascade should handle this, but being explicit)
  await prisma.servicesOption.deleteMany({
    where: { serviceId: { in: ids } },
  })

  // Delete services
  const result = await prisma.services.deleteMany({
    where: { id: { in: ids } },
  })

  return successResponse(
    { deletedCount: result.count },
    `${result.count} service(s) deleted successfully`
  )
})
