import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withE<PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/clients/dropdown - Get clients for dropdown selection
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const clients = await prisma.clientss.findMany({
    select: {
      id: true,
      companyname: true,
      contactname: true,
      contactemail: true,
    },
    orderBy: {
      companyname: 'asc',
    },
  })

  // Format for dropdown
  const formattedClients = clients.map(client => ({
    value: client.id,
    label: `${client.companyname} - ${client.contactname}`,
    companyname: client.companyname,
    contactname: client.contactname,
    contactemail: client.contactemail,
  }))

  return successResponse(formattedClients)
})
