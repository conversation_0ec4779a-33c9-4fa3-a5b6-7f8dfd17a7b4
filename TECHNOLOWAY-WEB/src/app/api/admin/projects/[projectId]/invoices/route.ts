import { prisma } from '@/config/prisma'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { projectId } = await params
    const { searchParams } = new URL(request.url)

    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') || ''

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      projectid: parseInt(projectId)
    }

    if (search) {
      where.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { status: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status) {
      where.status = status
    }

    // Get invoices with related data
    const [invoices, total] = await Promise.all([
      prisma.invoices.findMany({
        where,
        include: {
          _count: {
            select: {
              payments: true
            }
          },
          projects: {
            select: {
              id: true,
              name: true
            }
          },
          clients: {
            select: {
              id: true,
              companyname: true
            }
          }
        },
        orderBy: { createdat: 'desc' },
        skip,
        take: limit
      }),
      prisma.invoices.count({ where })
    ])

    // Transform the data to match expected format
    const transformedInvoices = invoices.map(invoice => ({
      id: Number(invoice.id),
      clientId: Number(invoice.clientid),
      projectId: Number(invoice.projectid),
      totalAmount: Number(invoice.totalamount),
      subtotal: Number(invoice.subtotal),
      taxRate: Number(invoice.taxrate),
      taxAmount: Number(invoice.taxamount),
      status: invoice.status,
      dueDate: invoice.duedate.toISOString(),
      description: invoice.description,
      paidAt: invoice.paidat?.toISOString(),
      createdAt: invoice.createdat.toISOString(),
      updatedAt: invoice.updatedat?.toISOString(),
      _count: invoice._count ? {
        payments: Number(invoice._count.payments)
      } : { payments: 0 },
      project: invoice.projects ? {
        id: Number(invoice.projects.id),
        name: invoice.projects.name
      } : null,
      client: invoice.clients ? {
        id: Number(invoice.clients.id),
        companyname: invoice.clients.companyname
      } : null
    }))

    return NextResponse.json({
      success: true,
      data: transformedInvoices,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching project invoices:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch invoices',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { projectId: projectIdStr } = await params
    const projectId = parseInt(projectIdStr)
    const body = await request.json()

    // Validate required fields for invoice creation
    const requiredFields = ['totalAmount', 'taxRate', 'taxAmount', 'status', 'dueDate']
    for (const field of requiredFields) {
      if (!(field in body)) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Get project to get clientId and orderid
    const project = await prisma.projects.findUnique({
      where: { id: projectId },
      select: {
        clientid: true,
        orderid: true
      }
    })

    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      )
    }

    // Get the first contract for this project and order
    const contract = await prisma.contracts.findFirst({
      where: {
        projid: projectId,
        clientid: project.clientid,
        orderid: project.orderid
      },
      select: { id: true }
    })

    let contractId = contract?.id

    // If no contract exists, create a default one
    if (!contract) {
      try {
        const newContract = await prisma.contracts.create({
          data: {
            contname: `Default Contract for Project ${projectId}`,
            projid: projectId,
            clientid: project.clientid,
            orderid: project.orderid,
            contstatus: 'ACTIVE',
            contvalue: body.totalAmount,
            contvaluecurr: 'USD',
            billingtype: 'One-time'
          }
        })
        contractId = newContract.id
      } catch (error) {
        return NextResponse.json(
          { success: false, error: 'Failed to create default contract. Please ensure the project has a valid order.' },
          { status: 400 }
        )
      }
    }

    // Create the invoice
    const invoice = await prisma.invoices.create({
      data: {
        clientid: project.clientid,
        projectid: projectId,
        totalamount: body.totalAmount,
        subtotal: body.subtotal,
        taxrate: body.taxRate,
        taxamount: body.taxAmount,
        status: body.status,
        duedate: new Date(body.dueDate),
        description: body.description,
        paidat: body.paidAt ? new Date(body.paidAt) : null,
        contid: contractId,
        orderid: project.orderid
      },
      include: {
        _count: {
          select: {
            payments: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true
          }
        },
        clients: {
          select: {
            id: true,
            companyname: true
          }
        }
      }
    })

    // Transform the response
    const transformedInvoice = {
      id: Number(invoice.id),
      clientId: Number(invoice.clientid),
      projectId: Number(invoice.projectid),
      totalAmount: Number(invoice.totalamount),
      subtotal: Number(invoice.subtotal),
      taxRate: Number(invoice.taxrate),
      taxAmount: Number(invoice.taxamount),
      status: invoice.status,
      dueDate: invoice.duedate.toISOString(),
      description: invoice.description,
      paidAt: invoice.paidat?.toISOString(),
      createdAt: invoice.createdat.toISOString(),
      updatedAt: invoice.updatedat?.toISOString(),
      _count: invoice._count ? {
        payments: Number(invoice._count.payments)
      } : { payments: 0 },
      project: invoice.projects ? {
        id: Number(invoice.projects.id),
        name: invoice.projects.name
      } : null,
      client: invoice.clients ? {
        id: Number(invoice.clients.id),
        companyname: invoice.clients.companyname
      } : null
    }

    return NextResponse.json({
      success: true,
      data: transformedInvoice
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating invoice:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create invoice',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
