import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  ApiError
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// POST /api/admin/projects/bulk-action - Perform bulk actions on projects
export const POST = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { action, ids } = await request.json()
  
  if (!action || !ids || !Array.isArray(ids) || ids.length === 0) {
    throw new ApiError('Invalid action or project IDs', 400)
  }

  // Verify all projects exist
  const existingProjects = await prisma.projects.findMany({
    where: { id: { in: ids } },
    select: { id: true },
  })

  if (existingProjects.length !== ids.length) {
    throw new ApiError('Some projects not found', 404)
  }

  let result
  let message

  switch (action) {
    case 'mark-completed':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { status: 'COMPLETED' },
      })
      message = `${result.count} projects marked as completed`
      break

    case 'mark-on-hold':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { status: 'ON_HOLD' },
      })
      message = `${result.count} projects marked as on hold`
      break

    case 'mark-in-progress':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { status: 'IN_PROGRESS' },
      })
      message = `${result.count} projects marked as in progress`
      break

    case 'mark-planning':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { status: 'PLANNING' },
      })
      message = `${result.count} projects marked as planning`
      break

    case 'mark-featured':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { isfeatured: true },
      })
      message = `${result.count} projects marked as featured`
      break

    case 'unmark-featured':
      result = await prisma.projects.updateMany({
        where: { id: { in: ids } },
        data: { isfeatured: false },
      })
      message = `${result.count} projects unmarked as featured`
      break

    case 'delete':
      // Delete projects (this will cascade to related records)
      result = await prisma.projects.deleteMany({
        where: { id: { in: ids } },
      })
      message = `${result.count} projects deleted`
      break

    default:
      throw new ApiError(`Unknown action: ${action}`, 400)
  }

  return successResponse(result, message)
})
