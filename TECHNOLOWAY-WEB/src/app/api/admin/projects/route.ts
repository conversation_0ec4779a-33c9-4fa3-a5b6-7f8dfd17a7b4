import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  validateRequest
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/projects - List all projects with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search && search.trim()) {
    const searchTerm = search.trim()
    where.OR = [
      { name: { contains: searchTerm, mode: 'insensitive' } },
      { description: { contains: searchTerm, mode: 'insensitive' } },
      { projgoals: { contains: searchTerm, mode: 'insensitive' } },
      { tags: { contains: searchTerm, mode: 'insensitive' } },
      {
        clients: {
          companyname: { contains: searchTerm, mode: 'insensitive' }
        }
      },
      {
        clients: {
          contactname: { contains: searchTerm, mode: 'insensitive' }
        }
      }
    ]
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.status) where.status = filters.status
      if (filters.isfeatured !== undefined) where.isfeatured = filters.isfeatured === 'true'
      if (filters.ispublic !== undefined) where.ispublic = filters.ispublic === 'true'
      if (filters.clientid) {
        const cid = typeof filters.clientid === 'string' ? Number(filters.clientid) : filters.clientid;
        if (!isNaN(cid)) where.clientid = cid;
      }
      if (filters.orderid) {
        const oid = typeof filters.orderid === 'string' ? Number(filters.orderid) : filters.orderid;
        if (!isNaN(oid)) where.orderid = oid;
      }
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  // Build orderBy clause with field mapping
  let orderBy: any;

  // Map frontend sort fields to database fields
  const sortFieldMap: Record<string, any> = {
    'client': { clients: { companyname: sortOrder || 'asc' } },
    'clients.companyname': { clients: { companyname: sortOrder || 'asc' } },
    'budget': { estimatecost: sortOrder || 'asc' },
    'timeline': { projstartdate: sortOrder || 'asc' },
    'manager': { projmanager: sortOrder || 'asc' },
    'flags': { status: sortOrder || 'asc' } // Map flags to status field
  };

  if (sortBy && sortFieldMap[sortBy]) {
    orderBy = sortFieldMap[sortBy];
  } else if (sortBy) {
    orderBy = { [sortBy]: sortOrder || 'asc' };
  } else {
    orderBy = { createdat: 'desc' };
  }

  const [projects, total] = await Promise.all([
    prisma.projects.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true,
          },
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true,
            ordertotalamount: true,
          },
        },
        teammembers: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            tasks: true,
            projectdocuments: true,
            messages: true,
          },
        },
      },
    }),
    prisma.projects.count({ where }),
  ])

  return paginatedResponse(projects, page, limit, total)
})

// POST /api/admin/projects - Create a new project
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.project.create)
  const data = await validate(request)

  // Validate order exists
  const orderIdNum = data.orderid && !isNaN(Number(data.orderid)) ? Number(data.orderid) : undefined;
  const order = await prisma.orders.findUnique({
    where: { id: orderIdNum },
  })

  if (!order) {
    throw new Error('Order not found')
  }

  // Validate client exists if provided
  let clientIdNum: number | undefined = undefined;
  if (data.clientid && !isNaN(Number(data.clientid))) {
    clientIdNum = Number(data.clientid);
    const client = await prisma.clients.findUnique({
      where: { id: clientIdNum },
    })

    if (!client) {
      throw new Error('Client not found')
    }
  }

  // Prepare project data
  const { projmanager, clientid, orderid, ...restData } = data;
  const projectData: any = { ...restData };
  if (orderIdNum !== undefined) projectData.orderid = orderIdNum;
  if (clientIdNum !== undefined) projectData.clientid = clientIdNum;
  // projmanager is omitted because the schema expects it to be undefined

  const project = await prisma.projects.create({
    data: projectData,
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true,
        },
      },
      orders: {
        select: {
          id: true,
          ordertitle: true,
          status: true,
          ordertotalamount: true,
        },
      },
      teammembers: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          tasks: true,
          projectdocuments: true,
          messages: true,
        },
      },
    },
  })

  return successResponse(project, 'Project created successfully', 201)
})
