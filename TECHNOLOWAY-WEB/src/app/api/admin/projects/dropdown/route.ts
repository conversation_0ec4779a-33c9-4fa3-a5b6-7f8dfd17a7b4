import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withE<PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/projects/dropdown - Get projects for dropdown selection
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const projects = await prisma.projects.findMany({
    select: {
      id: true,
      name: true,
      status: true,
      clients: {
        select: {
          companyname: true,
        },
      },
    },
    orderBy: {
      createdat: 'desc',
    },
  })

  // Format for dropdown
  const formattedProjects = projects.map(project => ({
    value: project.id,
    label: `${project.name} - ${project.clients?.companyname || 'No Client'} (${project.status})`,
    name: project.name,
    clientname: project.clients?.companyname || 'No Client',
    status: project.status,
  }))

  return successResponse(formattedProjects)
})
