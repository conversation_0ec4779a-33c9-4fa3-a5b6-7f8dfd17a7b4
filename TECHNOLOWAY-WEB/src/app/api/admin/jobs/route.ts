import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON>andler, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/jobs - Get all job listings with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['title', 'description', 'location', 'employmenttype'])
  
  // Add active filter if provided
  if (filter === 'active') {
    searchQuery.isactive = true
  } else if (filter === 'inactive') {
    searchQuery.isactive = false
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get job listings with pagination
  const [jobListings, total] = await Promise.all([
    prisma.joblistings.findMany({
      where: searchQuery,
      include: {
        jobapplications: {
          select: {
            id: true,
            applicantname: true,
            applicantemail: true,
            status: true,
            createdat: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.joblistings.count({ where: searchQuery })
  ])

  // Transform BigInt to string for JSON serialization
  const transformedJobListings = jobListings.map(job => ({
    ...job,
    id: job.id.toString(),
    salarymin: job.salarymin ? Number(job.salarymin) : null,
    salarymax: job.salarymax ? Number(job.salarymax) : null,
    jobapplications: job.jobapplications?.map(app => ({
      ...app,
      id: app.id.toString()
    }))
  }))

  return paginatedResponse(transformedJobListings, page, limit, total)
})

// POST /api/admin/jobs - Create a new job listing
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.jobListing.create)
  const data = await validate(request)

  const jobListing = await prisma.joblistings.create({
    data,
    include: {
      jobapplications: {
        select: {
          id: true,
          applicantname: true,
          applicantemail: true,
          status: true,
          createdat: true
        }
      }
    }
  })

  // Transform BigInt to string for JSON serialization
  const transformedJobListing = {
    ...jobListing,
    id: jobListing.id.toString(),
    salarymin: jobListing.salarymin ? Number(jobListing.salarymin) : null,
    salarymax: jobListing.salarymax ? Number(jobListing.salarymax) : null,
    jobapplications: jobListing.jobapplications?.map(app => ({
      ...app,
      id: app.id.toString()
    }))
  }

  return successResponse(transformedJobListing, 'Job listing created successfully', 201)
})
