import { NextRequest } from 'next/server'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successR<PERSON>ponse, 
  requireAdmin,
  validate<PERSON>eth<PERSON> 
} from '@/services/api/api-utils'
import { 
  parseFormData, 
  uploadFiles, 
  FileUploadError,
  UPLOAD_CONFIG
} from '@/services/file-upload/file-upload'

// POST /api/admin/contact-forms/attachments - Upload attachments for contact form replies
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['POST'])

  try {
    // Parse form data
    const { files, fields } = await parseFormData(request)

    if (files.length === 0) {
      throw new Error('No files provided')
    }

    // Validate file types for email attachments
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ]

    // Check file types
    for (const file of files) {
      if (!allowedTypes.includes(file.type)) {
        throw new FileUploadError(
          `File type ${file.type} is not allowed for email attachments`,
          'INVALID_FILE_TYPE'
        )
      }
    }

    // Upload files to attachments subdirectory
    const uploadedFiles = await uploadFiles(files, 'contact-attachments', false)

    // Format response with attachment info
    const attachments = uploadedFiles.map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      filename: file.originalName,
      size: file.size,
      mimeType: file.mimeType,
      url: file.url,
      path: file.path,
    }))

    return successResponse(
      {
        attachments,
        count: attachments.length,
      },
      `${attachments.length} attachment(s) uploaded successfully`
    )
  } catch (error) {
    if (error instanceof FileUploadError) {
      throw error
    }
    throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
})

// GET /api/admin/contact-forms/attachments - Get upload configuration
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  return successResponse({
    maxFileSize: UPLOAD_CONFIG.maxFileSize,
    allowedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp'
    ],
    maxFiles: 5,
    supportedFormats: [
      'PDF documents',
      'Word documents (.doc, .docx)',
      'Excel spreadsheets (.xls, .xlsx)',
      'Text files (.txt)',
      'Images (JPEG, PNG, GIF, WebP)'
    ]
  })
})
