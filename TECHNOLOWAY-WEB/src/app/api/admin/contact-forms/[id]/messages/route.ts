import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON>rror<PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { transformFromDbFields } from '@/lib/utils/data-transform'
import { z } from 'zod'
import {
  validateChatAccess,
  filterMessagesForUser,
  getAllowedRecipients
} from '@/services/chat/chat-access-control'

interface RouteParams {
  params: { id: string }
}

// Message schema for creating new chat messages
const createMessageSchema = z.object({
  message: z.string().optional().default(''),
  contenttype: z.enum(['text', 'html', 'file']).default('text'),
  attachments: z.array(z.object({
    id: z.string(),
    filename: z.string(),
    size: z.number(),
    mimeType: z.string(),
    url: z.string(),
    path: z.string(),
  })).default([]),
  receiverid: z.number().optional(),
  messagetype: z.enum(['contact', 'reply', 'chat']).default('chat'),
}).refine(
  (data) => data.message.trim().length > 0 || data.attachments.length > 0,
  {
    message: 'Either message or attachments are required',
    path: ['message']
  }
)

// GET /api/admin/contact-forms/[id]/messages - Get all messages in a thread
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const contactFormId = parseInt(resolvedParams.id)
  if (isNaN(contactFormId)) {
    throw new ApiError('Invalid contact form ID', 400)
  }

  // Validate access with new chat access control
  const { user } = await validateChatAccess(request, contactFormId)

  // Get the original contact form to determine thread ID
  const originalContactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) }
  })

  if (!originalContactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Determine thread ID - use the original contact form ID as thread ID if not set
  const threadId = originalContactForm.threadid || originalContactForm.id

  // Get all messages in the thread, including the original contact form
  const messages = await prisma.contactforms.findMany({
    where: {
      OR: [
        { id: BigInt(contactFormId) }, // Original contact form
        { threadid: threadId }, // All messages in the thread
        { parentid: BigInt(contactFormId) } // Direct replies to original
      ]
    },
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      parent: {
        select: {
          id: true,
          subject: true,
          message: true,
          createdat: true
        }
      }
    },
    orderBy: {
      createdat: 'asc'
    }
  })

  // Transform the messages
  const transformedMessages = messages.map(message => ({
    ...transformFromDbFields.contactForm(message),
    sender: message.sender,
    receiver: message.receiver,
    parent: message.parent ? transformFromDbFields.contactForm(message.parent) : null,
    attachments: message.attachments ? JSON.parse(message.attachments) : []
  }))

  // Filter messages based on user access rights
  const filteredMessages = filterMessagesForUser(transformedMessages, user)

  return successResponse({
    messages: filteredMessages,
    threadId: threadId.toString(),
    totalCount: filteredMessages.length
  })
})

// POST /api/admin/contact-forms/[id]/messages - Create a new chat message
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const contactFormId = parseInt(resolvedParams.id)
  if (isNaN(contactFormId)) {
    throw new ApiError('Invalid contact form ID', 400)
  }

  // Validate the message data
  const validate = validateRequest(createMessageSchema)
  const data = await validate(request)

  // Validate access with new chat access control
  const { user } = await validateChatAccess(
    request,
    contactFormId,
    data.receiverid
  )

  // Get the original contact form to determine thread and receiver
  const originalContactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) }
  })

  if (!originalContactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Determine thread ID - use the original contact form ID as thread ID if not set
  const threadId = originalContactForm.threadid || originalContactForm.id

  // Determine receiver - if not specified, use the original contact form sender
  let receiverId = data.receiverid
  if (!receiverId) {
    // For original contact forms without sender ID, we'll need to find or create a user
    if (originalContactForm.senderid) {
      receiverId = Number(originalContactForm.senderid)
    } else {
      // Try to find user by email from original contact form
      const existingUser = await prisma.users.findUnique({
        where: { email: originalContactForm.email }
      })
      if (existingUser) {
        receiverId = Number(existingUser.id)
      }
    }
  }

  // Create the new message
  const newMessage = await prisma.contactforms.create({
    data: {
      name: `${user.firstname || ''} ${user.lastname || ''}`.trim() || user.email,
      email: user.email || '',
      phone: null,
      subject: `Re: ${originalContactForm.subject}`,
      message: data.message,
      messagetype: data.messagetype,
      contenttype: data.contenttype,
      attachments: data.attachments.length > 0 ? JSON.stringify(data.attachments) : null,
      threadid: threadId,
      parentid: BigInt(contactFormId),
      senderid: BigInt(user.id),
      receiverid: receiverId ? BigInt(receiverId) : null,
      isread: false,
      isdelivered: true,
      deliveredat: new Date(),
      status: 'New'
    },
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      parent: {
        select: {
          id: true,
          subject: true,
          message: true,
          createdat: true
        }
      }
    }
  })

  // Update the original contact form with thread ID if not set
  if (!originalContactForm.threadid) {
    await prisma.contactforms.update({
      where: { id: BigInt(contactFormId) },
      data: { threadid: threadId }
    })
  }

  // Send email notification to client/user
  try {
    // Get client/user email - either from receiver or original contact form
    let clientEmail = ''
    let clientName = ''

    if (newMessage.receiver) {
      clientEmail = newMessage.receiver.email
      clientName = `${newMessage.receiver.firstname || ''} ${newMessage.receiver.lastname || ''}`.trim() || newMessage.receiver.email
    } else {
      // Fallback to original contact form email
      clientEmail = originalContactForm.email
      clientName = originalContactForm.name
    }

    if (clientEmail && user.role === 'ADMIN') {
      const { sendChatNotification } = await import('@/lib/email')

      // Prepare attachments for email
      const emailAttachments = newMessage.attachments
        ? JSON.parse(newMessage.attachments).map((att: any) => ({
            filename: att.filename,
            url: att.url
          }))
        : []

      await sendChatNotification({
        clientName,
        clientEmail,
        senderName: `${user.firstname || ''} ${user.lastname || ''}`.trim() || user.email,
        subject: originalContactForm.subject,
        message: data.message,
        messageDate: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }),
        attachments: emailAttachments,
        dashboardUrl: `${process.env.NEXTAUTH_URL}/client-auth/signin`
      })

      console.log(`Chat notification email sent to ${clientEmail}`)
    }
  } catch (emailError) {
    console.error('Failed to send chat notification email:', emailError)
    // Don't fail the request if email fails, just log it
  }

  // Transform the response
  const transformedMessage = {
    ...transformFromDbFields.contactForm(newMessage),
    sender: newMessage.sender,
    receiver: newMessage.receiver,
    parent: newMessage.parent ? transformFromDbFields.contactForm(newMessage.parent) : null,
    attachments: newMessage.attachments ? JSON.parse(newMessage.attachments) : []
  }

  return successResponse(transformedMessage, 'Message sent successfully', 201)
})
