import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/orders/[id] - Get a specific order
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const order = await prisma.orders.findUnique({
    where: { id },
    include: {
      client: true,
      orderDetails: {
        include: {
          service: true,
          serviceOption: true
        }
      },
      projects: {
        include: {
          services: true,
          technologies: true
        }
      },
      invoices: true,
      contracts: true
    }
  })

  if (!order) {
    throw new ApiError('Order not found', 404)
  }

  return successResponse(order)
})

// PUT /api/admin/orders/[id] - Update an order
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.order.update)
  const data = await validate(request)

  // Check if order exists
  const existingOrder = await prisma.orders.findUnique({
    where: { id },
  })

  if (!existingOrder) {
    throw new ApiError('Order not found', 404)
  }

  const order = await prisma.orders.update({
    where: { id },
    data,
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      orderDetails: {
        include: {
          service: {
            select: {
              id: true,
              name: true
            }
          },
          serviceOption: {
            select: {
              id: true,
              name: true
            }
          }
        }
      },
      projects: {
        select: {
          id: true,
          name: true,
          status: true
        }
      }
    }
  })

  return successResponse(order, 'Order updated successfully')
})

// DELETE /api/admin/orders/[id] - Delete an order
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if order exists
  const existingOrder = await prisma.orders.findUnique({
    where: { id },
  })

  if (!existingOrder) {
    throw new ApiError('Order not found', 404)
  }

  // Check if order has associated projects
  const projectsCount = await prisma.projects.count({
    where: { orderid: id }
  })

  if (projectsCount > 0) {
    throw new ApiError('Cannot delete order with associated projects', 400)
  }

  // Delete order details first
  await prisma.ordersDetail.deleteMany({
    where: { orderid: id }
  })

  // Then delete the order
  await prisma.orders.delete({
    where: { id }
  })

  return successResponse(null, 'Order deleted successfully')
})
