import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/orders - Get all orders with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['orderNumber', 'description'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get orders with pagination
  const [orders, total] = await Promise.all([
    prisma.orderss.findMany({
      where: searchQuery,
      include: {
        client: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        orderDetails: {
          include: {
            service: {
              select: {
                id: true,
                name: true
              }
            },
            serviceOption: {
              select: {
                id: true,
                name: true
              }
            }
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        invoices: {
          select: {
            id: true,
            invoiceNumber: true,
            status: true,
            totalamount: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.orderss.count({ where: searchQuery })
  ])

  return paginatedResponse(orders, page, limit, total)
})

// POST /api/admin/orders - Create a new order
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.order.create)
  const data = await validate(request)

  const order = await prisma.orderss.create({
    data,
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
          contactname: true,
          contactemail: true
        }
      },
      orderDetails: true
    }
  })

  return successResponse(order, 'Order created successfully', 201)
})
