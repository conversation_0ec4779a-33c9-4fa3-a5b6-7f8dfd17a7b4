import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  paginatedResponse,
  successResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  validateRequest
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/team-members - List all team members with pagination and search
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search && search.trim()) {
    const searchQuery = buildSearchQuery(search.trim(), [
      'name',
      'email',
      'position',
      'phone'
    ])
    Object.assign(where, searchQuery)
  }

  // Add filters
  if (filter) {
    try {
      const filters = JSON.parse(filter)
      if (filters.isActive !== undefined) where.isactive = filters.isActive === 'true'
      if (filters.department) where.department = filters.department
      if (filters.position) where.position = filters.position
    } catch (e) {
      // Invalid filter JSON, ignore
    }
  }

  // Build orderBy clause with field mapping
  const fieldMapping: Record<string, string> = {
    'createdAt': 'createdat',
    'updatedAt': 'updatedat',
    'hireDate': 'hiredate',
    'birthDate': 'birthdate',
    'displayOrder': 'displayorder',
    'isActive': 'isactive',
    'photoUrl': 'photourl',
    'linkedinUrl': 'linkedinurl',
    'twitterUrl': 'twitterurl',
    'githubUrl': 'githuburl'
  }

  const orderBy: any = {}
  if (sortBy) {
    const mappedSortBy = fieldMapping[sortBy] || sortBy
    orderBy[mappedSortBy] = sortOrder || 'asc'
  } else {
    orderBy.updatedat = 'desc' // Default sort by Last Active
  }

  const [teamMembers, total] = await Promise.all([
    prisma.teammembers.findMany({
      where,
      skip,
      take,
      orderBy,
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
          },
          take: 3,
        },
        payrollrecords: {
          select: {
            id: true,
            paydate: true,
            grosspay: true,
          },
          orderBy: {
            paydate: 'desc',
          },
          take: 3,
        },
        tasks: {
          select: {
            id: true,
            taskdesc: true,
            status: true,
          },
          take: 5,
        },
        _count: {
          select: {
            projects: true,
            payrollrecords: true,
            tasks: true,
          },
        },
      },
    }),
    prisma.teammembers.count({ where }),
  ])

  // Transform the data for frontend
  const transformedTeamMembers = teamMembers.map(member => transformFromDbFields.teamMember(member))

  // Convert any remaining BigInt values to Numbers
  const serializedTeamMembers = JSON.parse(JSON.stringify(transformedTeamMembers, (key, value) =>
    typeof value === 'bigint' ? Number(value) : value
  ))

  return paginatedResponse(serializedTeamMembers, page, limit, Number(total))
})

// POST /api/admin/team-members - Create a new team member
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.teamMember.create)
  const validatedData = await validate(request)

  // Check if a team member with the same email already exists
  if (validatedData.email) {
    const existingMember = await prisma.teammembers.findFirst({
      where: {
        email: validatedData.email,
      },
    })

    if (existingMember) {
      throw new Error('A team member with this email already exists')
    }
  }

  // Transform data to database format
  const dbData = transformToDbFields.teamMember(validatedData)

  const teamMember = await prisma.teammembers.create({
    data: dbData,
    include: {
      projects: {
        select: {
          id: true,
          name: true,
          status: true,
        },
        take: 3,
      },
      payrollrecords: {
        select: {
          id: true,
          paydate: true,
          grosspay: true,
        },
        orderBy: {
          paydate: 'desc',
        },
        take: 3,
      },
      tasks: {
        select: {
          id: true,
          taskdesc: true,
          status: true,
        },
        take: 5,
      },
      _count: {
        select: {
          projects: true,
          payrollrecords: true,
          tasks: true,
        },
      },
    },
  })

  const transformedTeamMember = transformFromDbFields.teamMember(teamMember)
  return successResponse(transformedTeamMember, 'Team member created successfully', 201)
})

// PUT /api/admin/team-members - Bulk update team members
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid team member IDs provided' },
      { status: 400 }
    )
  }

  const updatedTeamMembers = await prisma.teammembers.updateMany({
    where: {
      id: {
        in: ids.map((id: any) => BigInt(id)),
      },
    },
    data: transformToDbFields.teamMember(data),
  })

  return successResponse(
    { count: updatedTeamMembers.count },
    `${updatedTeamMembers.count} team members updated successfully`
  )
})

// DELETE /api/admin/team-members - Bulk delete team members
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid team member IDs provided' },
      { status: 400 }
    )
  }

  // Check if any team members have associated data that should be preserved
  const membersWithData = await prisma.teammembers.findMany({
    where: {
      id: { in: ids.map((id: any) => BigInt(id)) },
      OR: [
        { projects: { some: {} } },
        { payrollrecords: { some: {} } },
        { tasks: { some: {} } },
      ],
    },
    select: { id: true, name: true },
  })

  if (membersWithData.length > 0) {
    const memberNames = membersWithData.map(m => m.name).join(', ')
    return NextResponse.json(
      { 
        success: false, 
        error: `Cannot delete team members with associated data: ${memberNames}. Please handle their projects, payroll records, and tasks first.` 
      },
      { status: 400 }
    )
  }

  const deletedTeamMembers = await prisma.teammembers.deleteMany({
    where: {
      id: {
        in: ids.map((id: any) => BigInt(id)),
      },
    },
  })

  return successResponse(
    { count: deletedTeamMembers.count },
    `${deletedTeamMembers.count} team members deleted successfully`
  )
})
