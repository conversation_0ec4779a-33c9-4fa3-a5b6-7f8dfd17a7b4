import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
} from '@/services/api/api-utils'

// POST /api/admin/team-members/bulk-action - Perform bulk actions on team members
export const POST = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)

  const { action, ids } = await request.json()

  if (!action || !Array.isArray(ids) || ids.length === 0) {
    return NextResponse.json(
      { success: false, error: 'Invalid action or team member IDs' },
      { status: 400 }
    )
  }

  // Convert string IDs to BigInt for database operations
  const bigIntIds = ids.map(id => BigInt(id))

  let result
  let message

  switch (action) {
    case 'activate':
      result = await prisma.teammembers.updateMany({
        where: {
          id: { in: bigIntIds }
        },
        data: {
          isactive: true,
          updatedat: new Date()
        }
      })
      message = `${result.count} team member(s) activated successfully`
      break

    case 'deactivate':
      result = await prisma.teammembers.updateMany({
        where: {
          id: { in: bigIntIds }
        },
        data: {
          isactive: false,
          updatedat: new Date()
        }
      })
      message = `${result.count} team member(s) deactivated successfully`
      break

    case 'delete':
      // Check if any team members have associated data
      const teamMembersWithData = await prisma.teammembers.findMany({
        where: {
          id: { in: bigIntIds }
        },
        include: {
          _count: {
            select: {
              projects: true,
              payrollrecords: true,
              tasks: true,
            }
          }
        }
      })

      const membersWithAssociatedData = teamMembersWithData.filter(member => 
        member._count.projects > 0 || 
        member._count.payrollrecords > 0 || 
        member._count.tasks > 0
      )

      if (membersWithAssociatedData.length > 0) {
        const memberNames = membersWithAssociatedData.map(m => m.name).join(', ')
        return NextResponse.json(
          {
            success: false,
            error: `Cannot delete team members: ${memberNames}. They have associated projects, payroll records, or tasks. Please handle these first.`
          },
          { status: 400 }
        )
      }

      result = await prisma.teammembers.deleteMany({
        where: {
          id: { in: bigIntIds }
        }
      })
      message = `${result.count} team member(s) deleted successfully`
      break

    default:
      return NextResponse.json(
        { success: false, error: `Unknown action: ${action}` },
        { status: 400 }
      )
  }

  return successResponse(
    { 
      action, 
      affectedCount: result.count,
      ids: ids 
    },
    message
  )
})
