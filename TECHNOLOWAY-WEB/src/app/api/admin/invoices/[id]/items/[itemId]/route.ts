import { prisma } from '@/config/prisma'
import {
    requireAdmin,
    successResponse,
    validateRequest,
    with<PERSON><PERSON>r<PERSON><PERSON><PERSON>
} from '@/services/api/api-utils'
import { NextRequest } from 'next/server'
import { z } from 'zod'

interface RouteParams {
  params: Promise<{ id: string; itemId: string }>
}

const updateInvoiceItemSchema = z.object({
  description: z.string().min(1).optional(),
  quantity: z.coerce.number().positive().optional(),
  unitPrice: z.coerce.number().positive().optional(),
  totalPrice: z.coerce.number().positive().optional(),
})

// PUT /api/admin/invoices/[id]/items/[itemId] - Update invoice item
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id, itemId } = await params
  const validate = validateRequest(updateInvoiceItemSchema)
  const data = await validate(request)

  const item = await prisma.invoiceitems.update({
    where: { id: BigInt(itemId) },
    data: {
      description: data.description,
      quantity: data.quantity,
      unitprice: data.unitPrice,
      totalprice: data.totalPrice,
      updatedat: new Date()
    }
  })

  // Recalculate invoice totals
  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) },
    include: { invoiceitems: true }
  })

  if (invoice) {
    const subtotal = invoice.invoiceitems.reduce((sum, item) => sum + Number(item.totalprice), 0)
    const totalAmount = subtotal + Number(invoice.taxamount)

    await prisma.invoices.update({
      where: { id: BigInt(id) },
      data: {
        subtotal,
        totalamount: totalAmount
      }
    })
  }

  return successResponse(item, 'Invoice item updated successfully')
})

// DELETE /api/admin/invoices/[id]/items/[itemId] - Delete invoice item
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id, itemId } = await params

  await prisma.invoiceitems.delete({
    where: { id: BigInt(itemId) }
  })

  // Recalculate invoice totals
  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) },
    include: { invoiceitems: true }
  })

  if (invoice) {
    const subtotal = invoice.invoiceitems.reduce((sum, item) => sum + Number(item.totalprice), 0)
    const totalAmount = subtotal + Number(invoice.taxamount)

    await prisma.invoices.update({
      where: { id: BigInt(id) },
      data: {
        subtotal,
        totalamount: totalAmount
      }
    })
  }

  return successResponse(null, 'Invoice item deleted successfully')
})
