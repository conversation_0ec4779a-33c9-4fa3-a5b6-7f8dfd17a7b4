import { prisma } from '@/config/prisma'
import {
    ApiError,
    requireAdmin,
    successResponse,
    validateRequest,
    with<PERSON><PERSON>r<PERSON><PERSON><PERSON>
} from '@/services/api/api-utils'
import { NextRequest } from 'next/server'
import { z } from 'zod'

interface RouteParams {
  params: Promise<{ id: string }>
}

const createInvoiceItemSchema = z.object({
  description: z.string().min(1),
  quantity: z.coerce.number().positive().default(1),
  unitPrice: z.coerce.number().positive(),
  totalPrice: z.coerce.number().positive(),
})

const updateInvoiceItemSchema = createInvoiceItemSchema.partial()

// GET /api/admin/invoices/[id]/items - Get all items for an invoice
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // First check if the invoice exists
  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) }
  })

  if (!invoice) {
    return successResponse([])
  }

  const items = await prisma.invoiceitems.findMany({
    where: { invoiceid: BigInt(id) },
    orderBy: { createdat: 'asc' }
  })

  console.log('Raw items from database:', items)
  console.log('Items after serialization will be handled by successResponse')

  return successResponse(items)
})

// POST /api/admin/invoices/[id]/items - Add item to invoice
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(createInvoiceItemSchema)
  const data = await validate(request)

  console.log('Creating item for invoice ID:', id, 'Data:', data)

  // Check if invoice exists
  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  const item = await prisma.invoiceitems.create({
    data: {
      description: data.description,
      quantity: data.quantity,
      unitprice: data.unitPrice,
      totalprice: data.totalPrice,
      invoiceid: BigInt(id)
    }
  })

  // Recalculate invoice totals
  const items = await prisma.invoiceitems.findMany({
    where: { invoiceid: BigInt(id) }
  })

  const subtotal = items.reduce((sum, item) => sum + Number(item.totalprice), 0)
  const totalAmount = subtotal + Number(invoice.taxamount)

  await prisma.invoices.update({
    where: { id: BigInt(id) },
    data: {
      subtotal,
      totalamount: totalAmount
    }
  })

  return successResponse(item, 'Invoice item added successfully', 201)
})
