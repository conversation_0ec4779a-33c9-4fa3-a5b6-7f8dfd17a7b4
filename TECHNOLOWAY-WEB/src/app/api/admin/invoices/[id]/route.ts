import { prisma } from '@/config/prisma'
import {
    ApiError,
    requireAdmin,
    successResponse,
    withE<PERSON>r<PERSON><PERSON><PERSON>
} from '@/services/api/api-utils'
import { NextRequest } from 'next/server'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/invoices/[id] - Get a specific invoice
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const invoice = await prisma.invoices.findUnique({
    where: { id },
    include: {
      clients: true,
      projects: true,
      orders: true,
      contracts: true,
      invoiceitems: true,
      payments: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  return successResponse(invoice)
})

// PUT /api/admin/invoices/[id] - Update an invoice
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received invoice update data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Get existing invoice to preserve foreign key relationships
  const existingInvoice = await prisma.invoices.findUnique({
    where: { id },
    select: {
      clientid: true,
      projectid: true,
      orderid: true,
      contid: true
    }
  })

  if (!existingInvoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Transform form data to database field names, preserving existing foreign keys
  const cleanInvoiceData = {
    // Preserve existing foreign key relationships
    clientid: existingInvoice.clientid,
    projectid: existingInvoice.projectid,
    orderid: existingInvoice.orderid,
    contid: existingInvoice.contid,
    // Update only the editable fields
    description: invoiceData.description,
    subtotal: invoiceData.subtotal ? Number(invoiceData.subtotal) : undefined,
    taxrate: invoiceData.taxRate ? Number(invoiceData.taxRate) : undefined,
    taxamount: invoiceData.taxAmount ? Number(invoiceData.taxAmount) : undefined,
    totalamount: invoiceData.totalAmount ? Number(invoiceData.totalAmount) : undefined,
    status: invoiceData.status,
    duedate: invoiceData.dueDate ? new Date(invoiceData.dueDate) : undefined,
    // paidat field removed - not used in this context
  }

  // Remove undefined values
  const validatedData = Object.fromEntries(
    Object.entries(cleanInvoiceData).filter(([_, value]) => value !== undefined)
  )

  // Update invoice and handle items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Update the invoice
    const updatedInvoice = await tx.invoices.update({
      where: { id },
      data: validatedData,
    })

    // Handle items if provided
    if (items && Array.isArray(items)) {
      console.log('Processing items in transaction:', items.length)

      // Get existing items
      const existingItems = await tx.invoiceitems.findMany({
        where: { invoiceid: BigInt(id) }
      })
      console.log('Found existing items:', existingItems.length)

      // Separate items into update, create, and delete operations
      const itemsToUpdate = items.filter(item => item.id)
      const itemsToCreate = items.filter(item => !item.id)
      const existingItemIds = existingItems.map(item => item.id.toString())
      const submittedItemIds = itemsToUpdate.map(item => item.id.toString())
      const itemsToDelete = existingItemIds.filter(id => !submittedItemIds.includes(id))

      console.log('Items to update:', itemsToUpdate.length, itemsToUpdate)
      console.log('Items to create:', itemsToCreate.length, itemsToCreate)
      console.log('Items to delete:', itemsToDelete.length, itemsToDelete)

      // Delete removed items
      if (itemsToDelete.length > 0) {
        console.log('Deleting items:', itemsToDelete)
        await tx.invoiceitems.deleteMany({
          where: {
            id: { in: itemsToDelete.map(id => BigInt(id)) }
          }
        })
      }

      // Update existing items
      for (const item of itemsToUpdate) {
        console.log('Updating item:', item.id, 'with data:', {
          description: item.description,
          quantity: Number(item.quantity),
          unitprice: Number(item.unitPrice),
          totalprice: Number(item.totalPrice)
        })
        await tx.invoiceitems.update({
          where: { id: BigInt(item.id) },
          data: {
            description: item.description,
            quantity: Number(item.quantity),
            unitprice: Number(item.unitPrice),
            totalprice: Number(item.totalPrice),
            updatedat: new Date()
          }
        })
      }

      // Create new items
      for (const item of itemsToCreate) {
        console.log('Creating new item:', item)
        await tx.invoiceitems.create({
          data: {
            invoiceid: BigInt(id),
            description: item.description,
            quantity: Number(item.quantity),
            unitprice: Number(item.unitPrice),
            totalprice: Number(item.totalPrice)
          }
        })
      }
    }

    // Return the updated invoice with all relations
    return await tx.invoices.findUnique({
      where: { id },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            contname: true,
            contstatus: true
          }
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      }
    })
  })

  return successResponse(invoice, 'Invoice updated successfully')
})

// DELETE /api/admin/invoices/[id] - Delete an invoice
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  // Check if invoice has payments
  const paymentsCount = await prisma.payments.count({
    where: { invoiceid: id }
  })

  if (paymentsCount > 0) {
    throw new ApiError('Cannot delete invoice with associated payments', 400)
  }

  // Delete invoice items first
  await prisma.invoiceitems.deleteMany({
    where: { invoiceid: id }
  })

  // Then delete the invoice
  await prisma.invoices.delete({
    where: { id }
  })

  return successResponse(null, 'Invoice deleted successfully')
})
