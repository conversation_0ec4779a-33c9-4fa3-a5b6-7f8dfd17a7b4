import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  withError<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/testimonials - Get all testimonials with pagination and search
export const GET = withErrorHand<PERSON>(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['clientname', 'clienttitle', 'clientcompany', 'content'])
  
  // Build sort query with field name mapping
  const fieldMapping: { [key: string]: string } = {
    'updatedAt': 'updatedat',
    'createdAt': 'createdat',
    'displayOrder': 'displayorder',
    'isFeatured': 'isfeatured',
    'clientName': 'clientname',
    'clientTitle': 'clienttitle',
    'clientCompany': 'clientcompany',
    'clientPhotoUrl': 'clientphotourl'
  }

  const mappedSortBy = fieldMapping[sortBy] || sortBy
  const sortQuery = buildSortQuery(mappedSortBy, sortOrder)

  // Get testimonials with pagination
  const [testimonials, total] = await Promise.all([
    prisma.testimonials.findMany({
      where: searchQuery,
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.testimonials.count({ where: searchQuery })
  ])

  // Transform testimonials to frontend format
  const transformedTestimonials = testimonials.map(testimonial => {
    const transformed = transformFromDbFields.testimonial(testimonial)
    // Ensure ID is a string to avoid BigInt serialization issues
    return {
      ...transformed,
      id: String(transformed.id)
    }
  })

  return paginatedResponse(transformedTestimonials, total, page, limit, 'Testimonials retrieved successfully')
})

// POST /api/admin/testimonials - Create a new testimonial
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const validate = validateRequest(schemas.testimonial.create)
  const data = await validate(request)

  // Transform data to database format
  const dbData = transformToDbFields.testimonial(data)

  const testimonial = await prisma.testimonials.create({
    data: dbData
  })

  const transformedTestimonial = transformFromDbFields.testimonial(testimonial)
  return successResponse({
    ...transformedTestimonial,
    id: String(transformedTestimonial.id)
  }, 'Testimonial created successfully', 201)
})
