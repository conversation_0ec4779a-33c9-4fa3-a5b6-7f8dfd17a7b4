import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError,
  generateSlug
} from '@/services/api/api-utils'
import { schemas } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/legal-pages/[id] - Get a specific legal page
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const legalPage = await prisma.legalpages.findUnique({
    where: { id: BigInt(id) },
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  if (!legalPage) {
    throw new ApiError('Legal page not found', 404)
  }

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage)
})

// PUT /api/admin/legal-pages/[id] - Update a legal page
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.legalPage.update)
  const validatedData = await validate(request)

  // Generate slug if title is updated but slug is not provided
  if (validatedData.title && !validatedData.slug) {
    validatedData.slug = generateSlug(validatedData.title)
  }

  // Transform data to database format
  const dbData = transformToDbFields.legalPage(validatedData)

  const legalPage = await prisma.legalpages.update({
    where: { id: BigInt(id) },
    data: {
      ...dbData,
      updatedat: new Date(),
    },
    include: {
      legalpagesections: {
        orderBy: { displayorder: 'asc' }
      }
    }
  })

  const transformedLegalPage = transformFromDbFields.legalPage(legalPage)
  return successResponse(transformedLegalPage, 'Legal page updated successfully')
})

// DELETE /api/admin/legal-pages/[id] - Delete a legal page
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // First delete all associated sections
  await prisma.legalpagesections.deleteMany({
    where: { legalpageid: BigInt(id) }
  })

  // Then delete the legal page
  await prisma.legalpages.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Legal page deleted successfully')
})
