import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/admin/contracts/dropdown - Get contracts for dropdown selection
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const contracts = await prisma.contracts.findMany({
    select: {
      id: true,
      title: true,
      status: true,
      totalamount: true,
      client: {
        select: {
          companyname: true,
        },
      },
    },
    orderBy: {
      createdat: 'desc',
    },
  })

  // Format for dropdown
  const formattedContracts = contracts.map(contract => ({
    value: contract.id,
    label: `${contract.title} - ${contract.client.companyname} (${contract.status})`,
    title: contract.title,
    clientname: contract.client.companyname,
    status: contract.status,
    totalamount: contract.totalamount,
  }))

  return successResponse(formattedContracts)
})
