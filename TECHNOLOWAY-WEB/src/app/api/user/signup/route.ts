import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/config/prisma'
import bcrypt from 'bcryptjs'
import { z } from 'zod'
import { AuditLogger } from '@/config/audit-log'

const signUpSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().max(50).optional(),
  email: z.string().email('Invalid email address').transform(val => val.toLowerCase().trim()),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  companyName: z.string().min(1, 'Company name is required').max(100),
  role: z.enum(['CLIENT', 'USER']).default('CLIENT')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = signUpSchema.parse(body)
    
    // Check if user already exists
    const existingUser = await prisma.users.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'An account with this email already exists' },
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user
    const user = await prisma.users.create({
      data: {
        firstname: validatedData.firstName,
        lastname: validatedData.lastName || '',
        email: validatedData.email,
        password: hashedPassword,
        role: validatedData.role,
        emailverified: new Date(), // Auto-verify for now
      },
      select: {
        id: true,
        firstname: true,
        lastname: true,
        email: true,
        role: true,
        createdat: true
      }
    })
    
    // Create client record if role is CLIENT and link it to the user
    if (validatedData.role === 'CLIENT') {
      await prisma.clients.create({
        data: {
          companyname: validatedData.companyName,
          contactname: `${validatedData.firstName} ${validatedData.lastName}`.trim(),
          contactemail: validatedData.email,
          contactphone: '', // Optional field
          address: '', // Optional field
          city: '', // Optional field
          state: '', // Optional field
          zipcode: '', // Optional field
          country: '', // Optional field
          userid: user.id, // ✅ Link the client to the user
        }
      })
    }
    
    // Log successful registration
    await AuditLogger.logAuth(
      'REGISTRATION_SUCCESS',
      user.id.toString(),
      { 
        email: validatedData.email, 
        role: validatedData.role,
        companyName: validatedData.companyName 
      }
    )
    
    return NextResponse.json(
      { 
        message: 'Account created successfully',
        user: {
          id: Number(user.id),
          firstName: user.firstname,
          lastName: user.lastname,
          email: user.email,
          role: user.role
        }
      },
      { status: 201 }
    )
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input data', details: error.errors },
        { status: 400 }
      )
    }

    // Log failed registration attempt
    try {
      await AuditLogger.logAuth(
        'REGISTRATION_FAILED',
        'unknown',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      )
    } catch (logError) {
      // Silent fail for audit logging in production
      if (process.env.NODE_ENV === 'development') {
        console.error('Failed to log registration error:', logError)
      }
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
