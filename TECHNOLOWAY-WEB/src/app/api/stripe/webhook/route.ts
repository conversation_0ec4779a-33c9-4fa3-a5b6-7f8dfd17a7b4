import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { headers } from 'next/headers'
import { PrismaClient } from '@prisma/client'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = headers().get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (err) {
    console.error('Webhook signature verification failed:', err)
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
  }

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent
        
        // Create payment record in database
        await prisma.payments.create({
          data: {
            amount: paymentIntent.amount / 100, // Convert from cents
            paymentdate: new Date(),
            paymentmethod: paymentIntent.metadata.paymentMethod || 'stripe_card',
            status: 'completed',
            notes: 'Payment processed via Stripe',
            reference: paymentIntent.id,
            transactionid: paymentIntent.id,
            processingfee: paymentIntent.application_fee_amount ? paymentIntent.application_fee_amount / 100 : null,
            invoiceid: parseInt(paymentIntent.metadata.invoiceId),
            // Enhanced payment form fields
            currency: paymentIntent.currency.toUpperCase(),
            promocode: paymentIntent.metadata.promoCode || null,
            discount: paymentIntent.metadata.discount ? parseFloat(paymentIntent.metadata.discount) : 0,
            emailreceipt: paymentIntent.metadata.emailReceipt === 'true',
            receiptemail: paymentIntent.metadata.receiptEmail || null,
            termsaccepted: true, // Stripe payments require terms acceptance
            paymentdetails: paymentIntent.metadata.paymentDetails ? JSON.parse(paymentIntent.metadata.paymentDetails) : null,
            stripepaymentintentid: paymentIntent.id,
            stripeclientsecret: paymentIntent.client_secret,
            createdat: new Date(),
            updatedat: new Date(),
          }
        })

        // Update invoice status if fully paid
        const invoice = await prisma.invoices.findUnique({
          where: { id: parseInt(paymentIntent.metadata.invoiceId) },
          include: {
            payments: true
          }
        })

        if (invoice) {
          const totalPaid = invoice.payments.reduce((sum, payment) => sum + Number(payment.amount), 0)
          if (totalPaid >= Number(invoice.totalamount)) {
            await prisma.invoices.update({
              where: { id: invoice.id },
              data: { 
                status: 'PAID',
                paidat: new Date()
              }
            })
          }
        }

        console.log('Payment succeeded:', paymentIntent.id)
        break

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent
        console.log('Payment failed:', failedPayment.id)
        
        // Optionally create a failed payment record
        await prisma.payments.create({
          data: {
            amount: failedPayment.amount / 100,
            paymentdate: new Date(),
            paymentmethod: 'stripe_card',
            status: 'failed',
            notes: `Payment failed: ${failedPayment.last_payment_error?.message || 'Unknown error'}`,
            reference: failedPayment.id,
            transactionid: failedPayment.id,
            invoiceid: parseInt(failedPayment.metadata.invoiceId),
            createdat: new Date(),
            updatedat: new Date(),
          }
        })
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
