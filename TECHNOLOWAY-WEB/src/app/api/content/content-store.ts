import { prisma } from '@/config/prisma'

export interface ContentChange {
  id: string
  pagePath: string
  elementId: string
  oldText: string
  newText: string
  filePath: string
  line?: number
  userId: string
  timestamp: Date
  status: 'draft' | 'published'
}

export class ContentStore {
  static async saveChange(change: Omit<ContentChange, 'id' | 'timestamp'>): Promise<ContentChange> {
    const result = await prisma.contentchanges.create({
      data: {
        pagepath: change.pagePath,
        elementid: change.elementId,
        oldtext: change.oldText,
        newtext: change.newText,
        filepath: change.filePath,
        line: change.line,
        userid: change.userId,
        status: change.status
      }
    })

    return {
      id: result.id.toString(),
      pagePath: result.pagepath,
      elementId: result.elementid,
      oldText: result.oldtext,
      newText: result.newtext,
      filePath: result.filepath,
      line: result.line || undefined,
      userId: result.userid,
      timestamp: result.timestamp,
      status: result.status as 'draft' | 'published'
    }
  }

  static async getChangesByPage(pagePath: string, status?: 'draft' | 'published'): Promise<ContentChange[]> {
    const where: any = { pagepath: pagePath }
    if (status) {
      where.status = status
    }

    const results = await prisma.contentchanges.findMany({
      where,
      orderBy: { timestamp: 'desc' }
    })

    return results.map(result => ({
      id: result.id.toString(),
      pagePath: result.pagepath,
      elementId: result.elementid,
      oldText: result.oldtext,
      newText: result.newtext,
      filePath: result.filepath,
      line: result.line || undefined,
      userId: result.userid,
      timestamp: result.timestamp,
      status: result.status as 'draft' | 'published'
    }))
  }

  static async publishChanges(pagePath: string, userId: string): Promise<void> {
    await prisma.contentchanges.updateMany({
      where: {
        pagepath: pagePath,
        status: 'draft'
      },
      data: {
        status: 'published'
      }
    })
  }

  static async getPublishedContent(pagePath: string): Promise<Record<string, string>> {
    const changes = await prisma.contentchanges.findMany({
      where: {
        pagepath: pagePath,
        status: 'published'
      },
      orderBy: { timestamp: 'desc' }
    })

    // Create a map of elementId to latest published text
    const contentMap: Record<string, string> = {}
    changes.forEach(change => {
      if (!contentMap[change.elementid]) {
        contentMap[change.elementid] = change.newtext
      }
    })

    return contentMap
  }
} 