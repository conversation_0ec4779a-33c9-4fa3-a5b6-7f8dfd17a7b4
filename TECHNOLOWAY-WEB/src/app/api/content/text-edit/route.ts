import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { prisma } from '@/config/prisma'
import { successResponse, errorResponse } from '@/services/api/api-utils'
import fs from 'fs'
import path from 'path'

// POST /api/content/text-edit - Handle text edits from iframe and update source files
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return errorResponse('Unauthorized', 401)
    }

    const body = await request.json()
    const { 
      tagName, 
      className, 
      id, 
      oldText, 
      newText, 
      pagePath, 
      elementPath,
      selector 
    } = body

    console.log('Processing text edit:', {
      pagePath,
      oldText,
      newText,
      tagName,
      className,
      id,
      elementPath,
      selector
    })

    // Validate required fields
    if (!pagePath || !oldText || !newText) {
      return NextResponse.json(
        { error: 'Missing required fields: pagePath, oldText, newText' },
        { status: 400 }
      )
    }

    // Map page paths to actual file paths and their related component files
    const pageFileMap: { [key: string]: string[] } = {
      '/': [
        'src/app/page.tsx',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>'
      ],
      '/about': ['src/app/about/page.tsx'],
      '/services': ['src/app/services/page.tsx'],
      '/projects': ['src/app/projects/page.tsx'],
      '/team': ['src/app/team/page.tsx'],
      '/contact': ['src/app/contact/page.tsx'],
      '/blog': ['src/app/blog/page.tsx'],
      '/portfolio': ['src/app/portfolio/page.tsx'],
      '/technologies': ['src/app/technologies/page.tsx'],
      '/clients': ['src/app/clients/page.tsx']
    }

    const filePaths = pageFileMap[pagePath]
    if (!filePaths) {
      return NextResponse.json(
        { error: `Unknown page path: ${pagePath}` },
        { status: 400 }
      )
    }

    // Search through all files for the text (with more flexible matching)
    let foundFile = null
    let fileContent = ''
    let fullPath = ''

    for (const filePath of filePaths) {
      fullPath = path.join(process.cwd(), filePath)
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf-8')
        
        // Try exact match first
        if (content.includes(oldText)) {
          foundFile = filePath
          fileContent = content
          break
        }
        
        // Try normalized text (remove extra spaces, newlines)
        const normalizedOldText = oldText.replace(/\s+/g, ' ').trim()
        const normalizedContent = content.replace(/\s+/g, ' ').trim()
        if (normalizedContent.includes(normalizedOldText)) {
          foundFile = filePath
          fileContent = content
          break
        }
        
        // Try case-insensitive search
        if (content.toLowerCase().includes(oldText.toLowerCase())) {
          foundFile = filePath
          fileContent = content
          break
        }
      }
    }

    if (!foundFile) {
      return NextResponse.json(
        { 
          error: 'Text not found in any source files',
          details: {
            searchedText: oldText,
            searchedFiles: filePaths,
            pagePath: pagePath
          }
        },
        { status: 404 }
      )
    }

    // Search for the text in the file
    const textIndex = fileContent.indexOf(oldText)
    if (textIndex === -1) {
      // Text not found, try to find similar text for better error reporting
      const lines = fileContent.split('\n')
      const similarLines = lines.filter(line =>
        line.includes(oldText.substring(0, Math.min(10, oldText.length)))
      ).slice(0, 3) // Limit to 3 similar lines for error context
    }

    // Use element selector to find the specific element instead of replacing all matches
    const elementSelector = selector || id || className || tagName
    
    // Try to find the specific element using the provided selector information
    let textFound = false
    let updatedContent = fileContent
    
    // First, try to find the element by its unique identifier
    if (id) {
      const idPattern = new RegExp(`id\\s*=\\s*['"]${escapeRegExp(id)}['"]`, 'i')
      if (idPattern.test(fileContent)) {
        // Find the line containing this ID and replace only that specific text
        const lines = fileContent.split('\n')
        for (let i = 0; i < lines.length; i++) {
          if (idPattern.test(lines[i])) {
            // This line contains our target element, replace the text in this line only
            const line = lines[i]
            if (line.includes(oldText)) {
              lines[i] = line.replace(oldText, newText)
              updatedContent = lines.join('\n')
              textFound = true
              break
            }
          }
        }
      }
    }
    
    // If no specific match found by ID, try by class name
    if (!textFound && className) {
      const classPattern = new RegExp(`class\\s*=\\s*['"][^'"]*${escapeRegExp(className)}[^'"]*['"]`, 'i')
      if (classPattern.test(fileContent)) {
        const lines = fileContent.split('\n')
        for (let i = 0; i < lines.length; i++) {
          if (classPattern.test(lines[i]) && lines[i].includes(oldText)) {
            lines[i] = lines[i].replace(oldText, newText)
            updatedContent = lines.join('\n')
            textFound = true
            break
          }
        }
      }
    }
    
    // If still no match, try to find the most specific occurrence based on context
    if (!textFound) {
      // Look for the text within JSX elements that match our criteria
      const jsxPattern = new RegExp(`<${tagName}[^>]*>\\s*${escapeRegExp(oldText)}\\s*</${tagName}>`, 'gi')
      if (jsxPattern.test(fileContent)) {
        updatedContent = fileContent.replace(jsxPattern, (match) => {
          return match.replace(oldText, newText)
        })
        textFound = true
      }
    }
    
    // If still no specific match, fall back to the first occurrence only
    if (!textFound) {
      const firstOccurrence = fileContent.indexOf(oldText)
      if (firstOccurrence !== -1) {
        updatedContent = fileContent.substring(0, firstOccurrence) + 
                        newText + 
                        fileContent.substring(firstOccurrence + oldText.length)
        textFound = true
      }
    }

    if (!textFound) {
      return NextResponse.json(
        {
          error: 'Text not found in source file',
          details: {
            searchedText: oldText,
            filePath: foundFile
          }
        },
        { status: 404 }
      )
    }

    // Write the updated content back to the file
    fs.writeFileSync(fullPath, updatedContent, 'utf-8')

    // Log the text edit
    await prisma.auditlogs.create({
      data: {
        action: 'SOURCE_FILE_EDIT',
        resource: 'SOURCE_CODE',
        resourceid: foundFile,
        userid: BigInt(session.user.id),
        details: JSON.stringify({
          pagePath,
          filePath: foundFile,
          tagName,
          className,
          elementId: id,
          oldText: oldText.substring(0, 200), // Truncate for logging
          newText: newText.substring(0, 200), // Truncate for logging
          elementPath,
          selector
        }),
        ipaddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        useragent: request.headers.get('user-agent') || 'unknown'
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Source file updated successfully',
      filePath: foundFile,
      oldText: oldText,
      newText: newText
    })

  } catch (error) {
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development'
          ? (error instanceof Error ? error.message : 'Unknown error')
          : 'An error occurred while processing your request'
      },
      { status: 500 }
    )
  }
}

// Helper function to escape special characters in a regular expression
function escapeRegExp(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
} 