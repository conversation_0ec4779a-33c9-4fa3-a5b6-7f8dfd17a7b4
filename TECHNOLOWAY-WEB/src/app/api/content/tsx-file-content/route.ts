import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import fs from 'fs'
import path from 'path'

// GET /api/content/tsx-file-content - Get content of a specific TSX file
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const filePath = searchParams.get('filePath')
    
    if (!filePath) {
      return NextResponse.json(
        { error: 'Missing filePath parameter' },
        { status: 400 }
      )
    }

    const projectRoot = process.cwd()

    // Clean the file path to remove double slashes and handle src prefix correctly
    const cleanFilePath = filePath.replace(/\/+/g, '/') // Remove double slashes
    const fullPath = cleanFilePath.startsWith('src/')
      ? path.join(projectRoot, cleanFilePath)
      : path.join(projectRoot, 'src', cleanFilePath)

    // Security check: ensure the file is within the src directory
    const normalizedPath = path.normalize(fullPath)
    const srcDir = path.join(projectRoot, 'src')
    
    if (!normalizedPath.startsWith(srcDir)) {
      return NextResponse.json(
        { error: 'Invalid file path' },
        { status: 400 }
      )
    }

    // Check if file exists and is a .tsx file
    if (!fs.existsSync(normalizedPath) || !filePath.endsWith('.tsx')) {
      return NextResponse.json(
        { error: 'File not found or not a TSX file' },
        { status: 404 }
      )
    }

    const content = await fs.promises.readFile(normalizedPath, 'utf-8')
    const stats = await fs.promises.stat(normalizedPath)
    
    return NextResponse.json({
      success: true,
      content,
      lastModified: stats.mtime.toISOString(),
      filePath
    })
  } catch (error) {
    console.error('Error reading TSX file:', error)
    return NextResponse.json(
      { error: 'Failed to read TSX file' },
      { status: 500 }
    )
  }
}
