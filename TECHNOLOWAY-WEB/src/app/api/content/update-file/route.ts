import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/services/auth/auth-config';
import * as fs from 'fs/promises';
import * as path from 'path';

// Helper function to escape special regex characters
function escapeRegex(text: string): string {
  return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Helper function to calculate text similarity
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

// Helper function to calculate Levenshtein distance
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { filePath, oldText, newText, elementInfo, context } = await request.json();

    if (!filePath || !oldText || !newText) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('Update request:', {
      filePath,
      oldText: `"${oldText}"`,
      newText: `"${newText}"`,
      elementInfo,
      context: context ? {
        componentType: context.componentType,
        likelyFile: context.likelyFile,
        isInComponent: context.isInComponent
      } : null
    });

    // Enhanced search paths based on context analysis and content type
    let searchPaths = [filePath];

    // Use context analysis if available for more precise file detection
    if (context && context.likelyFile) {
      // Start with the context-detected file, then add fallbacks
      searchPaths = [context.likelyFile];

      // Add component-specific fallbacks based on context
      if (context.componentType) {
        const componentFiles = {
          'hero': ['src/components/home/<USER>', 'src/app/page.tsx'],
          'services': ['src/components/home/<USER>', 'src/app/page.tsx'],
          'about': ['src/components/home/<USER>', 'src/app/about/page.tsx'],
          'contact': ['src/components/home/<USER>', 'src/app/contact/page.tsx'],
          'testimonials': ['src/components/home/<USER>', 'src/app/page.tsx'],
          'blog': ['src/components/home/<USER>', 'src/app/blog/page.tsx'],
          'clients': ['src/components/home/<USER>', 'src/app/page.tsx'],
          'projects': ['src/components/home/<USER>', 'src/app/page.tsx']
        };

        const additionalFiles = componentFiles[context.componentType as keyof typeof componentFiles] || [];
        searchPaths = [...searchPaths, ...additionalFiles.filter(f => f !== context.likelyFile)];
      }

      // Always include the original filePath as fallback
      if (!searchPaths.includes(filePath)) {
        searchPaths.push(filePath);
      }

      console.log('Using context-based file detection:', {
        primaryFile: context.likelyFile,
        componentType: context.componentType,
        allSearchPaths: searchPaths
      });
    }

    // Determine search paths based on element type and content - more comprehensive
    if (elementInfo?.type === 'getContent' || elementInfo?.type === 'text' || elementInfo?.type === 'description' || elementInfo?.type === 'title') {
      // For all text content, search in ALL relevant files
      searchPaths = [
        filePath,
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/app/page.tsx',
        'src/app/about/page.tsx',
        'src/app/services/page.tsx',
        'src/app/contact/page.tsx',
        'src/app/blog/page.tsx',
        'src/app/portfolio/page.tsx',
        'src/app/team/page.tsx'
      ];
    } else if (elementInfo?.type === 'stats') {
      searchPaths = [
        filePath,
        'src/app/page.tsx',
        'src/components/home/<USER>',
        'src/components/home/<USER>'
      ];
    } else if (elementInfo?.type === 'heading') {
      // Headings are usually in main page files or section components
      searchPaths = [
        filePath,
        'src/app/page.tsx',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>'
      ];
    } else if (elementInfo?.type === 'button') {
      // Buttons can be in various components
      searchPaths = [
        filePath,
        'src/components/ui/button.tsx',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/app/page.tsx'
      ];
    } else {
      // For all other types, search in all relevant files
      searchPaths = [
        filePath,
        'src/app/page.tsx',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>',
        'src/components/home/<USER>'
      ];
    }
    
    // Try to find and update the text in each search path
    let updated = false;
    let searchAttempts = [];
    let actualFilePath = filePath;
    let actualLineNumber = 0; // Track the actual line number where text was found

    console.log('=== TEXT SEARCH STARTING ===');
    console.log('Target text:', `"${oldText}"`);
    console.log('Replacement text:', `"${newText}"`);
    console.log('Search paths:', searchPaths);
    console.log('Context info:', context ? {
      componentType: context.componentType,
      likelyFile: context.likelyFile,
      isInComponent: context.isInComponent
    } : 'No context available');
    
    for (const searchPath of searchPaths) {
      try {
        const fullPath = path.join(process.cwd(), searchPath);
        let fileContent = await fs.readFile(fullPath, 'utf-8');
        console.log(`\n--- Searching in file: ${searchPath} ---`);
        console.log(`File size: ${fileContent.length} characters`);

        // Quick check if the text exists anywhere in the file
        const textExists = fileContent.includes(oldText);
        const textExistsLower = fileContent.toLowerCase().includes(oldText.toLowerCase());
        console.log(`Text exists (exact): ${textExists}`);
        console.log(`Text exists (case-insensitive): ${textExistsLower}`);
        
        // Handle different types of content updates
        if (elementInfo?.type === 'getContent') {
                  // Handle getContent calls
        const getContentRegex = /getContent\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*\)/g;
        fileContent = fileContent.replace(getContentRegex, (match, page, section, key, fallback) => {
          searchAttempts.push(`getContent('${page}', '${section}', '${key}', '${fallback}')`);
          if (fallback === oldText) {
            updated = true;
            actualFilePath = searchPath;
            // Find line number for this match
            const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
            actualLineNumber = beforeMatch.split('\n').length;
            return `getContent('${page}', '${section}', '${key}', '${newText}')`;
          }
          return match;
        });
        
        // Also handle getContent calls with escaped quotes (like in projects-section.tsx)
        if (!updated) {
          const getContentEscapedRegex = /getContent\s*\(\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*,\s*['"`]([^'"`]+)['"`]\s*\)/g;
          fileContent = fileContent.replace(getContentEscapedRegex, (match, page, section, key, fallback) => {
            // Handle escaped quotes in the fallback text
            const unescapedFallback = fallback.replace(/\\'/g, "'").replace(/\\"/g, '"').replace(/\\`/g, '`');
            searchAttempts.push(`getContent('${page}', '${section}', '${key}', '${unescapedFallback}')`);
            if (unescapedFallback === oldText) {
              updated = true;
              actualFilePath = searchPath;
              // Find line number for this match
              const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
              actualLineNumber = beforeMatch.split('\n').length;
              // Preserve the original escaping
              const escapedNewText = newText.replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/`/g, '\\`');
              return `getContent('${page}', '${section}', '${key}', '${escapedNewText}')`;
            }
            return match;
          });
        }
        } else if (elementInfo?.type === 'stats') {
          // Handle stats array items - support both formats
          // Format 1: { name: '...', value: '...' }
          let statsRegex = /\{\s*name\s*:\s*['"`]([^'"`]+)['"`]\s*,\s*value\s*:\s*['"`]([^'"`]+)['"`]\s*\}/g;
          fileContent = fileContent.replace(statsRegex, (match, name, value) => {
            searchAttempts.push(`{ name: '${name}', value: '${value}' }`);
            if (value === oldText) {
              updated = true;
              actualFilePath = searchPath;
              // Find line number for this match
              const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
              actualLineNumber = beforeMatch.split('\n').length;
              return `{ name: '${name}', value: '${newText}' }`;
            }
            return match;
          });
          
          // Format 2: { name: '...', value: '...', iconName: '...' }
          if (!updated) {
            statsRegex = /\{\s*name\s*:\s*['"`]([^'"`]+)['"`]\s*,\s*value\s*:\s*['"`]([^'"`]+)['"`]\s*,\s*iconName\s*:\s*['"`]([^'"`]+)['"`]\s*\}/g;
            fileContent = fileContent.replace(statsRegex, (match, name, value, iconName) => {
              searchAttempts.push(`{ name: '${name}', value: '${value}', iconName: '${iconName}' }`);
                          if (value === oldText) {
              updated = true;
              actualFilePath = searchPath;
              // Find line number for this match
              const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
              actualLineNumber = beforeMatch.split('\n').length;
              return `{ name: '${name}', value: '${newText}', iconName: '${iconName}' }`;
            }
              return match;
            });
          }
        } else if (elementInfo?.type === 'const') {
          // Handle const declarations
          const constRegex = /const\s+(\w+)\s*=\s*['"`]([^'"`]+)['"`]/g;
          fileContent = fileContent.replace(constRegex, (match, varName, value) => {
            searchAttempts.push(`const ${varName} = '${value}'`);
            if (value === oldText) {
              updated = true;
              actualFilePath = searchPath;
              // Find line number for this match
              const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
              actualLineNumber = beforeMatch.split('\n').length;
              return `const ${varName} = '${newText}'`;
            }
            return match;
          });
        } else if (elementInfo?.type === 'heading') {
          // Handle heading elements with specific patterns
          const headingLevel = elementInfo.level || 'h1';
          const headingPatterns = [
            new RegExp(`(<${headingLevel}[^>]*>)\\s*${escapeRegex(oldText)}\\s*(</${headingLevel}>)`, 'g'),
            new RegExp(`(title\\s*[:=]\\s*)['"\`]${escapeRegex(oldText)}['"\`]`, 'g'),
            new RegExp(`(heading\\s*[:=]\\s*)['"\`]${escapeRegex(oldText)}['"\`]`, 'g')
          ];

          for (const pattern of headingPatterns) {
            if (pattern.test(fileContent)) {
              fileContent = fileContent.replace(pattern, (match, prefix, suffix) => {
                updated = true;
                actualFilePath = searchPath;
                // Find line number for this match
                const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
                actualLineNumber = beforeMatch.split('\n').length;
                searchAttempts.push(`Heading pattern: ${match}`);
                if (suffix) {
                  return `${prefix}${newText}${suffix}`;
                } else {
                  return match.replace(oldText, newText);
                }
              });
              break;
            }
          }
        } else if (elementInfo?.type === 'button') {
          // Handle button text patterns
          const buttonPatterns = [
            new RegExp(`(<button[^>]*>)\\s*${escapeRegex(oldText)}\\s*(</button>)`, 'g'),
            new RegExp(`(buttonText\\s*[:=]\\s*)['"\`]${escapeRegex(oldText)}['"\`]`, 'g'),
            new RegExp(`(label\\s*[:=]\\s*)['"\`]${escapeRegex(oldText)}['"\`]`, 'g')
          ];

          for (const pattern of buttonPatterns) {
            if (pattern.test(fileContent)) {
              fileContent = fileContent.replace(pattern, (match, prefix, suffix) => {
                updated = true;
                actualFilePath = searchPath;
                // Find line number for this match
                const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
                actualLineNumber = beforeMatch.split('\n').length;
                searchAttempts.push(`Button pattern: ${match}`);
                if (suffix) {
                  return `${prefix}${newText}${suffix}`;
                } else {
                  return match.replace(oldText, newText);
                }
              });
              break;
            }
          }
            } else {
      // Enhanced text replacement with better pattern matching
      const lines = fileContent.split('\n');
      let lineUpdated = false;
      const trimmedOldText = oldText.trim();
      const trimmedNewText = newText.trim();

      console.log(`Searching for text: "${trimmedOldText}" in ${searchPath}`);

      // First, try to find the text in getContent calls (most common case)
      const getContentPattern = new RegExp(`getContent\\([^)]*['"\`]${escapeRegex(trimmedOldText)}['"\`][^)]*\\)`, 'g');
      if (getContentPattern.test(fileContent)) {
        fileContent = fileContent.replace(getContentPattern, (match) => {
          // Find the fallback parameter and replace it
          const fallbackMatch = match.match(/['"\`]([^'"\`]+)['"\`]\s*\)$/);
          if (fallbackMatch && fallbackMatch[1] === trimmedOldText) {
            updated = true;
            actualFilePath = searchPath;
            // Find line number for this match
            const beforeMatch = fileContent.substring(0, fileContent.indexOf(match));
            actualLineNumber = beforeMatch.split('\n').length;
            searchAttempts.push(`getContent fallback: ${match}`);
            return match.replace(fallbackMatch[1], trimmedNewText);
          }
          return match;
        });
      }

      // Try multiple matching strategies with priority order
      for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const trimmedLine = line.trim();
            let updatedLine = line;

            // Skip empty lines and comments
            if (!trimmedLine || trimmedLine.startsWith('//') || trimmedLine.startsWith('/*')) {
              continue;
            }

            // Strategy 1: Exact line match (highest priority)
            if (trimmedLine === trimmedOldText) {
              updatedLine = line.replace(trimmedOldText, trimmedNewText);
              console.log(`Exact line match found at line ${i + 1}`);
            }

            // Strategy 2: Exact quoted string match
            if (updatedLine === line) {
              const quotedPatterns = [
                `'${trimmedOldText}'`,
                `"${trimmedOldText}"`,
                `\`${trimmedOldText}\``
              ];

              for (const pattern of quotedPatterns) {
                if (trimmedLine.includes(pattern)) {
                  const replacement = pattern.replace(trimmedOldText, trimmedNewText);
                  updatedLine = line.replace(pattern, replacement);
                  console.log(`Quoted string match found at line ${i + 1}: ${pattern}`);
                  break;
                }
              }
            }

            // Strategy 3: JSX text content (more precise)
            if (updatedLine === line) {
              // Match JSX text content like <h1>Text</h1> or <p>Text</p>
              const jsxPatterns = [
                new RegExp(`(>\\s*)${escapeRegex(trimmedOldText)}(\\s*<)`, 'g'),
                new RegExp(`(>)${escapeRegex(trimmedOldText)}(<)`, 'g'),
                new RegExp(`(>\\s*)${escapeRegex(trimmedOldText)}(\\s*$)`, 'g') // End of line
              ];

              for (const jsxPattern of jsxPatterns) {
                if (jsxPattern.test(trimmedLine)) {
                  updatedLine = line.replace(jsxPattern, `$1${trimmedNewText}$2`);
                  console.log(`JSX content match found at line ${i + 1}`);
                  break;
                }
              }
            }

            // Strategy 4: Object property values
            if (updatedLine === line) {
              // Match object properties like title: "Text" or name: 'Text'
              const propPatterns = [
                new RegExp(`(\\w+\\s*:\\s*)['"\`]${escapeRegex(trimmedOldText)}['"\`]`, 'g'),
                new RegExp(`(['"\`]\\w+['"\`]\\s*:\\s*)['"\`]${escapeRegex(trimmedOldText)}['"\`]`, 'g')
              ];

              for (const pattern of propPatterns) {
                if (pattern.test(trimmedLine)) {
                  updatedLine = line.replace(pattern, (match, prefix) => {
                    const quote = match.includes("'") ? "'" : match.includes('"') ? '"' : '`';
                    return `${prefix}${quote}${trimmedNewText}${quote}`;
                  });
                  console.log(`Object property match found at line ${i + 1}`);
                  break;
                }
              }
            }

            // Strategy 5: Template literal content
            if (updatedLine === line) {
              // Match template literals with ${} expressions
              const templatePattern = new RegExp(`(\\\${[^}]*}).*?${escapeRegex(trimmedOldText)}`, 'g');
              if (templatePattern.test(trimmedLine)) {
                updatedLine = line.replace(trimmedOldText, trimmedNewText);
                console.log(`Template literal match found at line ${i + 1}`);
              }
            }

            // Strategy 6: Partial text match within line
            if (updatedLine === line && trimmedLine.includes(trimmedOldText)) {
              updatedLine = line.replace(trimmedOldText, trimmedNewText);
              console.log(`Partial text match found at line ${i + 1}`);
            }

            // Strategy 7: Word boundary match
            if (updatedLine === line) {
              const wordBoundaryRegex = new RegExp(`\\b${escapeRegex(trimmedOldText)}\\b`, 'g');
              if (wordBoundaryRegex.test(trimmedLine)) {
                updatedLine = line.replace(wordBoundaryRegex, trimmedNewText);
                console.log(`Word boundary match found at line ${i + 1}`);
              }
            }

            // Strategy 8: Case-insensitive match (last resort)
            if (updatedLine === line && trimmedLine.toLowerCase().includes(trimmedOldText.toLowerCase())) {
              const regex = new RegExp(escapeRegex(trimmedOldText), 'gi');
              updatedLine = line.replace(regex, trimmedNewText);
              console.log(`Case-insensitive match found at line ${i + 1}`);
            }

            // Strategy 9: Fuzzy match for similar text (very last resort)
            if (updatedLine === line) {
              const similarity = calculateSimilarity(trimmedLine, trimmedOldText);
              if (similarity > 0.8) {
                // Try to find the most similar part and replace it
                const words = trimmedOldText.split(' ');
                const lineWords = trimmedLine.split(' ');
                
                for (let j = 0; j <= lineWords.length - words.length; j++) {
                  const segment = lineWords.slice(j, j + words.length).join(' ');
                  const segmentSimilarity = calculateSimilarity(segment, trimmedOldText);
                  
                  if (segmentSimilarity > 0.9) {
                    updatedLine = line.replace(segment, trimmedNewText);
                    console.log(`Fuzzy match found at line ${i + 1}: "${segment}" -> "${trimmedNewText}"`);
                    break;
                  }
                }
              }
            }

            // If we made a change, update and break
            if (updatedLine !== line) {
              lines[i] = updatedLine;
              lineUpdated = true;
              updated = true;
              actualFilePath = searchPath;
              actualLineNumber = i + 1;
              searchAttempts.push(`Line ${i + 1}: ${line.trim()} -> ${updatedLine.trim()}`);
              break;
            }
          }

          if (lineUpdated) {
            fileContent = lines.join('\n');
          } else {
            // Enhanced debugging - find similar text
            lines.forEach((line, index) => {
              const similarity = calculateSimilarity(line.trim(), trimmedOldText);
              if (similarity > 0.7 || line.includes(oldText.substring(0, Math.min(15, oldText.length)))) {
                searchAttempts.push(`Line ${index + 1} (${Math.round(similarity * 100)}% match): ${line.trim()}`);
              }
            });
          }
        }
        
        // If we found and updated the text, write the file and break
        if (updated) {
          console.log(`✅ SUCCESS: Text found and updated in: ${searchPath}`);
          console.log(`=== SEARCH COMPLETED SUCCESSFULLY ===`);
          try {
            await fs.writeFile(fullPath, fileContent, 'utf-8');
            break;
          } catch (writeError) {
            console.error(`Failed to write file ${searchPath}:`, writeError);
            // If it's a file conflict error, try to provide helpful message
            if (writeError instanceof Error && writeError.message.includes('newer')) {
              return NextResponse.json({
                error: 'File is being edited in another application',
                details: {
                  filePath: searchPath,
                  suggestion: 'Please close the file in your editor and try again, or save your changes first.'
                }
              }, { status: 409 });
            }
            throw writeError;
          }
        } else {
          console.log(`❌ No match found in: ${searchPath}`);
        }
      } catch (error) {
        // File doesn't exist or can't be read, continue to next path
        console.log(`Could not read file: ${searchPath}`);
        continue;
      }
    }
    
    if (!updated) {
      console.log('\n=== SEARCH FAILED ===');
      console.log('❌ Text not found in any of the searched files');
      console.log('Searched files:', searchPaths);
      console.log('Search attempts:', searchAttempts.slice(0, 10));
      console.log('Target text:', `"${oldText}"`);
      console.log('Text length:', oldText.length);
      console.log('=== END SEARCH SUMMARY ===\n');

      return NextResponse.json({
        error: 'Text not found in file',
        details: {
          searchedText: oldText,
          filePath: actualFilePath,
          elementType: elementInfo?.type || 'general',
          searchAttempts: searchAttempts.slice(0, 5), // Show first 5 attempts
          searchedFiles: searchPaths,
          suggestions: [
            'The text might be generated dynamically',
            'The text might be in a different file',
            'The text might have different formatting',
            'Try selecting a different part of the text',
            'Check if the text is in a component file',
            'The text might be in a different page file'
          ]
        }
      }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'File updated successfully',
      updated: true,
      actualFilePath: actualFilePath,
      actualLineNumber: actualLineNumber
    });

  } catch (error) {
    console.error('Error updating file:', error);
    return NextResponse.json({ 
      error: 'Failed to update file',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 