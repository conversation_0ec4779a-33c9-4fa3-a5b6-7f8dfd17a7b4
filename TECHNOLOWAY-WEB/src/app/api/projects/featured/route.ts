import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse
} from '@/services/api/api-utils'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'

// GET /api/projects/featured - Get featured projects for landing page
export const GET = with<PERSON>rror<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const limit = parseInt(searchParams.get('limit') || '6')

  const projects = await prisma.projectss.findMany({
    where: {
      ispublic: true,
      isfeatured: true,
      status: 'COMPLETED',
    },
    take: limit,
    orderBy: [
      { displayorder: 'asc' },
      { createdat: 'desc' }
    ],
    include: {
      client: {
        select: {
          id: true,
          companyname: true,
        },
      },
      services: {
        select: {
          id: true,
          name: true,
          description: true,
        },
      },
      technologies: {
        select: {
          id: true,
          name: true,
          iconurl: true,
        },
      },
      feedbacks: {
        where: {
          ispublic: true,
        },
        select: {
          id: true,
          rating: true,
        },
      },
      _count: {
        select: {
          feedbacks: {
            where: {
              ispublic: true,
            },
          },
        },
      },
    },
  })

  // Add computed fields
  const enrichedProjects = projects.map(project => ({
    ...project,
    averageRating: project.feedbacks.length > 0 
      ? project.feedbacks.reduce((sum, feedback) => sum + feedback.rating, 0) / project.feedbacks.length
      : null,
    tagsArray: project.tags ? project.tags.split(',').map(tag => tag.trim()) : [],
  }))

  return successResponse(enrichedProjects)
})
