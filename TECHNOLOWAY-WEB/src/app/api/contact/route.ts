import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  withError<PERSON>and<PERSON>,
  successResponse,
  paginatedResponse,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  validateMethod,
  requireAdmin
} from '@/services/api/api-utils'
import { createContactFormSchema, updateContactFormSchema } from '@/lib/utils/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/utils/data-transform'
import { RateLimiter } from '@/services/api/rate-limit'
import {
  sanitizeContactFormData,
  validateContactFormSecurity,
  addSecurityHeaders,
  getClientIP,
  logSecurityEvent
} from '@/services/auth/security'

// Contact form rate limiter - 3 submissions per 15 minutes
const contactFormRateLimiter = new RateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 3, // 3 submissions per 15 minutes
  skipSuccessfulRequests: false
})

// GET /api/contact - List all contact form submissions with pagination and search (admin only)
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  
  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build where clause
  const where: any = {}
  
  // Add search functionality
  if (search) {
    Object.assign(where, buildSearchQuery(search, [
      'name', 
      'email', 
      'company', 
      'subject', 
      'message'
    ]))
  }
  
  // Add filter for contact status
  if (filter && ['NEW', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'].includes(filter)) {
    where.status = filter
  }

  // Get total count for pagination
  const total = await prisma.contactforms.count({ where })

  // Get contact forms with pagination
  const contactForms = await prisma.contactforms.findMany({
    where,
    orderBy: buildSortQuery(sortBy, sortOrder),
    skip,
    take,
  })

  return paginatedResponse(contactForms, page, limit, total)
})

// POST /api/contact - Submit a new contact form
export const POST = withErrorHandler(async (request: NextRequest) => {
  validateMethod(request, ['POST'])

  // Apply rate limiting
  const rateLimitResult = await contactFormRateLimiter.isAllowed(request)
  if (!rateLimitResult.allowed) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Too many contact form submissions. Please try again later.',
        retryAfter: rateLimitResult.retryAfter
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': rateLimitResult.retryAfter?.toString() || '900',
          'X-RateLimit-Limit': '3',
          'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
          'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString()
        }
      }
    )
  }

  // Get raw body for security validation before schema validation
  const requestBody = await request.json()

  // Check for honeypot fields before schema validation
  if (requestBody.website || requestBody.url || requestBody.link) {
    logSecurityEvent({
      type: 'BOT_DETECTED',
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined,
      details: { honeypotFields: Object.keys(requestBody).filter(k => ['website', 'url', 'link'].includes(k)) },
      severity: 'medium'
    })

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Invalid form submission detected'
      }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }

  const rawData = createContactFormSchema.parse(requestBody)

  // Security validation
  const securityCheck = validateContactFormSecurity(rawData)
  if (!securityCheck.valid) {
    // Log security event
    logSecurityEvent({
      type: 'SUSPICIOUS_CONTACT_FORM',
      ip: getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined,
      details: { reason: securityCheck.reason, data: rawData },
      severity: 'medium'
    })

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Invalid form submission detected'
      }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }

  // Sanitize the data
  const data = sanitizeContactFormData(rawData)

  // Create contact form in database
  const contactForm = await prisma.contactforms.create({
    data: {
      name: data.name,
      email: data.email,
      phone: data.phone || null,
      subject: data.subject,
      message: data.message,
      isread: false,
      status: 'New',
    },
  })

  // Send email notifications
  let emailSuccess = false
  try {
    const { sendContactFormNotification } = await import('@/lib/email')
    emailSuccess = await sendContactFormNotification({
      name: data.name,
      email: data.email,
      phone: data.phone,
      subject: data.subject,
      message: data.message,
    })
  } catch (emailError) {
    console.error('Failed to send email notification:', emailError)
    // Don't fail the request if email fails, but log it
  }

  // Record the successful attempt for rate limiting
  await contactFormRateLimiter.recordAttempt(request, true)

  const response = successResponse(contactForm, 'Contact form submitted successfully', 201)
  return addSecurityHeaders(response)
})

// PUT /api/contact - Bulk update contact forms (admin only)
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['PUT'])
  
  const body = await request.json()
  const { ids, data } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid contact form IDs provided')
  }

  const validate = validateRequest(updateContactFormSchema)
  const updateData = await validate({ json: () => data } as NextRequest)

  const updatedForms = await prisma.contactforms.updateMany({
    where: {
      id: {
        in: ids,
      },
    },
    data: updateData,
  })

  return successResponse(
    { count: updatedForms.count },
    `${updatedForms.count} contact forms updated successfully`
  )
})

// DELETE /api/contact - Bulk delete contact forms (admin only)
export const DELETE = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)
  validateMethod(request, ['DELETE'])
  
  const body = await request.json()
  const { ids } = body

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('Invalid contact form IDs provided')
  }

  const deletedForms = await prisma.contactforms.deleteMany({
    where: {
      id: {
        in: ids,
      },
    },
  })

  return successResponse(
    { count: deletedForms.count },
    `${deletedForms.count} contact forms deleted successfully`
  )
})
