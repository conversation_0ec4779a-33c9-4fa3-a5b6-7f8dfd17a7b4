import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Utility function to convert BigInt fields to strings for JSON serialization
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) return obj
  if (typeof obj === 'bigint') return obj.toString()
  if (Array.isArray(obj)) return obj.map(serializeBigInt)
  if (typeof obj === 'object') {
    const serialized: any = {}
    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value)
    }
    return serialized
  }
  return obj
}

export async function POST(request: NextRequest) {
  // Fixed BigInt serialization issue
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      invoiceId,
      amount,
      paymentMethod,
      currency = 'USD',
      description,
      promoCode,
      emailReceipt = true,
      stripePaymentIntentId,
      stripeStatus,
      clientSecret
    } = body

    console.log('Creating payment record:', {
      invoiceId,
      amount,
      paymentMethod,
      currency,
      userEmail: session.user.email,
      stripePaymentIntentId,
      stripeStatus
    })

    // Validate required fields
    if (!invoiceId || !amount || !paymentMethod) {
      return NextResponse.json(
        { error: 'Missing required fields: invoiceId, amount, paymentMethod' },
        { status: 400 }
      )
    }

    // For Stripe payments, ensure payment was successful
    if (paymentMethod === 'stripe_card' && stripeStatus !== 'succeeded') {
      return NextResponse.json(
        { error: 'Stripe payment was not successful. Payment not saved.' },
        { status: 400 }
      )
    }

    // Verify the invoice exists and belongs to the user's client
    const invoice = await prisma.invoices.findFirst({
      where: {
        id: parseInt(invoiceId),
        clients: {
          users: {
            email: session.user.email
          }
        }
      },
      include: {
        clients: {
          include: {
            users: true
          }
        }
      }
    })

    if (!invoice) {
      return NextResponse.json(
        { error: 'Invoice not found or access denied' },
        { status: 404 }
      )
    }

    // Create payment record
    const payment = await prisma.payments.create({
      data: {
        amount: parseFloat(amount),
        paymentdate: new Date(),
        paymentmethod: paymentMethod,
        status: stripeStatus === 'succeeded' ? 'completed' : (paymentMethod.includes('mock') ? 'pending' : 'completed'),
        notes: description || `Payment for invoice #${invoiceId}`,
        reference: `PAY-${Date.now()}`,
        transactionid: stripePaymentIntentId || `TXN-${Date.now()}`,
        invoiceid: parseInt(invoiceId),
        currency: currency.toUpperCase(),
        promocode: promoCode || null,
        emailreceipt: emailReceipt,
        receiptemail: session.user.email,
        termsaccepted: true,
        stripepaymentintentid: stripePaymentIntentId || null,
        stripeclientsecret: clientSecret || null,
        createdat: new Date(),
        updatedat: new Date(),
      }
    })

    // Check if invoice is now fully paid
    const totalPaid = await prisma.payments.aggregate({
      where: {
        invoiceid: parseInt(invoiceId),
        status: 'completed'
      },
      _sum: {
        amount: true
      }
    })

    const totalPaidAmount = totalPaid._sum.amount || 0
    if (totalPaidAmount >= parseFloat(invoice.totalamount)) {
      await prisma.invoices.update({
        where: { id: parseInt(invoiceId) },
        data: { 
          status: 'PAID',
          paidat: new Date()
        }
      })
    }

    return NextResponse.json(serializeBigInt({
      success: true,
      payment: {
        id: payment.id,
        amount: payment.amount,
        status: payment.status,
        reference: payment.reference,
        paymentDate: payment.paymentdate
      },
      message: 'Payment recorded successfully'
    }))

  } catch (error) {
    console.error('Error creating payment:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create payment',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get payments for the user's client
    const payments = await prisma.payments.findMany({
      where: {
        invoices: {
          clients: {
            users: {
              email: session.user.email
            }
          }
        }
      },
      include: {
        invoices: {
          include: {
            projects: true
          }
        }
      },
      orderBy: {
        paymentdate: 'desc'
      }
    })

    return NextResponse.json(serializeBigInt({
      success: true,
      payments
    }))

  } catch (error) {
    console.error('Error fetching payments:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch payments',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  } finally {
    await prisma.$disconnect()
  }
}
