import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/services/auth/auth-config'
import { prisma } from '@/config/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const paymentId = parseInt(id)

    if (isNaN(paymentId)) {
      return NextResponse.json(
        { success: false, message: 'Invalid payment ID' },
        { status: 400 }
      )
    }

    // Get payment details
    const payment = await prisma.payments.findUnique({
      where: { id: paymentId },
      include: {
        invoices: {
          include: {
            clients: true
          }
        }
      }
    })

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      )
    }

    // Generate receipt PDF
    const pdfContent = generateReceiptPDF(payment)
    
    return new NextResponse(pdfContent, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="payment-receipt-${paymentId}.pdf"`
      }
    })

  } catch (error) {
    console.error('Error generating payment receipt:', error)
    return NextResponse.json(
      { success: false, message: 'Failed to generate receipt' },
      { status: 500 }
    )
  }
}

function generateReceiptPDF(payment: any): Buffer {
  // This is a placeholder implementation
  // In a real application, you would use a PDF generation library
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 250
>>
stream
BT
/F1 12 Tf
50 750 Td
(Payment Receipt #${payment.id}) Tj
0 -20 Td
(Amount: $${payment.amount}) Tj
0 -20 Td
(Payment Date: ${payment.paymentdate}) Tj
0 -20 Td
(Payment Method: ${payment.paymentmethod}) Tj
0 -20 Td
(Status: ${payment.status}) Tj
0 -20 Td
(Client: ${payment.invoices?.clients?.companyname || payment.invoices?.clients?.contactname || 'N/A'}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000576 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
673
%%EOF`

  return Buffer.from(pdfContent)
}
