import { NextRequest } from 'next/server'
import { prisma } from '@/config/prisma'
import {
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  successResponse,
  validateRequest,
  ApiError
} from '@/services/api/api-utils'
import { transformFromDbFields } from '@/lib/utils/data-transform'
import { z } from 'zod'
import { 
  validateChatAccess, 
  filterMessagesForUser,
  getAllowedRecipients 
} from '@/services/chat/chat-access-control'

interface RouteParams {
  params: { id: string }
}

// Message schema for creating new chat messages
const createMessageSchema = z.object({
  message: z.string().optional().default(''),
  contenttype: z.enum(['text', 'html', 'file']).default('text'),
  attachments: z.array(z.object({
    id: z.string(),
    filename: z.string(),
    size: z.number(),
    mimeType: z.string(),
    url: z.string(),
    path: z.string(),
  })).default([]),
  receiverid: z.number().optional(),
  messagetype: z.enum(['contact', 'reply', 'chat']).default('chat'),
}).refine(
  (data) => data.message.trim().length > 0 || data.attachments.length > 0,
  {
    message: 'Either message or attachments are required',
    path: ['message']
  }
)

// GET /api/chat/contact-forms/[id]/messages - Get all messages in a thread (client access)
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const contactFormId = parseInt(resolvedParams.id)
  if (isNaN(contactFormId)) {
    throw new ApiError('Invalid contact form ID', 400)
  }

  // Validate access with chat access control (allows both admin and client access)
  const { user } = await validateChatAccess(request, contactFormId)

  // Get the original contact form to determine thread ID
  const originalContactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) }
  })

  if (!originalContactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Determine thread ID - use the original contact form ID as thread ID if not set
  const threadId = originalContactForm.threadid || originalContactForm.id

  // Get all messages in this thread
  const messages = await prisma.contactforms.findMany({
    where: {
      OR: [
        { id: BigInt(contactFormId) }, // Include the original contact form
        { threadid: threadId }, // Include all messages in the thread
        { parentid: BigInt(contactFormId) } // Include direct replies
      ]
    },
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      parent: {
        select: {
          id: true,
          subject: true,
          message: true,
          createdat: true
        }
      }
    },
    orderBy: {
      createdat: 'asc'
    }
  })

  // Transform the messages
  const transformedMessages = messages.map(message => ({
    ...transformFromDbFields.contactForm(message),
    sender: message.sender,
    receiver: message.receiver,
    parent: message.parent ? transformFromDbFields.contactForm(message.parent) : null,
    attachments: message.attachments ? JSON.parse(message.attachments) : []
  }))

  // Filter messages based on user access rights
  const filteredMessages = filterMessagesForUser(transformedMessages, user)

  return successResponse({
    messages: filteredMessages,
    threadId: threadId.toString(),
    totalCount: filteredMessages.length
  })
})

// POST /api/chat/contact-forms/[id]/messages - Create a new chat message (client access)
export const POST = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  const resolvedParams = await params
  const contactFormId = parseInt(resolvedParams.id)
  if (isNaN(contactFormId)) {
    throw new ApiError('Invalid contact form ID', 400)
  }

  // Validate the message data
  const validate = validateRequest(createMessageSchema)
  const data = await validate(request)

  // Validate access with chat access control
  const { user } = await validateChatAccess(
    request, 
    contactFormId, 
    data.receiverid
  )

  // Get the original contact form to determine thread and receiver
  const originalContactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) }
  })

  if (!originalContactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Determine thread ID - use the original contact form ID as thread ID if not set
  const threadId = originalContactForm.threadid || originalContactForm.id

  // Determine receiver - if not specified, use appropriate default based on user role
  let receiverId = data.receiverid
  if (!receiverId) {
    if (user.role === 'ADMIN') {
      // Admin replying - send to original contact form sender
      if (originalContactForm.senderid) {
        receiverId = Number(originalContactForm.senderid)
      }
    } else {
      // Client/User messaging - send to any admin (get first admin)
      const firstAdmin = await prisma.users.findFirst({
        where: { role: 'ADMIN' },
        select: { id: true }
      })
      if (firstAdmin) {
        receiverId = Number(firstAdmin.id)
      }
    }
  }

  // Create the new message
  const newMessage = await prisma.contactforms.create({
    data: {
      name: user.firstname && user.lastname ? `${user.firstname} ${user.lastname}` : user.email,
      email: user.email,
      subject: `Re: ${originalContactForm.subject}`,
      message: data.message,
      messagetype: data.messagetype,
      contenttype: data.contenttype,
      attachments: data.attachments.length > 0 ? JSON.stringify(data.attachments) : null,
      threadid: threadId,
      parentid: BigInt(contactFormId),
      senderid: BigInt(user.id),
      receiverid: receiverId ? BigInt(receiverId) : null,
      isread: false,
      isdelivered: true,
      deliveredat: new Date(),
      status: 'New'
    },
    include: {
      sender: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      receiver: {
        select: {
          id: true,
          email: true,
          firstname: true,
          lastname: true,
          imageurl: true,
          role: true
        }
      },
      parent: {
        select: {
          id: true,
          subject: true,
          message: true,
          createdat: true
        }
      }
    }
  })

  // Update the original contact form's thread ID if not set
  if (!originalContactForm.threadid) {
    await prisma.contactforms.update({
      where: { id: BigInt(contactFormId) },
      data: { threadid: threadId }
    })
  }

  // Transform the response
  const transformedMessage = {
    ...transformFromDbFields.contactForm(newMessage),
    sender: newMessage.sender,
    receiver: newMessage.receiver,
    parent: newMessage.parent ? transformFromDbFields.contactForm(newMessage.parent) : null,
    attachments: newMessage.attachments ? JSON.parse(newMessage.attachments) : []
  }

  return successResponse({
    message: transformedMessage,
    threadId: threadId.toString()
  })
})
