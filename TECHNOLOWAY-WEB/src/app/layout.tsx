import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AuthProvider from "@/components/providers/session-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { HydrationProvider } from "@/components/providers/hydration-provider";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "Technoloway - Software Development Company",
    template: "%s | Technoloway",
  },
  description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
  keywords: ["software development", "web development", "mobile apps", "enterprise solutions", "TypeScript", "React", "Next.js"],
  authors: [{ name: "Technoloway Team" }],
  creator: "Technoloway",
  publisher: "Technoloway",
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "https://technoloway.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "/",
    title: "Technoloway - Software Development Company",
    description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
    siteName: "Technoloway",
  },
  twitter: {
    card: "summary_large_image",
    title: "Technoloway - Software Development Company",
    description: "Leading software development company specializing in modern web applications, mobile apps, and enterprise solutions.",
    creator: "@technoloway",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent browser extension hydration issues
              if (typeof window !== 'undefined') {
                // Override console.error to suppress hydration warnings from browser extensions
                const originalError = console.error;
                console.error = function(...args) {
                  const message = args[0];
                  if (typeof message === 'string' &&
                      (message.includes('hydrated but some attributes') ||
                       message.includes('abId') ||
                       message.includes('browser extension') ||
                       message.includes('NewsletterForm') ||
                       message.includes('FormWrapper') ||
                       message.includes('max-w-md mx-auto'))) {
                    return; // Suppress these specific errors
                  }
                  originalError.apply(console, args);
                };
              }
            `,
          }}
        />
      </head>
      <body className={`${inter.className} antialiased`} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <HydrationProvider>
              {children}
            </HydrationProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
