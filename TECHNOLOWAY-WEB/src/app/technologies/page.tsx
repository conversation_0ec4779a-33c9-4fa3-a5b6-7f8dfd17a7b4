'use client';

import { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  CodeBracketIcon,
  ArrowTopRightOnSquareIcon,
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { TechnologyCard } from '@/components/technologies/technology-card';
import { TechnologyFiltersComponent } from '@/components/technologies/technology-filters';
import { ErrorBoundary } from '@/components/error-boundary';
import { useStaticContent } from '@/lib/hooks/use-static-content';
import { useTechnologies } from '@/hooks/use-technologies';
import { categories, proficiencyLevels, technologyStats } from '@/data/technologies';
import { TechnologyFilters } from '@/types/shared/technology';

// Utility functions
const getProficiencyColor = (level: string): string => {
  switch (level) {
    case 'Expert': return 'bg-green-100 text-green-800';
    case 'Advanced': return 'bg-blue-100 text-blue-800';
    case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
    case 'Beginner': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function TechnologiesPage() {
  const { getContent } = useStaticContent();
  const [filters, setFilters] = useState<TechnologyFilters>({
    searchTerm: '',
    selectedCategory: 'All',
    selectedProficiency: 'All'
  });

  const {
    filteredTechnologies,
    featuredTechnologies
  } = useTechnologies(filters);

  // Memoized callback for filter changes
  const handleFiltersChange = useCallback((newFilters: TechnologyFilters) => {
    setFilters(newFilters);
  }, []);

    return (
    <ErrorBoundary>
      <div className="min-h-screen bg-white">
        <Header />

        <main className="pt-20">
          {/* Hero Section */}
          <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center max-w-4xl mx-auto"
              >
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
                >
                  {getContent('technologies', 'hero', 'title', 'Our')} <span className="gradient-text">{getContent('technologies', 'hero', 'title_highlight', 'Technologies')}</span>
                </motion.h1>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
                >
                  {getContent('technologies', 'hero', 'subtitle', 'We leverage cutting-edge technologies and frameworks to build robust, scalable, and future-proof solutions for our clients.')}
                </motion.p>
              </motion.div>
            </div>
          </section>

          {/* Stats Section */}
          <section className="py-16 bg-white" aria-label="Technology statistics">
            <div className="container">
              <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
                {technologyStats.map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center"
                  >
                    <div className="text-3xl font-bold text-blue-600 sm:text-4xl">
                      {stat.value}
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Filters Section */}
          <TechnologyFiltersComponent
            filters={filters}
            onFiltersChange={handleFiltersChange}
            categories={categories}
            proficiencyLevels={proficiencyLevels}
          />

          {/* Technologies Grid */}
          <section className="py-16 bg-white" aria-label="Technology stack">
            <div className="container">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredTechnologies.map((tech, index) => (
                  <TechnologyCard
                    key={tech.id}
                    technology={tech}
                    index={index}
                    getProficiencyColor={getProficiencyColor}
                  />
                ))}
              </div>

              {/* Empty State */}
              {filteredTechnologies.length === 0 && (
                <div className="text-center py-12" role="status" aria-live="polite">
                  <div className="text-gray-400 mb-4">
                    <CodeBracketIcon className="mx-auto h-12 w-12" aria-hidden="true" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No technologies found</h3>
                  <p className="text-gray-600">
                    Try adjusting your search criteria or filters.
                  </p>
                </div>
              )}
            </div>
          </section>

          {/* Featured Technologies */}
          <section className="py-16 bg-gray-50" aria-label="Featured technologies">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {getContent('technologies', 'categories', 'title', 'Technology')} <span className="gradient-text">{getContent('technologies', 'categories', 'title_highlight', 'Stack')}</span>
                </h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  {getContent('technologies', 'categories', 'subtitle', 'Comprehensive expertise across the full technology spectrum')}
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {featuredTechnologies.map((tech, index) => (
                  <motion.div
                    key={tech.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="text-center group"
                  >
                    <div className="flex items-center justify-center w-20 h-20 bg-white rounded-2xl mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow">
                      <img
                        src={tech.logo}
                        alt={`${tech.name} logo`}
                        className="w-12 h-12 object-contain"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {tech.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {tech.yearsOfExperience} years experience
                    </p>
                    <span 
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}
                      aria-label={`Proficiency level: ${tech.proficiencyLevel}`}
                    >
                      {tech.proficiencyLevel}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                  Need Help Choosing the Right Technology?
                </h2>
                <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                  Our experts can help you select the perfect technology stack for your project.
                  Let's discuss your requirements and find the best solution.
                </p>
                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/contact"
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
                  >
                    Get Technology Consultation
                  </a>
                  <a
                    href="/services"
                    className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
                  >
                    View Our Services
                  </a>
                </div>
              </motion.div>
            </div>
          </section>
        </main>

        <Footer />
      </div>
    </ErrorBoundary>
  );
}
