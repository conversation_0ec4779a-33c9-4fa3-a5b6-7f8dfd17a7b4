'use client';

import { use<PERSON>arams } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  ArrowLeftIcon,
  StarIcon,
  CheckIcon,
  ArrowTopRightOnSquareIcon,
  ClockIcon,
  TrophyIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';

// Technology data (in real app, this would come from API or database)
const technologiesData: { [key: string]: any } = {
  'react': {
    id: 'react',
    name: 'React',
    description: 'React is a free and open-source front-end JavaScript library for building user interfaces based on UI components. It is maintained by Meta and a community of individual developers and companies. React can be used as a base in the development of single-page, mobile, or server-rendered applications.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 5,
    projectsUsed: 45,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    website: 'https://reactjs.org',
    documentation: 'https://reactjs.org/docs',
    github: 'https://github.com/facebook/react',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],
    useCases: [
      'Single Page Applications (SPAs)',
      'Interactive User Interfaces',
      'Component Libraries',
      'Progressive Web Apps',
      'Mobile Apps (React Native)',
      'Desktop Apps (Electron)'
    ],
    advantages: [
      'Virtual DOM for optimal performance',
      'Large ecosystem and community support',
      'Reusable component architecture',
      'Strong developer tools and debugging',
      'Backed by Meta (Facebook)',
      'Excellent documentation and learning resources'
    ],
    keyFeatures: [
      'Component-Based Architecture',
      'Virtual DOM',
      'JSX Syntax',
      'Unidirectional Data Flow',
      'React Hooks',
      'Server-Side Rendering Support'
    ],
    relatedTechnologies: ['Next.js', 'TypeScript', 'Redux', 'React Router'],
    projects: [
      {
        name: 'EcoCommerce Platform',
        description: 'E-commerce platform built with React and Next.js',
        url: '/projects/ecommerce-platform'
      },
      {
        name: 'HealthTracker Dashboard',
        description: 'Healthcare analytics dashboard using React',
        url: '/projects/healthcare-app'
      }
    ],
    learningResources: [
      {
        title: 'Official React Documentation',
        url: 'https://reactjs.org/docs',
        type: 'Documentation'
      },
      {
        title: 'React Tutorial for Beginners',
        url: 'https://reactjs.org/tutorial',
        type: 'Tutorial'
      },
      {
        title: 'React Patterns',
        url: 'https://reactpatterns.com',
        type: 'Best Practices'
      }
    ]
  },
  'nextjs': {
    id: 'nextjs',
    name: 'Next.js',
    description: 'Next.js is a React framework that gives you building blocks to create web applications. By framework, we mean Next.js handles the tooling and configuration needed for React, and provides additional structure, features, and optimizations for your application.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 32,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    website: 'https://nextjs.org',
    documentation: 'https://nextjs.org/docs',
    github: 'https://github.com/vercel/next.js',
    isActive: true,
    isFeatured: true,
    tags: ['React', 'SSR', 'SSG', 'Full-stack'],
    useCases: [
      'E-commerce Websites',
      'Corporate Websites',
      'Blogs and Content Sites',
      'Web Applications',
      'Landing Pages',
      'API Development'
    ],
    advantages: [
      'Built-in SEO optimization',
      'Automatic code splitting',
      'API routes for backend logic',
      'Excellent performance out of the box',
      'Zero-config deployment',
      'Built-in CSS and Sass support'
    ],
    keyFeatures: [
      'Server-Side Rendering (SSR)',
      'Static Site Generation (SSG)',
      'API Routes',
      'Automatic Code Splitting',
      'Built-in CSS Support',
      'Image Optimization'
    ],
    relatedTechnologies: ['React', 'TypeScript', 'Vercel', 'Tailwind CSS'],
    projects: [
      {
        name: 'Corporate Website',
        description: 'Modern corporate website with Next.js',
        url: '/projects/corporate-website'
      }
    ]
  }
  // Add more technologies as needed...
};

export default function TechnologyDetailsPage() {
  const params = useParams();
  const slug = params.slug as string;
  const technology = technologiesData[slug];

  if (!technology) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="pt-20">
          <div className="container py-24 text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Technology Not Found</h1>
            <p className="text-lg text-gray-600 mb-8">The technology you're looking for doesn't exist.</p>
            <Link href="/technologies" className="btn-primary">
              Back to Technologies
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const getProficiencyColor = (level: string) => {
    switch (level) {
      case 'Expert': return 'bg-green-100 text-green-800';
      case 'Advanced': return 'bg-blue-100 text-blue-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Beginner': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">
        {/* Breadcrumb */}
        <section className="py-8 bg-gray-50">
          <div className="container">
            <nav className="flex items-center space-x-2 text-sm text-gray-600">
              <Link href="/" className="hover:text-blue-600">Home</Link>
              <span>/</span>
              <Link href="/technologies" className="hover:text-blue-600">Technologies</Link>
              <span>/</span>
              <span className="text-gray-900">{technology.name}</span>
            </nav>
          </div>
        </section>

        {/* Hero Section */}
        <section className="py-16 bg-white">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
              >
                <Link
                  href="/technologies"
                  className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-6 group"
                >
                  <ArrowLeftIcon className="w-4 h-4 mr-2 transition-transform group-hover:-translate-x-1" />
                  Back to Technologies
                </Link>

                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mr-4">
                    <img
                      src={technology.logo}
                      alt={technology.name}
                      className="w-10 h-10 object-contain"
                    />
                  </div>
                  <div>
                    <h1 className="text-4xl font-bold text-gray-900">{technology.name}</h1>
                    <p className="text-lg text-gray-600 mt-2">{technology.category} • {technology.type}</p>
                  </div>
                </div>

                <p className="text-xl text-gray-600 leading-relaxed mb-8">
                  {technology.description}
                </p>

                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div className="flex items-center">
                    <TrophyIcon className="w-5 h-5 text-blue-600 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500">Proficiency</div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(technology.proficiencyLevel)}`}>
                        {technology.proficiencyLevel}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <ClockIcon className="w-5 h-5 text-blue-600 mr-2" />
                    <div>
                      <div className="text-sm text-gray-500">Experience</div>
                      <div className="font-semibold text-gray-900">{technology.yearsOfExperience} years</div>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <a
                    href={technology.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-primary inline-flex items-center"
                  >
                    Official Website
                    <ArrowTopRightOnSquareIcon className="ml-2 w-4 h-4" />
                  </a>
                  <a
                    href={technology.documentation}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-secondary inline-flex items-center"
                  >
                    Documentation
                    <ArrowTopRightOnSquareIcon className="ml-2 w-4 h-4" />
                  </a>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative"
              >
                <div className="aspect-square bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 flex items-center justify-center">
                  <img
                    src={technology.logo}
                    alt={technology.name}
                    className="w-32 h-32 object-contain"
                  />
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Key Features */}
        <section className="py-16 bg-gray-50">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Key Features</h2>
                <div className="space-y-4">
                  {technology.keyFeatures.map((feature: string, index: number) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center"
                    >
                      <CheckIcon className="w-5 h-5 text-green-500 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Advantages</h2>
                <div className="space-y-4">
                  {technology.advantages.map((advantage: string, index: number) => (
                    <motion.div
                      key={advantage}
                      initial={{ opacity: 0, x: -10 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-start"
                    >
                      <StarIcon className="w-5 h-5 text-blue-500 mr-3 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700">{advantage}</span>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Use Cases */}
        <section className="py-16 bg-white">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Use Cases</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Here are the common scenarios where {technology.name} excels and provides the best value.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {technology.useCases.map((useCase: string, index: number) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-gray-50 p-6 rounded-lg"
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <CodeBracketIcon className="w-5 h-5 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{useCase}</h3>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Related Technologies */}
        {technology.relatedTechnologies && (
          <section className="py-16 bg-gray-50">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Related Technologies</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  Technologies that work well with {technology.name} in our development stack.
                </p>
              </motion.div>

              <div className="flex flex-wrap justify-center gap-4">
                {technology.relatedTechnologies.map((tech: string, index: number) => (
                  <motion.span
                    key={tech}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="px-6 py-3 bg-white rounded-full text-gray-700 font-medium border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all"
                  >
                    {tech}
                  </motion.span>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* Projects */}
        {technology.projects && technology.projects.length > 0 && (
          <section className="py-16 bg-white">
            <div className="container">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="text-center mb-12"
              >
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Projects Using {technology.name}</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  See how we've successfully implemented {technology.name} in real-world projects.
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {technology.projects.map((project: any, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-gray-50 p-6 rounded-lg"
                  >
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{project.name}</h3>
                    <p className="text-gray-600 mb-4">{project.description}</p>
                    <Link
                      href={project.url}
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                    >
                      View Project
                      <ArrowTopRightOnSquareIcon className="ml-1 h-4 w-4" />
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Build with {technology.name}?
              </h2>
              <p className="text-lg text-blue-100 max-w-3xl mx-auto mb-8">
                Let's discuss how {technology.name} can help bring your project to life.
                Our experts are ready to guide you through the development process.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Start Your Project
                </Link>
                <Link
                  href="/technologies"
                  className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                >
                  Explore More Technologies
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
