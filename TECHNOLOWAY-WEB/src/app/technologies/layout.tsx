import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Our Technologies | Technoloway - Expert Technology Stack',
  description: 'Explore our comprehensive technology stack including React, Next.js, Node.js, TypeScript, Python, PostgreSQL, AWS, and Docker. Expert-level proficiency across frontend, backend, and DevOps technologies.',
  keywords: 'React, Next.js, Node.js, TypeScript, Python, PostgreSQL, AWS, Docker, frontend, backend, full-stack development, web technologies',
  openGraph: {
    title: 'Our Technologies | Technoloway',
    description: 'Expert technology stack for modern web development',
    type: 'website',
    url: 'https://technoloway.com/technologies',
    siteName: 'Technoloway',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Technologies | Technoloway',
    description: 'Expert technology stack for modern web development',
  },
  alternates: {
    canonical: 'https://technoloway.com/technologies',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function TechnologiesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Technoloway",
            "url": "https://technoloway.com",
            "description": "Expert technology stack for modern web development",
            "knowsAbout": ["React", "Next.js", "Node.js", "TypeScript", "Python", "PostgreSQL", "AWS", "Docker"]
          })
        }}
      />
      {children}
    </>
  );
} 