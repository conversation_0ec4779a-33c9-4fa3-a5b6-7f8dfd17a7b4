'use client';

import dynamic from 'next/dynamic';
import { useState } from 'react';

const JobApplicationModal = dynamic(() => import('@/components/client/job-application-modal'), { ssr: false });

interface Job {
  id: string;
  title: string;
  location: string;
  employmenttype: string;
  description: string;
  requirements: string;
  salarymin?: string;
  salarymax?: string;
  salarycurrency?: string;
  isremote?: boolean;
}

interface JobDetailsClientProps {
  job: Job;
}

export default function JobDetailsClient({ job }: JobDetailsClientProps) {
  const [showModal, setShowModal] = useState(false);

  const formatEmploymentType = (type: string) => {
    return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatSalary = () => {
    if (!job.salarymin && !job.salarymax) return null;

    const currency = job.salarycurrency || 'USD';
    if (job.salarymin && job.salarymax) {
      return `$${job.salarymin.toLocaleString()} - $${job.salarymax.toLocaleString()} ${currency}`;
    } else if (job.salarymin) {
      return `$${job.salarymin.toLocaleString()}+ ${currency}`;
    } else if (job.salarymax) {
      return `Up to $${job.salarymax.toLocaleString()} ${currency}`;
    }
    return null;
  };

  return (
    <main className="pt-20 pb-16">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 mb-8 text-white">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1">
                <h1 className="text-3xl lg:text-4xl font-bold mb-4">{job.title}</h1>
                <div className="flex flex-wrap items-center gap-4 text-blue-100">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {job.location}
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z" />
                    </svg>
                    {formatEmploymentType(job.employmenttype)}
                  </div>
                  {job.isremote && (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Remote Friendly
                    </span>
                  )}
                  {formatSalary() && (
                    <div className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                      {formatSalary()}
                    </div>
                  )}
                </div>
              </div>
              <div className="mt-6 lg:mt-0 lg:ml-8">
                <button
                  className="w-full lg:w-auto bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors shadow-lg"
                  type="button"
                  onClick={() => setShowModal(true)}
                >
                  Apply Now
                </button>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              {/* Job Description */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                <div className="prose prose-blue max-w-none">
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                    {job.description}
                  </div>
                </div>
              </div>

              {/* Requirements */}
              {job.requirements && (
                <div className="bg-white rounded-xl p-6 shadow-lg">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                  <div className="prose prose-blue max-w-none">
                    <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                      {job.requirements}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Apply */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Ready to Apply?</h3>
                <p className="text-gray-600 text-sm mb-4">
                  Join our team and help shape the future of technology.
                </p>
                <button
                  className="w-full bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                  type="button"
                  onClick={() => setShowModal(true)}
                >
                  Apply for this Position
                </button>
              </div>

              {/* Job Details */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Job Details</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Location</span>
                    <span className="text-gray-900 font-medium">{job.location}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Type</span>
                    <span className="text-gray-900 font-medium">{formatEmploymentType(job.employmenttype)}</span>
                  </div>
                  {job.isremote && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Remote</span>
                      <span className="text-green-600 font-medium">Yes</span>
                    </div>
                  )}
                  {formatSalary() && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Salary</span>
                      <span className="text-gray-900 font-medium">{formatSalary()}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Share */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Share this Job</h3>
                <div className="flex space-x-3">
                  <button className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    LinkedIn
                  </button>
                  <button className="flex-1 bg-gray-800 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-900 transition-colors">
                    Twitter
                  </button>
                  <button className="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-700 transition-colors">
                    Copy Link
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <JobApplicationModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        jobId={job.id}
      />
    </main>
  );
}
