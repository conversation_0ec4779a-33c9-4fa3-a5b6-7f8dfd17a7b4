'use client';

import {
    AcademicCapIcon,
    ArrowRightIcon,
    BriefcaseIcon,
    CalendarIcon,
    ClockIcon,
    CurrencyDollarIcon,
    GlobeAltIcon,
    HeartIcon,
    LightBulbIcon,
    MapPinIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { memo, useEffect, useState } from 'react';

// Types for better type safety
interface Stat {
  readonly name: string;
  readonly value: string;
}

interface Benefit {
  readonly title: string;
  readonly description: string;
  readonly icon: string;
}

interface Job {
  id: string;
  title: string;
  location: string;
  employmenttype: string;
  description: string;
  createdat: string;
}

interface CareersPageClientProps {
  stats: readonly Stat[];
  benefits: readonly Benefit[];
}

// Icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  CurrencyDollarIcon,
  GlobeAltIcon,
  AcademicCapIcon,
  HeartIcon,
  ClockIcon,
  LightBulbIcon,
  MapPinIcon,
  BriefcaseIcon,
  CalendarIcon,
};

// Fetch jobs function
async function fetchJobs() {
  try {
    const res = await fetch('/api/careers', { cache: 'no-store' });
    if (!res.ok) throw new Error('Failed to fetch jobs');
    return res.json();
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return [];
  }
}

// Optimized components with memoization
const StatCard = memo(({ stat, index }: { stat: Stat; index: number }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.8 }}
    whileInView={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5, delay: index * 0.1 }}
    viewport={{ once: true }}
    className="text-center"
  >
    <div className="text-4xl font-bold text-white mb-2" aria-label={`${stat.value} ${stat.name}`}>
      {stat.value}
    </div>
    <div className="text-blue-100 text-sm">
      {stat.name}
    </div>
  </motion.div>
));

StatCard.displayName = 'StatCard';

const BenefitCard = memo(({ benefit, index }: { benefit: Benefit; index: number }) => {
  const Icon = iconMap[benefit.icon];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow group"
    >
      <div className="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4 group-hover:bg-blue-200 transition-colors">
        {Icon && <Icon className="w-6 h-6 text-blue-600" aria-hidden="true" />}
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        {benefit.title}
      </h3>
      <p className="text-gray-600 text-sm leading-relaxed">
        {benefit.description}
      </p>
    </motion.div>
  );
});

BenefitCard.displayName = 'BenefitCard';

const JobCard = memo(({ job, index }: { job: Job; index: number }) => {
  const formatEmploymentType = (type: string) => {
    return type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return 'Recently';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group"
    >
      <div className="flex flex-col h-full">
        <div className="flex-1">
          <div className="flex items-start justify-between mb-4">
            <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {job.title}
            </h3>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {formatEmploymentType(job.employmenttype)}
            </span>
          </div>

          <div className="flex items-center text-gray-500 text-sm mb-3">
            <MapPinIcon className="w-4 h-4 mr-1" />
            {job.location}
          </div>

          <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
            {job.description}
          </p>

          <div className="flex items-center text-gray-400 text-xs mb-4">
            <CalendarIcon className="w-4 h-4 mr-1" />
            Posted {formatDate(job.createdat)}
          </div>
        </div>

        <Link
          href={`/careers/${job.id}`}
          className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors group-hover:bg-blue-700"
        >
          View Details
          <ArrowRightIcon className="w-4 h-4 ml-2" />
        </Link>
      </div>
    </motion.div>
  );
});

JobCard.displayName = 'JobCard';

// Jobs list component
function JobsList() {
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadJobs() {
      try {
        setLoading(true);
        setError(null);
        const jobsData = await fetchJobs();
        setJobs(jobsData);
      } catch (err) {
        setError('Failed to load job listings.');
        console.error('Error loading jobs:', err);
      } finally {
        setLoading(false);
      }
    }

    loadJobs();
  }, []);

  if (loading) {
    return <JobsLoading />;
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-2">{error}</div>
        <p className="text-gray-500 text-sm">Please try again later.</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!jobs.length) {
    return (
      <div className="text-center py-16">
        <BriefcaseIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No open positions</h3>
        <p className="text-gray-500 max-w-md mx-auto">
          We don't have any open positions at the moment, but we're always looking for talented individuals.
          Feel free to send us your resume for future opportunities.
        </p>
        <Link
          href="/contact"
          className="inline-flex items-center mt-6 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Contact Us
          <ArrowRightIcon className="w-4 h-4 ml-2" />
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {jobs.map((job, index) => (
        <JobCard key={job.id} job={job} index={index} />
      ))}
    </div>
  );
}

// Loading component
const JobsLoading = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {[...Array(6)].map((_, i) => (
      <div key={i} className="bg-white rounded-xl p-6 shadow-lg animate-pulse">
        <div className="h-6 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded mb-4 w-1/2"></div>
        <div className="space-y-2 mb-4">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded w-5/6"></div>
        </div>
        <div className="h-10 bg-gray-200 rounded"></div>
      </div>
    ))}
  </div>
);

// CTA Section
const CTASection = memo(() => (
  <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600" aria-label="Call to action">
    <div className="container mx-auto px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="text-center"
      >
        <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
          Don't See the Right Position?
        </h2>
        <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
          We're always looking for talented individuals to join our team.
          Send us your resume and let's discuss how you can contribute to our mission.
        </p>
        <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/contact"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
            aria-label="Send your resume to Technoloway"
          >
            Send Your Resume
          </Link>
          <Link
            href="/about"
            className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
            aria-label="Learn more about Technoloway"
          >
            Learn About Us
          </Link>
        </div>
      </motion.div>
    </div>
  </section>
));

CTASection.displayName = 'CTASection';

// Main component
export function CareersPageClient({ stats, benefits }: CareersPageClientProps) {
  return (
    <main className="pt-20" role="main" aria-label="Careers page content">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700" aria-label="Careers hero">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl"
            >
              Join Our{' '}
              <span className="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Amazing Team
              </span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg text-blue-100 max-w-3xl mx-auto leading-relaxed"
            >
              Shape the future of technology with us. We're building innovative solutions
              that make a difference, and we want passionate individuals to join our mission.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mt-8 flex flex-col sm:flex-row gap-4 justify-center"
            >
              <a
                href="#open-positions"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              >
                View Open Positions
                <ArrowRightIcon className="w-4 h-4 ml-2" />
              </a>
              <a
                href="#benefits"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              >
                Learn About Benefits
              </a>
            </motion.div>
          </motion.div>

          {/* Stats Section */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {stats.map((stat, index) => (
              <StatCard key={stat.name} stat={stat} index={index} />
            ))}
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-24 bg-gray-50" aria-label="Employee benefits">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Why Work With Us?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              We believe in creating an environment where our team can thrive,
              innovate, and grow both personally and professionally.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <BenefitCard key={benefit.title} benefit={benefit} index={index} />
            ))}
          </div>
        </div>
      </section>

      {/* Open Positions Section */}
      <section id="open-positions" className="py-24 bg-white" aria-label="Open positions">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center max-w-3xl mx-auto mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Open Positions
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Discover exciting opportunities to grow your career and make an impact
              with cutting-edge technology projects.
            </p>
          </motion.div>

          <JobsList />
        </div>
      </section>

      {/* CTA Section */}
      <CTASection />
    </main>
  );
}
