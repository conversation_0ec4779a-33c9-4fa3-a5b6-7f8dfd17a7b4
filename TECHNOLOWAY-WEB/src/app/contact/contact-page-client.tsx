'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { memo } from 'react';
import {
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import { useStaticContent } from '@/lib/hooks/use-static-content';

// Types for better type safety
interface ContactPageClientProps {
  projectTypes: readonly string[];
  budgetRanges: readonly string[];
  timelines: readonly string[];
  faqItems: readonly Array<{
    readonly question: string;
    readonly answer: string;
  }>;
}

// Form validation schema
const contactSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number').optional(),
  company: z.string().optional(),
  projectType: z.string().min(1, 'Please select a project type'),
  budget: z.string().min(1, 'Please select a budget range'),
  timeline: z.string().min(1, 'Please select a timeline'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms'),
});

type ContactFormData = z.infer<typeof contactSchema>;

// Optimized components with memoization
const ContactInfoCard = memo(({ 
  icon: Icon, 
  title, 
  content, 
  subContent, 
  index 
}: { 
  icon: React.ComponentType<any>; 
  title: string; 
  content: string; 
  subContent: string; 
  index: number; 
}) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    viewport={{ once: true }}
    className="text-center"
  >
    <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mx-auto mb-4">
      <Icon className="w-8 h-8 text-blue-600" aria-hidden="true" />
    </div>
    <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-700 font-medium">{content}</p>
    <p className="text-sm text-gray-500 mt-1">{subContent}</p>
  </motion.div>
));

ContactInfoCard.displayName = 'ContactInfoCard';

const FAQItem = memo(({ faq, index }: { faq: { question: string; answer: string }; index: number }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: index * 0.1 }}
    viewport={{ once: true }}
    className="bg-gray-50 rounded-lg p-6"
  >
    <h3 className="text-lg font-semibold text-gray-900 mb-3">{faq.question}</h3>
    <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
  </motion.div>
));

FAQItem.displayName = 'FAQItem';

const StatusMessage = memo(({ 
  status, 
  message, 
  description, 
  icon: Icon 
}: { 
  status: 'success' | 'error'; 
  message: string; 
  description: string; 
  icon: React.ComponentType<any>; 
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    className={`mb-8 p-4 ${
      status === 'success' 
        ? 'bg-green-50 border border-green-200' 
        : 'bg-red-50 border border-red-200'
    } rounded-lg flex items-center`}
    role="alert"
    aria-live="polite"
  >
    <Icon className={`w-5 h-5 ${
      status === 'success' ? 'text-green-500' : 'text-red-500'
    } mr-3`} aria-hidden="true" />
    <div>
      <h3 className={`text-sm font-medium ${
        status === 'success' ? 'text-green-800' : 'text-red-800'
      }`}>
        {message}
      </h3>
      <p className={`text-sm ${
        status === 'success' ? 'text-green-700' : 'text-red-700'
      } mt-1`}>
        {description}
      </p>
    </div>
  </motion.div>
));

StatusMessage.displayName = 'StatusMessage';

export function ContactPageClient({ 
  projectTypes, 
  budgetRanges, 
  timelines, 
  faqItems 
}: ContactPageClientProps) {
  const { getContent } = useStaticContent();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
  });

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Transform form data to match API schema
      const apiData = {
        name: `${data.firstName} ${data.lastName}`,
        email: data.email,
        phone: data.phone || undefined,
        subject: `${data.projectType} Project Inquiry`,
        message: `Project Type: ${data.projectType}\nBudget: ${data.budget}\nTimeline: ${data.timeline}\nCompany: ${data.company || 'Not specified'}\n\nMessage:\n${data.message}`,
      };

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send message');
      }

      setSubmitStatus('success');
      reset();
    } catch (error) {
      // Log error for debugging but don't expose to user
      if (process.env.NODE_ENV === 'development') {
        console.error('Contact form submission error:', error);
      }
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfoItems = [
    {
      icon: EnvelopeIcon,
      title: getContent('contact', 'info', 'email_label', 'Email Us'),
      content: getContent('contact', 'info', 'email_value', '<EMAIL>'),
      subContent: 'We respond within 24 hours',
    },
    {
      icon: PhoneIcon,
      title: getContent('contact', 'info', 'phone_label', 'Call Us'),
      content: getContent('contact', 'info', 'phone_value', '+****************'),
      subContent: getContent('contact', 'info', 'hours_value', 'Mon-Fri: 9:00 AM - 6:00 PM'),
    },
    {
      icon: MapPinIcon,
      title: getContent('contact', 'info', 'address_label', 'Visit Us'),
      content: getContent('contact', 'info', 'address_value', '123 Innovation Drive, Tech City, TC 12345'),
      subContent: 'By appointment only',
    },
  ];

  return (
    <main className="pt-20" role="main" aria-label="Contact page content">
      {/* Hero Section */}
      <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100" aria-label="Contact hero">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
            >
              {getContent('contact', 'hero', 'title', 'Let\'s Build Something')} <span className="gradient-text">{getContent('contact', 'hero', 'title_highlight', 'Amazing')}</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
            >
              {getContent('contact', 'hero', 'subtitle', 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.')}
            </motion.p>
          </motion.div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-16 bg-white" aria-label="Contact information">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16" role="list" aria-label="Contact methods">
            {contactInfoItems.map((item, index) => (
              <ContactInfoCard
                key={`contact-info-${item.title}-${index}`}
                icon={item.icon}
                title={item.title}
                content={item.content}
                subContent={item.subContent}
                index={index}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-16 bg-gray-50" aria-label="Contact form">
        <div className="container">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {getContent('contact', 'form', 'title', 'Start Your')} <span className="gradient-text">{getContent('contact', 'form', 'title_highlight', 'Project')}</span>
              </h2>
              <p className="text-lg text-gray-600">
                {getContent('contact', 'form', 'subtitle', 'Tell us about your project and we\'ll get back to you within 24 hours.')}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl shadow-lg p-8"
            >
              {/* Status Messages */}
              {submitStatus === 'success' && (
                <StatusMessage
                  status="success"
                  message="Message sent successfully!"
                  description="Thank you for reaching out. We'll get back to you within 24 hours."
                  icon={CheckCircleIcon}
                />
              )}

              {submitStatus === 'error' && (
                <StatusMessage
                  status="error"
                  message="Error sending message"
                  description="Please try again or contact us <NAME_EMAIL>"
                  icon={ExclamationTriangleIcon}
                />
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" aria-label="Project inquiry form">
                {/* Name Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-2">
                      First Name *
                    </label>
                    <input
                      {...register('firstName')}
                      type="text"
                      id="firstName"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.firstName ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="John"
                      aria-describedby={errors.firstName ? 'firstName-error' : undefined}
                    />
                    {errors.firstName && (
                      <p id="firstName-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-2">
                      Last Name *
                    </label>
                    <input
                      {...register('lastName')}
                      type="text"
                      id="lastName"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.lastName ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="Doe"
                      aria-describedby={errors.lastName ? 'lastName-error' : undefined}
                    />
                    {errors.lastName && (
                      <p id="lastName-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Contact Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address *
                    </label>
                    <input
                      {...register('email')}
                      type="email"
                      id="email"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.email ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="<EMAIL>"
                      aria-describedby={errors.email ? 'email-error' : undefined}
                    />
                    {errors.email && (
                      <p id="email-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.email.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      {...register('phone')}
                      type="tel"
                      id="phone"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.phone ? 'border-red-300' : 'border-gray-300'
                      }`}
                      placeholder="+****************"
                      aria-describedby={errors.phone ? 'phone-error' : undefined}
                    />
                    {errors.phone && (
                      <p id="phone-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.phone.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Company */}
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
                    Company Name
                  </label>
                  <input
                    {...register('company')}
                    type="text"
                    id="company"
                    className="block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Your Company"
                  />
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label htmlFor="projectType" className="block text-sm font-medium text-gray-700 mb-2">
                      Project Type *
                    </label>
                    <select
                      {...register('projectType')}
                      id="projectType"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.projectType ? 'border-red-300' : 'border-gray-300'
                      }`}
                      aria-describedby={errors.projectType ? 'projectType-error' : undefined}
                    >
                      <option value="">Select type</option>
                      {projectTypes.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                    {errors.projectType && (
                      <p id="projectType-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.projectType.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">
                      Budget Range *
                    </label>
                    <select
                      {...register('budget')}
                      id="budget"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.budget ? 'border-red-300' : 'border-gray-300'
                      }`}
                      aria-describedby={errors.budget ? 'budget-error' : undefined}
                    >
                      <option value="">Select budget</option>
                      {budgetRanges.map(range => (
                        <option key={range} value={range}>{range}</option>
                      ))}
                    </select>
                    {errors.budget && (
                      <p id="budget-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.budget.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-2">
                      Timeline *
                    </label>
                    <select
                      {...register('timeline')}
                      id="timeline"
                      className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                        errors.timeline ? 'border-red-300' : 'border-gray-300'
                      }`}
                      aria-describedby={errors.timeline ? 'timeline-error' : undefined}
                    >
                      <option value="">Select timeline</option>
                      {timelines.map(time => (
                        <option key={time} value={time}>{time}</option>
                      ))}
                    </select>
                    {errors.timeline && (
                      <p id="timeline-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.timeline.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Message */}
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                    Project Description *
                  </label>
                  <textarea
                    {...register('message')}
                    id="message"
                    rows={6}
                    className={`block w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.message ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Tell us about your project, goals, and any specific requirements..."
                    aria-describedby={errors.message ? 'message-error' : undefined}
                  />
                  {errors.message && (
                    <p id="message-error" className="mt-1 text-sm text-red-600" role="alert">
                      {errors.message.message}
                    </p>
                  )}
                </div>

                {/* Terms Agreement */}
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      {...register('agreeToTerms')}
                      id="agreeToTerms"
                      type="checkbox"
                      className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
                      aria-describedby={errors.agreeToTerms ? 'agreeToTerms-error' : undefined}
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="agreeToTerms" className="text-gray-700">
                      I agree to the{' '}
                      <a href="/legal/terms" className="text-blue-600 hover:text-blue-500">
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a href="/legal/privacy" className="text-blue-600 hover:text-blue-500">
                        Privacy Policy
                      </a>
                      *
                    </label>
                    {errors.agreeToTerms && (
                      <p id="agreeToTerms-error" className="mt-1 text-sm text-red-600" role="alert">
                        {errors.agreeToTerms.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Submit Button */}
                <div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-describedby={isSubmitting ? 'submitting-status' : undefined}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" aria-hidden="true"></div>
                        <span id="submitting-status">Sending Message...</span>
                      </div>
                    ) : (
                      'Send Message'
                    )}
                  </button>
                </div>
              </form>
            </motion.div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white" aria-label="Frequently asked questions">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Quick answers to common questions about our services and process.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-6" role="list" aria-label="FAQ items">
            {faqItems.map((faq, index) => (
              <FAQItem key={`faq-${index}`} faq={faq} index={index} />
            ))}
          </div>
        </div>
      </section>
    </main>
  );
} 