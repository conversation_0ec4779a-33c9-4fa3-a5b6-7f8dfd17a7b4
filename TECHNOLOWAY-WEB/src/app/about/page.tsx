'use client';

import { Footer } from '@/components/footer';
import { Header } from '@/components/header';
import {
  AcademicCapIcon,
  ArrowRightIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  CheckCircleIcon,
  GlobeAltIcon,
  HeartIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  ShieldCheckIcon,
  SparklesIcon,
  StarIcon,
  TrophyIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useEffect, useState } from 'react';

// Static data - moved to constants for better maintainability
const VALUES = [
  {
    name: 'Innovation',
    description: 'We stay ahead of technology trends and embrace cutting-edge solutions to deliver exceptional results.',
    icon: 'LightBulbIcon',
  },
  {
    name: 'Quality',
    description: 'We deliver excellence in every project, ensuring robust, scalable, and maintainable solutions.',
    icon: 'ShieldCheckIcon',
  },
  {
    name: 'Partnership',
    description: 'We work as an extension of your team, fostering collaboration and transparent communication.',
    icon: 'HeartIcon',
  },
  {
    name: 'Growth',
    description: 'We are committed to continuous learning and helping our clients achieve sustainable growth.',
    icon: 'RocketLaunchIcon',
  },
] as const;

const STATS = [
  { name: 'Years of Experience', value: '10+' },
  { name: 'Projects Completed', value: '500+' },
  { name: 'Happy Clients', value: '200+' },
  { name: 'Team Members', value: '50+' },
  { name: 'Countries Served', value: '25+' },
  { name: 'Technologies Mastered', value: '100+' },
] as const;

const MILESTONES = [
  {
    year: '2014',
    title: 'Company Founded',
    description: 'Started as a small team with a big vision to democratize technology.',
  },
  {
    year: '2016',
    title: 'First Major Client',
    description: 'Secured our first enterprise client and delivered a game-changing solution.',
  },
  {
    year: '2018',
    title: 'Team Expansion',
    description: 'Grew to 25+ team members and opened our second office.',
  },
  {
    year: '2020',
    title: 'Global Reach',
    description: 'Expanded internationally and started serving clients across 5 continents.',
  },
  {
    year: '2022',
    title: 'Innovation Hub',
    description: 'Launched our R&D division focusing on AI and emerging technologies.',
  },
  {
    year: '2024',
    title: 'Industry Leader',
    description: 'Recognized as a leading software development company with 500+ successful projects.',
  },
] as const;

const ACHIEVEMENTS = [
  {
    title: 'Best Tech Company 2024',
    organization: 'Tech Awards',
    year: '2024',
    icon: 'TrophyIcon',
  },
  {
    title: 'Top 100 Startups',
    organization: 'Forbes',
    year: '2023',
    icon: 'StarIcon',
  },
  {
    title: 'Excellence in Innovation',
    organization: 'Digital Innovation Awards',
    year: '2023',
    icon: 'AcademicCapIcon',
  },
  {
    title: 'Best Workplace',
    organization: 'Great Place to Work',
    year: '2022',
    icon: 'BuildingOfficeIcon',
  },
] as const;

// Icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  LightBulbIcon,
  ShieldCheckIcon,
  HeartIcon,
  RocketLaunchIcon,
  GlobeAltIcon,
  SparklesIcon,
  UserGroupIcon,
  TrophyIcon,
  CalendarIcon,
  StarIcon,
  AcademicCapIcon,
  BuildingOfficeIcon,
};

// Type for team member from database
interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio: string | null;
  photourl: string | null;
  email: string | null;
  linkedinurl: string | null;
  twitterurl: string | null;
  githuburl: string | null;
  displayorder: number;
}

export default function AboutPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/about/team');
        if (response.ok) {
          const data = await response.json();
          setTeamMembers(data);
        } else {
          console.error('Failed to fetch team members');
        }
      } catch (error) {
        console.error('Error fetching team members:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main className="pt-20" role="main" aria-label="About Technoloway company information">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-blue-900 to-purple-900" aria-label="About hero">
          <div className="container relative z-10 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="max-w-5xl mx-auto"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mb-8"
              >
                <span className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm px-6 py-3 text-sm font-medium text-white ring-1 ring-inset ring-white/20">
                  <SparklesIcon className="w-4 h-4 mr-2" aria-hidden="true" />
                  Our Story
                </span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="text-5xl font-bold tracking-tight text-white sm:text-7xl lg:text-8xl mb-8"
              >
                Transforming Ideas Into{' '}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                  Digital Reality
                </span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="text-xl leading-8 text-blue-100 sm:text-2xl lg:text-3xl mb-12 max-w-4xl mx-auto"
              >
                We're not just another software company. We're your partners in innovation, helping businesses of all sizes harness the power of cutting-edge technology to achieve extraordinary results.
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-6 justify-center items-center"
              >
                <Link
                  href="/contact"
                  className="group inline-flex items-center px-8 py-4 bg-white text-blue-900 rounded-2xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105"
                >
                  Start Your Journey
                  <ArrowRightIcon className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-20 bg-gray-50" aria-label="Company statistics">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold text-gray-900 mb-4">
                Numbers That Tell Our Story
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Over a decade of excellence, innovation, and client success
              </p>
            </motion.div>

            <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-6">
              {STATS.map((stat, index) => (
                <motion.div
                  key={`stat-${stat.name}-${index}`}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                    <div className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 sm:text-5xl">
                      {stat.value}
                    </div>
                    <div className="mt-2 text-sm text-gray-600 font-medium">
                      {stat.name}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Company Story / Mission Section */}
        <section className="py-24 bg-white" aria-label="Company story and mission">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Story</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                From a small startup to an industry leader - here's how we got here
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl flex items-center justify-center">
                      <SparklesIcon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">The Beginning</h3>
                      <p className="text-gray-600 leading-relaxed">
                        In 2014, our founder Sarah Johnson left Google with a simple mission: to make technology accessible to businesses of all sizes. What started as a team of three developers has grown into a global force for digital transformation.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl flex items-center justify-center">
                      <HeartIcon className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">Our Mission</h3>
                      <p className="text-gray-600 leading-relaxed">
                        We believe that every business deserves access to world-class technology solutions. Our mission is to democratize innovation, making cutting-edge software development accessible, affordable, and impactful for organizations worldwide.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl flex items-center justify-center">
                      <RocketLaunchIcon className="w-6 h-6 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2">The Future</h3>
                      <p className="text-gray-600 leading-relaxed">
                        Today, we're not just building software—we're building the future. With AI, machine learning, and emerging technologies, we're helping businesses stay ahead of the curve and thrive in the digital age.
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 shadow-xl">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                      <SparklesIcon className="w-10 h-10 text-white" />
                    </div>
                    <blockquote className="text-2xl font-semibold text-gray-900 mb-4 italic">
                      "Technology should empower people, not complicate their lives. That's been our guiding principle from day one."
                    </blockquote>
                    <p className="text-lg text-gray-600">— Sarah Johnson, CEO & Founder</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-24 bg-gray-50" aria-label="Our values">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Core Values</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                The principles that guide everything we do and shape every decision we make
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {VALUES.map((value, index) => {
                const Icon = iconMap[value.icon];
                return (
                  <motion.div
                    key={`value-${value.name}-${index}`}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                      <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                        {Icon && <Icon className="w-8 h-8 text-blue-600" aria-hidden="true" />}
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4">
                        {value.name}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-24 bg-white" aria-label="Our team">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Meet Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Leadership</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                The brilliant minds behind our success, dedicated to transforming your ideas into reality
              </p>
            </motion.div>

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
              </div>
            ) : teamMembers.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {teamMembers.map((member, index) => (
                  <motion.div
                    key={`team-${member.id}-${index}`}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden">
                      <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                        {member.photourl ? (
                          <img
                            src={member.photourl}
                            alt={member.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <UserGroupIcon className="w-24 h-24 text-blue-600" />
                        )}
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                        <p className="text-blue-600 font-medium mb-4">{member.position}</p>
                        {member.bio && (
                          <p className="text-gray-600 text-sm leading-relaxed mb-4">
                            {member.bio}
                          </p>
                        )}
                        <div className="flex space-x-3">
                          {member.linkedinurl && (
                            <a
                              href={member.linkedinurl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              LinkedIn
                            </a>
                          )}
                          {member.twitterurl && (
                            <a
                              href={member.twitterurl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              Twitter
                            </a>
                          )}
                          {member.githuburl && (
                            <a
                              href={member.githuburl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 transition-colors"
                            >
                              GitHub
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-600">No team members found.</p>
              </div>
            )}
          </div>
        </section>

        {/* Milestones & Achievements Section */}
        <section className="py-24 bg-gray-50" aria-label="Milestones and achievements">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Journey</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Key milestones and achievements that mark our path to success
              </p>
            </motion.div>

            {/* Timeline */}
            <div className="relative">
              <div className="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gradient-to-b from-blue-600 to-purple-600"></div>

              <div className="space-y-12">
                {MILESTONES.map((milestone, index) => (
                  <motion.div
                    key={`milestone-${milestone.year}-${index}`}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -30 : 30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
                  >
                    <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                      <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
                        <div className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-2">
                          {milestone.year}
                        </div>
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{milestone.title}</h3>
                        <p className="text-gray-600">{milestone.description}</p>
                      </div>
                    </div>

                    <div className="relative z-10 w-4 h-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full border-4 border-white shadow-lg"></div>

                    <div className="w-1/2"></div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Awards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="mt-20"
            >
              <h3 className="text-3xl font-bold text-center text-gray-900 mb-12">Awards & Recognition</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {ACHIEVEMENTS.map((achievement, index) => {
                  const Icon = iconMap[achievement.icon];
                  return (
                    <motion.div
                      key={`achievement-${achievement.title}-${index}`}
                      initial={{ opacity: 0, scale: 0.8 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 text-center"
                    >
                      <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-100 to-purple-100 rounded-2xl mx-auto mb-4">
                        {Icon && <Icon className="w-8 h-8 text-blue-600" />}
                      </div>
                      <h4 className="text-lg font-bold text-gray-900 mb-2">{achievement.title}</h4>
                      <p className="text-gray-600 text-sm mb-2">{achievement.organization}</p>
                      <p className="text-blue-600 font-medium">{achievement.year}</p>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Vision & Future Goals Section */}
        <section className="py-24 bg-white" aria-label="Vision and future goals">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl mb-6">
                Our <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">Vision</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Looking ahead to the future of technology and our role in shaping it
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <div className="space-y-8">
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mr-4">
                        <RocketLaunchIcon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">AI-First Future</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      We're investing heavily in AI and machine learning to create intelligent solutions that adapt and learn from user behavior, making technology more intuitive and powerful than ever before.
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl flex items-center justify-center mr-4">
                        <GlobeAltIcon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">Global Expansion</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      By 2025, we aim to have offices in 15 countries and serve clients across every continent, bringing our innovative solutions to businesses worldwide.
                    </p>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-xl flex items-center justify-center mr-4">
                        <AcademicCapIcon className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900">Education & Training</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      We're launching Technoloway Academy to train the next generation of developers and tech leaders, ensuring the future of technology is in capable hands.
                    </p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 rounded-3xl p-8 text-white shadow-2xl">
                  <div className="text-center">
                    <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6">
                      <SparklesIcon className="w-12 h-12 text-white" />
                    </div>
                    <h3 className="text-3xl font-bold mb-6">Building Tomorrow's Technology</h3>
                    <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                      Our vision extends beyond today's challenges. We're building the foundation for a future where technology seamlessly integrates into every aspect of business and life.
                    </p>
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div>
                        <div className="text-3xl font-bold text-blue-300">2025</div>
                        <div className="text-sm text-blue-200">Global Presence</div>
                      </div>
                      <div>
                        <div className="text-3xl font-bold text-blue-300">1000+</div>
                        <div className="text-sm text-blue-200">Projects Delivered</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-blue-900 via-purple-900 to-indigo-900 relative overflow-hidden" aria-label="Call to action">
          <div className="container relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center max-w-4xl mx-auto"
            >
              <h2 className="text-4xl font-bold tracking-tight text-white sm:text-6xl mb-8">
                Ready to Transform Your Business?
              </h2>
              <p className="text-xl text-blue-100 mb-12 leading-8">
                Join hundreds of satisfied clients who have transformed their businesses with our innovative solutions.
                Let's build something extraordinary together and turn your vision into reality.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Link
                  href="/contact"
                  className="group inline-flex items-center px-8 py-4 bg-white text-blue-900 rounded-2xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 shadow-xl hover:shadow-2xl hover:scale-105"
                  aria-label="Get started with Technoloway"
                >
                  Start Your Project Today
                  <ArrowRightIcon className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </Link>
                <Link
                  href="/portfolio"
                  className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm border-2 border-white/20 text-white rounded-2xl font-bold text-lg hover:bg-white/20 transition-all duration-300"
                  aria-label="View our portfolio of work"
                >
                  Explore Our Portfolio
                </Link>
              </div>

              <div className="mt-12 flex items-center justify-center space-x-8 text-blue-200">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="w-5 h-5" />
                  <span>Free Consultation</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="w-5 h-5" />
                  <span>No Hidden Costs</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="w-5 h-5" />
                  <span>24/7 Support</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
