'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon,
  ChartBarIcon,
  ShoppingCartIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';

const projects = [
  {
    id: 1,
    title: 'EcoCommerce Platform',
    description: 'A sustainable e-commerce platform built for eco-friendly products with advanced analytics and inventory management.',
    longDescription: 'Complete e-commerce solution featuring real-time inventory tracking, AI-powered product recommendations, and carbon footprint calculations for each purchase.',
    category: 'E-commerce',
    technologies: ['Next.js', 'TypeScript', 'PostgreSQL', 'Stripe', 'AWS'],
    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',
    liveUrl: 'https://ecocommerce-demo.com',
    githubUrl: 'https://github.com/technoloway/ecocommerce',
    featured: true,
    results: {
      metric1: { label: 'Revenue Increase', value: '150%' },
      metric2: { label: 'User Engagement', value: '+85%' },
      metric3: { label: 'Page Load Speed', value: '2.1s' },
    },
    icon: ShoppingCartIcon,
    client: 'GreenTech Solutions',
    duration: '6 months',
    teamSize: '5 developers',
  },
  {
    id: 2,
    title: 'HealthTracker Mobile App',
    description: 'Cross-platform mobile application for health monitoring with real-time data synchronization and AI insights.',
    longDescription: 'Comprehensive health tracking app with wearable device integration, personalized health insights, and telemedicine features.',
    category: 'Mobile App',
    technologies: ['React Native', 'Node.js', 'MongoDB', 'Firebase', 'TensorFlow'],
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
    liveUrl: 'https://healthtracker-app.com',
    githubUrl: null,
    featured: true,
    results: {
      metric1: { label: 'Active Users', value: '50K+' },
      metric2: { label: 'App Store Rating', value: '4.8/5' },
      metric3: { label: 'Data Accuracy', value: '99.2%' },
    },
    icon: DevicePhoneMobileIcon,
    client: 'MedTech Innovations',
    duration: '8 months',
    teamSize: '4 developers',
  },
  {
    id: 3,
    title: 'FinanceFlow Dashboard',
    description: 'Real-time financial analytics dashboard for investment firms with advanced charting and portfolio management.',
    longDescription: 'Enterprise-grade financial dashboard with real-time market data, risk analysis, and automated reporting capabilities.',
    category: 'Web Application',
    technologies: ['React', 'D3.js', 'Python', 'Redis', 'Docker'],
    image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop',
    liveUrl: 'https://financeflow-demo.com',
    githubUrl: 'https://github.com/technoloway/financeflow',
    featured: false,
    results: {
      metric1: { label: 'Processing Speed', value: '10x faster' },
      metric2: { label: 'Data Accuracy', value: '99.9%' },
      metric3: { label: 'User Satisfaction', value: '95%' },
    },
    icon: ChartBarIcon,
    client: 'Capital Investments LLC',
    duration: '4 months',
    teamSize: '6 developers',
  },
  {
    id: 4,
    title: 'EduConnect Learning Platform',
    description: 'Online learning management system with interactive courses, progress tracking, and collaborative features.',
    longDescription: 'Comprehensive LMS with video streaming, interactive quizzes, peer collaboration tools, and AI-powered learning recommendations.',
    category: 'Education',
    technologies: ['Vue.js', 'Laravel', 'MySQL', 'WebRTC', 'AWS S3'],
    image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=600&fit=crop',
    liveUrl: 'https://educonnect-platform.com',
    githubUrl: null,
    featured: false,
    results: {
      metric1: { label: 'Student Enrollment', value: '25K+' },
      metric2: { label: 'Course Completion', value: '+40%' },
      metric3: { label: 'Platform Uptime', value: '99.8%' },
    },
    icon: AcademicCapIcon,
    client: 'EduTech Academy',
    duration: '10 months',
    teamSize: '7 developers',
  },
  {
    id: 5,
    title: 'SmartCity IoT Platform',
    description: 'IoT data management platform for smart city infrastructure with real-time monitoring and predictive analytics.',
    longDescription: 'Scalable IoT platform managing thousands of sensors across urban infrastructure with machine learning-powered insights.',
    category: 'IoT Platform',
    technologies: ['Node.js', 'InfluxDB', 'Grafana', 'Kubernetes', 'MQTT'],
    image: 'https://images.unsplash.com/photo-1480714378408-67cf0d13bc1f?w=800&h=600&fit=crop',
    liveUrl: 'https://smartcity-demo.com',
    githubUrl: 'https://github.com/technoloway/smartcity',
    featured: false,
    results: {
      metric1: { label: 'Sensors Connected', value: '10K+' },
      metric2: { label: 'Data Points/Day', value: '1M+' },
      metric3: { label: 'Energy Savings', value: '30%' },
    },
    icon: GlobeAltIcon,
    client: 'Metro City Council',
    duration: '12 months',
    teamSize: '8 developers',
  },
  {
    id: 6,
    title: 'CodeReview AI Assistant',
    description: 'AI-powered code review tool that provides intelligent suggestions and identifies potential issues automatically.',
    longDescription: 'Machine learning-based code analysis tool that integrates with popular version control systems to provide automated code reviews.',
    category: 'Developer Tools',
    technologies: ['Python', 'TensorFlow', 'FastAPI', 'PostgreSQL', 'Docker'],
    image: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=600&fit=crop',
    liveUrl: 'https://codereview-ai.com',
    githubUrl: 'https://github.com/technoloway/codereview-ai',
    featured: false,
    results: {
      metric1: { label: 'Bug Detection', value: '+75%' },
      metric2: { label: 'Review Time', value: '-60%' },
      metric3: { label: 'Code Quality', value: '+45%' },
    },
    icon: CodeBracketIcon,
    client: 'DevTools Inc.',
    duration: '5 months',
    teamSize: '4 developers',
  },
];

const categories = ['All', 'E-commerce', 'Mobile App', 'Web Application', 'Education', 'IoT Platform', 'Developer Tools'];

export default function PortfolioPage() {
  const featuredProjects = projects.filter(project => project.featured);
  const allProjects = projects;

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Header */}
      <header className="bg-gray-50 py-20 pt-32">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
              Our <span className="gradient-text">Portfolio</span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our successful projects and case studies. From startups to enterprise solutions, 
              we've helped businesses transform their ideas into powerful digital experiences.
            </p>
          </motion.div>
        </div>
      </header>

      <main className="container py-16">
        {/* Featured Projects */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-20"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Featured Projects</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {featuredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 hover:shadow-xl transition-shadow"
              >
                <div className="relative h-64">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-600 text-white">
                      Featured
                    </span>
                  </div>
                </div>
                
                <div className="p-8">
                  <div className="flex items-center space-x-2 mb-4">
                    <project.icon className="w-6 h-6 text-blue-600" />
                    <span className="text-sm font-medium text-blue-600">{project.category}</span>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-3">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-6">
                    {project.longDescription}
                  </p>
                  
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    {Object.entries(project.results).map(([key, result]) => (
                      <div key={key} className="text-center">
                        <div className="text-2xl font-bold text-blue-600">{result.value}</div>
                        <div className="text-sm text-gray-500">{result.label}</div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      <p><strong>Client:</strong> {project.client}</p>
                      <p><strong>Duration:</strong> {project.duration}</p>
                    </div>
                    
                    <div className="flex space-x-3">
                      {project.liveUrl && (
                        <Link
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          <ArrowTopRightOnSquareIcon className="w-4 h-4 mr-2" />
                          Live Demo
                        </Link>
                      )}
                      {project.githubUrl && (
                        <Link
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                        >
                          <CodeBracketIcon className="w-4 h-4 mr-2" />
                          Code
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* Category Filter */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-12"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
              >
                {category}
              </button>
            ))}
          </div>
        </motion.section>

        {/* All Projects Grid */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">All Projects</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {allProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-2">
                      {project.liveUrl && (
                        <Link
                          href={project.liveUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 bg-white rounded-full text-gray-700 hover:text-blue-600"
                        >
                          <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                        </Link>
                      )}
                      {project.githubUrl && (
                        <Link
                          href={project.githubUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="p-2 bg-white rounded-full text-gray-700 hover:text-blue-600"
                        >
                          <CodeBracketIcon className="w-5 h-5" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <project.icon className="w-5 h-5 text-blue-600" />
                    <span className="text-sm font-medium text-blue-600">{project.category}</span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {project.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {project.technologies.slice(0, 3).map((tech) => (
                      <span
                        key={tech}
                        className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                      >
                        {tech}
                      </span>
                    ))}
                    {project.technologies.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        +{project.technologies.length - 3} more
                      </span>
                    )}
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    <p><strong>Client:</strong> {project.client}</p>
                    <p><strong>Team:</strong> {project.teamSize}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.section>

        {/* CTA Section */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-12 text-center"
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Start Your Project?
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help bring your ideas to life. 
            Our team is ready to tackle your next challenge.
          </p>
          <Link href="/contact" className="btn-primary">
            Start Your Project
          </Link>
        </motion.section>
      </main>
      <Footer />
    </div>
  );
}
