# Client Dashboard

A comprehensive client dashboard built using the Frontend Mentor feedback board design specification as a visual reference. This dashboard provides clients with a modern, intuitive interface to manage their projects, invoices, payments, and communications.

## Features

### 🏠 Dashboard Overview
- **Client Information Panel**: Displays client details with avatar, company info, and contact information
- **Project Status Sidebar**: Quick overview of project statuses with color-coded indicators
- **Interactive Navigation**: Smooth transitions between different sections

### 📁 Projects Management
- **Project Cards**: Interactive cards with voting system, progress bars, and status indicators
- **Progress Tracking**: Visual progress bars showing project completion percentage
- **Status Management**: Color-coded status badges (In Progress, Planning, Completed)
- **Category Tags**: Project categorization (Enhancement, Feature, Bug)
- **Comments System**: View and track project discussions

### 📄 Invoices Section
- **Invoice Overview**: List of all invoices with status, amounts, and due dates
- **Status Tracking**: Visual status indicators (Paid, Pending, Overdue)
- **Quick Actions**: Direct payment buttons and receipt viewing
- **Due Date Alerts**: Clear visibility of payment deadlines

### 💳 Payment Processing
- **Payment Form**: Secure payment interface with multiple payment methods
- **Invoice Selection**: Dropdown to select specific invoices for payment
- **Payment Methods**: Support for Credit Card, Bank Transfer, and PayPal
- **Amount Display**: Clear total amount visualization

### 📊 Payment History
- **Transaction Table**: Comprehensive table of all past payments
- **Receipt Access**: Quick access to payment receipts
- **Payment Methods**: Track which payment method was used
- **Status Tracking**: Complete payment status history

### 📝 Quote Requests
- **Project Type Selection**: Dropdown for different service types
- **Budget Range**: Predefined budget ranges for quick selection
- **Detailed Forms**: Comprehensive project description and requirements
- **Timeline Selection**: Flexible timeline options

### 💬 Messages Module
- **Conversation List**: Organized list of all conversations
- **Real-time Chat**: Interactive chat interface with team members
- **Message History**: Complete conversation history
- **Online Status**: See who's currently available
- **File Sharing**: Support for sharing project files and documents

### ⚙️ Settings Page
- **Profile Management**: Update personal and company information
- **Password Security**: Secure password change functionality
- **Notification Preferences**: Customizable notification settings
- **Email/SMS Controls**: Toggle different types of notifications

## Design System

The dashboard follows the Frontend Mentor feedback board design specification with:

### Color Palette
- **Primary Gradient**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **Secondary Gradient**: `linear-gradient(135deg, #f093fb 0%, #f5576c 100%)`
- **Background**: `#f7f8fd`
- **Card Background**: `#ffffff`
- **Text Colors**: `#3a4374` (primary), `#647196` (secondary), `#8c92b3` (muted)
- **Accent Colors**: `#4661e6` (blue), `#ad1fea` (purple), `#f49f85` (orange)

### Typography
- **Font Family**: 'Jost' with system font fallbacks
- **Font Weights**: 400 (regular), 500 (medium), 600 (semibold), 700 (bold)
- **Font Sizes**: Responsive scale from 0.75rem to 1.875rem

### Components
- **Interactive Cards**: Hover effects with subtle shadows and transforms
- **Vote Buttons**: Animated voting system with state management
- **Progress Bars**: Smooth animated progress indicators
- **Status Badges**: Color-coded status indicators
- **Gradient Buttons**: Eye-catching call-to-action buttons

## Technical Implementation

### Built With
- **Next.js 14**: App Router for modern React development
- **TypeScript**: Type-safe development
- **Tailwind CSS**: Utility-first CSS framework
- **Heroicons**: Beautiful SVG icons
- **React Hooks**: State management and interactivity

### Component Structure
```
src/app/client-dashboard/
├── page.tsx                 # Main dashboard page
├── layout.tsx              # Dashboard layout
├── components/
│   ├── VoteButton.tsx      # Interactive voting component
│   ├── StatusBadge.tsx     # Status indicator component
│   └── ProgressBar.tsx     # Progress visualization component
└── README.md               # Documentation
```

### Key Features
- **Responsive Design**: Mobile-first approach with tablet and desktop optimizations
- **Interactive Elements**: Hover effects, animations, and state management
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Performance**: Optimized components with efficient re-rendering

## Usage

1. **Navigation**: Use the sidebar to switch between different sections
2. **Project Interaction**: Click on project cards to view details, vote on projects
3. **Payment Processing**: Select invoices and process payments securely
4. **Communication**: Use the messages module to communicate with the team
5. **Settings**: Customize your preferences and notification settings

## Responsive Behavior

- **Mobile**: Single column layout with collapsible sidebar
- **Tablet**: Optimized two-column layout
- **Desktop**: Full three-column layout with expanded sidebar

## Future Enhancements

- Real-time notifications
- File upload functionality
- Advanced filtering and search
- Export capabilities for invoices and reports
- Integration with external payment processors
- Mobile app companion

This dashboard provides a complete client management experience while maintaining the beautiful, modern aesthetic of the Frontend Mentor feedback board design.
