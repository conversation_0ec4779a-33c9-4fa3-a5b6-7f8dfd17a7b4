'use client'

import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'next/navigation'
import { ProjectList } from '@/components/clients/ProjectList'
import { ProjectListItem } from '@/lib/fetchers/project'

interface ProjectsPageState {
  projects: ProjectListItem[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function ClientProjectsPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const clientId = params.clientId as string

  const [state, setState] = useState<ProjectsPageState>({
    projects: [],
    loading: true,
    error: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0,
    },
  })

  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setFilters] = useState<any>({})

  const fetchProjects = async (page = 1, search = '', filterParams = {}) => {
    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: state.pagination.limit.toString(),
      })

      if (search) {
        params.append('search', search)
      }

      if (Object.keys(filterParams).length > 0) {
        params.append('filter', JSON.stringify(filterParams))
      }

      const response = await fetch(`/api/clients/${clientId}/projects?${params}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch projects')
      }

      const data = await response.json()

      if (data.success) {
        setState(prev => ({
          ...prev,
          projects: data.data,
          loading: false,
          pagination: {
            page: data.pagination.page,
            limit: data.pagination.limit,
            total: data.pagination.total,
            totalPages: data.pagination.totalPages,
          },
        }))
      } else {
        throw new Error(data.error || 'Failed to fetch projects')
      }
    } catch (error) {
      console.error('Error fetching projects:', error)
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'An error occurred',
      }))
    }
  }

  useEffect(() => {
    fetchProjects()
  }, [clientId])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    fetchProjects(1, query, filters)
  }

  const handleFilter = (newFilters: any) => {
    setFilters(newFilters)
    fetchProjects(1, searchQuery, newFilters)
  }

  const handlePageChange = (page: number) => {
    fetchProjects(page, searchQuery, filters)
  }

  if (state.error) {
    return (
      <div className="bg-white shadow rounded-lg p-12 text-center">
        <div className="text-red-600 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Projects</h3>
        <p className="text-sm text-gray-500 mb-4">{state.error}</p>
        <button
          onClick={() => fetchProjects()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
            <p className="mt-1 text-sm text-gray-500">
              {state.pagination.total} project{state.pagination.total !== 1 ? 's' : ''} total
            </p>
          </div>
        </div>
      </div>

      {/* Projects List */}
      <ProjectList
        projects={state.projects}
        loading={state.loading}
        onSearch={handleSearch}
        onFilter={handleFilter}
        searchQuery={searchQuery}
        filters={filters}
      />

      {/* Pagination */}
      {state.pagination.totalPages > 1 && (
        <div className="bg-white shadow rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing{' '}
              <span className="font-medium">
                {(state.pagination.page - 1) * state.pagination.limit + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(state.pagination.page * state.pagination.limit, state.pagination.total)}
              </span>{' '}
              of{' '}
              <span className="font-medium">{state.pagination.total}</span>{' '}
              results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handlePageChange(state.pagination.page - 1)}
                disabled={state.pagination.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, state.pagination.totalPages) }, (_, i) => {
                const pageNum = Math.max(1, state.pagination.page - 2) + i
                if (pageNum > state.pagination.totalPages) return null
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md ${
                      pageNum === state.pagination.page
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                )
              })}
              
              <button
                onClick={() => handlePageChange(state.pagination.page + 1)}
                disabled={state.pagination.page >= state.pagination.totalPages}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
