import { notFound } from 'next/navigation'
import { getClient } from '@/lib/fetchers/client'
import { ClientHeader } from '@/components/clients/ClientHeader'
import { ClientTabs } from '@/components/clients/ClientTabs'

interface ClientLayoutProps {
  children: React.ReactNode
  params: Promise<{ clientId: string }>
}

export default async function ClientLayout({ children, params }: ClientLayoutProps) {
  const { clientId } = await params

  // Validate clientId
  if (!clientId || isNaN(Number(clientId))) {
    notFound()
  }

  // Fetch client data
  let client
  try {
    client = await getClient(clientId)
  } catch (error) {
    console.error('Error fetching client:', error)
    notFound()
  }

  if (!client) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Client Header */}
      <ClientHeader client={client} />
      
      {/* Navigation Tabs */}
      <ClientTabs 
        clientId={clientId} 
        counts={client._count}
      />
      
      {/* Page Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  )
}

// Generate metadata for the page
export async function generateMetadata({ params }: { params: Promise<{ clientId: string }> }) {
  const { clientId } = await params
  
  try {
    const client = await getClient(clientId)
    
    if (!client) {
      return {
        title: 'Client Not Found',
        description: 'The requested client could not be found.',
      }
    }

    return {
      title: `${client.companyName} - Client Details`,
      description: `View details, projects, contracts, invoices, and payments for ${client.companyName}`,
    }
  } catch (error) {
    return {
      title: 'Client Details',
      description: 'View client information and related data.',
    }
  }
}
