import { Metada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import HeroSection from '@/components/home/<USER>';
import { ServicesSection } from '@/components/home/<USER>';
import { ProjectsSection } from '@/components/home/<USER>';
import { TestimonialsSection } from '@/components/home/<USER>';
import { TechnologiesSection } from '@/components/home/<USER>';
import { TeamSection } from '@/components/home/<USER>';
import { BlogSection } from '@/components/home/<USER>';
import { PricingSection } from '@/components/home/<USER>';
import { NewsletterSection } from '@/components/home/<USER>';
import { CTASection } from '@/components/home/<USER>';
import { ContactSection } from '@/components/home/<USER>';
import { ClientLogosSection } from '@/components/home/<USER>';

// Static data - moved to constants for better maintainability
const DEFAULT_STATS = [
  { name: 'Projects Delivered', value: '500+', iconName: 'rocket' },
  { name: 'Happy Clients', value: '200+', iconName: 'users' },
  { name: 'Years Experience', value: '10+', iconName: 'chart' },
  { name: 'Team Members', value: '50+', iconName: 'cpu' },
];

const CLIENT_LOGOS = [
  { name: 'Microsoft', logo: '/images/clients/microsoft.svg', url: '#' },
  { name: 'Google', logo: '/images/clients/google.svg', url: '#' },
  { name: 'Amazon', logo: '/images/clients/amazon.svg', url: '#' },
  { name: 'Apple', logo: '/images/clients/apple.svg', url: '#' },
  { name: 'Meta', logo: '/images/clients/meta.svg', url: '#' },
  { name: 'Netflix', logo: '/images/clients/netflix.svg', url: '#' },
];

const PRICING_PLANS = [
  {
    name: 'Startup',
    price: '$5,000',
    period: 'per project',
    description: 'Perfect for small businesses and startups',
    features: [
      'Custom web application',
      'Responsive design',
      'Basic SEO optimization',
      '3 months support',
      'Source code included'
    ],
    popular: false
  },
  {
    name: 'Business',
    price: '$15,000',
    period: 'per project',
    description: 'Ideal for growing businesses',
    features: [
      'Full-stack application',
      'Advanced UI/UX design',
      'Database integration',
      'API development',
      '6 months support',
      'Performance optimization'
    ],
    popular: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: 'quote',
    description: 'For large-scale applications',
    features: [
      'Scalable architecture',
      'MicroServices design',
      'Cloud deployment',
      'Security audit',
      '12 months support',
      'Dedicated team'
    ],
    popular: false
  }
];

// Types for better type safety
interface HomePageData {
  services: Array<{
    id: string;
    name: string;
    description?: string;
    excerpt?: string;
    category?: string;
    slug?: string;
  }>;
  projects: Array<{
    id: string;
    name: string;
    description?: string;
    excerpt?: string;
    category?: string;
    type?: string;
    slug?: string;
    imageUrl?: string;
    metrics?: Array<{ value: string; label: string }>;
  }>;
  testimonials: Array<{
    id: string;
    content: string;
    clientName: string;
    clientTitle?: string;
    clientCompany?: string;
    clientPhotoUrl?: string;
    rating?: number;
  }>;
  technologies: Array<{
    id: string;
    name: string;
    description?: string;
    category?: string;
    iconUrl?: string;
    slug?: string;
  }>;
  teamMembers: Array<{
    id: string;
    name: string;
    role: string;
    bio?: string;
    imageUrl?: string;
    socialLinks?: Record<string, string>;
  }>;
  blogPosts: Array<{
    id: string;
    title: string;
    excerpt?: string;
    slug: string;
    featuredImageUrl?: string;
    publishedAt?: string;
    createdAt: string;
    readTime?: string;
    categories?: string;
  }>;
}

// Optimized data fetching with better error handling and caching
async function getHomePageData(): Promise<HomePageData> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  
  try {
    const endpoints = [
      { url: `${baseUrl}/api/services?limit=6&filter=active`, key: 'services' },
      { url: `${baseUrl}/api/projects?featured=true&limit=3`, key: 'projects' },
      { url: `${baseUrl}/api/testimonials?limit=5`, key: 'testimonials' },
      { url: `${baseUrl}/api/technologies?filter=active&limit=8`, key: 'technologies' },
      { url: `${baseUrl}/api/team?filter=active&limit=4`, key: 'teamMembers' },
      { url: `${baseUrl}/api/blog?filter=published&limit=3`, key: 'blogPosts' },
    ];

    const fetchPromises = endpoints.map(async ({ url, key }) => {
      try {
        const response = await fetch(url, { 
          next: { revalidate: 3600 },
          headers: {
            'Accept': 'application/json',
          }
        });
        
        if (!response.ok) {
          console.warn(`Failed to fetch ${key}: ${response.status}`);
          return { key, data: [] };
        }
        
        const result = await response.json();
        return { key, data: result.data || [] };
      } catch (error) {
        console.error(`Error fetching ${key}:`, error);
        return { key, data: [] };
      }
    });

    const results = await Promise.allSettled(fetchPromises);
    
    const data: Partial<HomePageData> = {};
    
    results.forEach((result) => {
      if (result.status === 'fulfilled') {
        const { key, data: responseData } = result.value;
        
        // Transform API data to match component interfaces
        if (key === 'testimonials') {
          data.testimonials = responseData.map((item: any) => ({
            id: item.id,
            content: item.content,
            clientName: item.name || item.clientName,
            clientTitle: item.role || item.clientTitle,
            clientCompany: item.company || item.clientCompany,
            clientPhotoUrl: item.imageUrl || item.clientPhotoUrl,
            rating: item.rating
          }));
        } else if (key === 'blogPosts') {
          data.blogPosts = responseData.map((item: any) => ({
            id: item.id,
            title: item.title,
            excerpt: item.excerpt,
            slug: item.slug,
            featuredImageUrl: item.imageUrl || item.featuredImageUrl,
            publishedAt: item.publishedAt,
            createdAt: item.createdAt || item.publishedAt || new Date().toISOString(),
            readTime: item.readTime,
            categories: item.categories
          }));
        } else {
          data[key as keyof HomePageData] = responseData;
        }
      }
    });

    return {
      services: data.services || [],
      projects: data.projects || [],
      testimonials: data.testimonials || [],
      technologies: data.technologies || [],
      teamMembers: data.teamMembers || [],
      blogPosts: data.blogPosts || [],
    };
  } catch (error) {
    console.error('Failed to fetch homepage data:', error);
    return {
      services: [],
      projects: [],
      testimonials: [],
      technologies: [],
      teamMembers: [],
      blogPosts: [],
    };
  }
}

export const metadata: Metadata = {
  title: 'Technoloway - Leading Software Development Company',
  description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
  keywords: [
    'software development',
    'web development', 
    'mobile apps',
    'enterprise solutions',
    'TypeScript',
    'React',
    'Next.js',
    'full-stack development',
    'custom software',
    'digital transformation'
  ],
  authors: [{ name: 'Technoloway Team' }],
  creator: 'Technoloway',
  publisher: 'Technoloway',
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://technoloway.com'),
  openGraph: {
    title: 'Technoloway - Leading Software Development Company',
    description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
    url: '/',
    siteName: 'Technoloway',
    images: [
      {
        url: '/images/og-homepage.svg',
        width: 1200,
        height: 630,
        alt: 'Technoloway - Software Development Company',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Technoloway - Leading Software Development Company',
    description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
    images: ['/images/og-homepage.svg'],
    creator: '@technoloway',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: '/',
  },
};

export default async function HomePage() {
  const data = await getHomePageData();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main role="main" aria-label="Home page content">
        <HeroSection stats={DEFAULT_STATS} />
        <ClientLogosSection logos={CLIENT_LOGOS} />
        <ServicesSection services={data.services} />
        <ProjectsSection projects={data.projects} />
        <TestimonialsSection testimonials={data.testimonials} />
        <TechnologiesSection technologies={data.technologies} />
        <TeamSection teamMembers={data.teamMembers} />
        <BlogSection blogPosts={data.blogPosts} />
        <PricingSection plans={PRICING_PLANS} />
        <NewsletterSection />
        <CTASection />
        <ContactSection />
      </main>
      
      <Footer />
    </div>
  );
}

// Enable static generation with revalidation
export const revalidate = 3600; // Revalidate every hour