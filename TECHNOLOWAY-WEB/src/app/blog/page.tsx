'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  ArrowRightIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';

const blogPosts = [
  {
    id: 1,
    title: 'The Future of Web Development: Trends to Watch in 2024',
    excerpt: 'Explore the latest trends shaping web development, from AI integration to progressive web apps and the rise of edge computing.',
    content: 'Full article content would go here...',
    author: '<PERSON>',
    authorRole: 'Lead Developer',
    publishedAt: '2024-01-15',
    readTime: '8 min read',
    category: 'Web Development',
    tags: ['React', 'Next.js', 'AI', 'Trends'],
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
    featured: true,
  },
  {
    id: 2,
    title: 'Building Scalable Mobile Apps with React Native',
    excerpt: 'Learn best practices for creating performant and scalable mobile applications using React Native and modern development patterns.',
    content: 'Full article content would go here...',
    author: '<PERSON>',
    authorRole: 'Mobile Developer',
    publishedAt: '2024-01-10',
    readTime: '6 min read',
    category: 'Mobile Development',
    tags: ['React Native', 'Mobile', 'Performance'],
    image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=400&fit=crop',
    featured: false,
  },
  {
    id: 3,
    title: 'Cloud Architecture Best Practices for Startups',
    excerpt: 'Discover how to design cost-effective and scalable cloud infrastructure that grows with your startup.',
    content: 'Full article content would go here...',
    author: 'David Rodriguez',
    authorRole: 'Cloud Architect',
    publishedAt: '2024-01-05',
    readTime: '10 min read',
    category: 'Cloud Computing',
    tags: ['AWS', 'Architecture', 'Startups'],
    image: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=800&h=400&fit=crop',
    featured: false,
  },
  {
    id: 4,
    title: 'TypeScript Tips for Better Code Quality',
    excerpt: 'Advanced TypeScript techniques to improve code quality, maintainability, and developer experience.',
    content: 'Full article content would go here...',
    author: 'Emily Davis',
    authorRole: 'Senior Developer',
    publishedAt: '2024-01-01',
    readTime: '7 min read',
    category: 'Programming',
    tags: ['TypeScript', 'Code Quality', 'Best Practices'],
    image: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop',
    featured: false,
  },
  {
    id: 5,
    title: 'API Security: Protecting Your Digital Assets',
    excerpt: 'Essential security practices for API development, including authentication, authorization, and data protection.',
    content: 'Full article content would go here...',
    author: 'Alex Thompson',
    authorRole: 'Security Engineer',
    publishedAt: '2023-12-28',
    readTime: '9 min read',
    category: 'Security',
    tags: ['API', 'Security', 'Authentication'],
    image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=400&fit=crop',
    featured: false,
  },
  {
    id: 6,
    title: 'The Art of Code Review: Building Better Teams',
    excerpt: 'How effective code reviews can improve code quality, knowledge sharing, and team collaboration.',
    content: 'Full article content would go here...',
    author: 'Lisa Wang',
    authorRole: 'Engineering Manager',
    publishedAt: '2023-12-20',
    readTime: '5 min read',
    category: 'Team Management',
    tags: ['Code Review', 'Team', 'Collaboration'],
    image: 'https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=400&fit=crop',
    featured: false,
  },
];

const categories = ['All', 'Web Development', 'Mobile Development', 'Cloud Computing', 'Programming', 'Security', 'Team Management'];

export default function BlogPage() {
  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-white">
      <Header />
      {/* Header */}
      <header className="bg-gray-50 py-20 pt-32">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
              Our <span className="gradient-text">Blog</span>
            </h1>
            <p className="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
              Insights, tutorials, and thoughts on software development, technology trends, 
              and best practices from our team of experts.
            </p>
          </motion.div>
        </div>
      </header>

      <main className="container py-16">
        {/* Featured Post */}
        {featuredPost && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Featured Article</h2>
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <div className="relative h-64 md:h-full">
                    <Image
                      src={featuredPost.image}
                      alt={featuredPost.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      <TagIcon className="w-4 h-4 mr-1" />
                      {featuredPost.category}
                    </span>
                    <span className="text-sm text-gray-500">Featured</span>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">
                    {featuredPost.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-6">
                    {featuredPost.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center">
                        <UserIcon className="w-4 h-4 mr-1" />
                        {featuredPost.author}
                      </div>
                      <div className="flex items-center">
                        <CalendarIcon className="w-4 h-4 mr-1" />
                        {new Date(featuredPost.publishedAt).toLocaleDateString()}
                      </div>
                      <div className="flex items-center">
                        <ClockIcon className="w-4 h-4 mr-1" />
                        {featuredPost.readTime}
                      </div>
                    </div>
                    
                    <Link 
                      href={`/blog/${featuredPost.id}`}
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                    >
                      Read More
                      <ArrowRightIcon className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </motion.section>
        )}

        {/* Category Filter */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-12"
        >
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                className="px-4 py-2 rounded-full text-sm font-medium border border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-colors"
              >
                {category}
              </button>
            ))}
          </div>
        </motion.section>

        {/* Blog Posts Grid */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Latest Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow"
              >
                <div className="relative h-48">
                  <Image
                    src={post.image}
                    alt={post.title}
                    fill
                    className="object-cover"
                  />
                </div>
                
                <div className="p-6">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                      {post.category}
                    </span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                    {post.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-3">
                      <span>{post.author}</span>
                      <span>{new Date(post.publishedAt).toLocaleDateString()}</span>
                    </div>
                    <span>{post.readTime}</span>
                  </div>
                  
                  <div className="mt-4">
                    <Link 
                      href={`/blog/${post.id}`}
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium text-sm"
                    >
                      Read Article
                      <ArrowRightIcon className="w-3 h-3 ml-1" />
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
        </motion.section>

        {/* Newsletter Signup */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-20 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 text-center"
        >
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Stay Updated
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Subscribe to our newsletter to get the latest insights on software development, 
            technology trends, and best practices delivered to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button className="btn-primary whitespace-nowrap">
              Subscribe
            </button>
          </div>
        </motion.section>
      </main>
      <Footer />
    </div>
  );
}
