import { NextAuthOptions } from 'next-auth'
import Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'
import { prisma } from '@/config/prisma'
import bcrypt from 'bcryptjs'
import { AuditLogger } from '@/config/audit-log'

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        try {
          // Test database connection first
          await prisma.$connect()

          const user = await prisma.users.findUnique({
            where: {
              email: credentials.email.toLowerCase().trim()
            }
          })

          if (!user || !user.password) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              undefined,
              { email: credentials.email, reason: 'User not found or no password' }
            )
            return null
          }

          const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

          if (!isPasswordValid) {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              user.id.toString(),
              { email: credentials.email, reason: 'Invalid password' }
            )
            return null
          }

          await AuditLogger.logAuth(
            'LOGIN_SUCCESS',
            user.id.toString(),
            { email: credentials.email }
          )

          return {
            id: user.id.toString(),
            email: user.email,
            name: `${user.firstname} ${user.lastname}`.trim(),
            role: user.role,
          }
        } catch (error) {
          console.error('Auth error details:', {
            error: error instanceof Error ? error.message : 'Unknown error',
            stack: error instanceof Error ? error.stack : undefined,
            email: credentials.email
          })

          // Try to log the error, but don't fail if audit logging fails
          try {
            await AuditLogger.logAuth(
              'LOGIN_FAILED',
              undefined,
              { email: credentials.email, error: error instanceof Error ? error.message : 'Unknown error' }
            )
          } catch (auditError) {
            console.error('Audit logging failed:', auditError)
          }

          return null
        }
      }
    })
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.id = user.id
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.role = token.role as string
      }
      return session
    },
  },
  pages: {
    signIn: '/client-auth/signin',
    error: '/client-auth/signin',
  },
  debug: false, // Disable debug mode to reduce console warnings
  logger: {
    error(code, metadata) {
      console.error('NextAuth Error:', { code, metadata })
    },
    warn(code) {
      console.warn('NextAuth Warning:', code)
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === 'development') {
        console.log('NextAuth Debug:', { code, metadata })
      }
    }
  },
}
