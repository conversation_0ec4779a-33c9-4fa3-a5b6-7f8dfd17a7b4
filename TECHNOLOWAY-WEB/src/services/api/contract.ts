import { prisma } from '@/config/prisma'

export interface ContractListItem {
  id: string | number
  contName: string
  contStatus?: string
  contValue?: number
  contValueCurr?: string
  billingType?: string
  nextBillDate?: string
  contSignedDate?: string
  contExecutedDate?: string
  contExpiryDate?: string
  createdAt: string
  updatedAt?: string
  project?: {
    id: string | number
    name: string
  }
  order?: {
    id: string | number
    orderTitle: string
  }
}

export interface ContractDetails extends ContractListItem {
  contServType?: string
  contLang?: string
  agreementDesc?: string
  contSignMethod?: string
  lastUpdateDate?: string
  contFile?: string
  fileUploadDate?: string
  comments?: string
  notes?: string
  contManager?: {
    id: string | number
    name: string
    position: string
  }
  lastUpdateUser?: {
    id: string | number
    name: string
    position: string
  }
  invoices: Array<{
    id: string | number
    totalAmount: number
    status: string
    dueDate: string
    description?: string
    createdAt: string
  }>
}

/**
 * Fetch contracts for a specific client
 */
export async function getClientContracts(
  clientId: string | number,
  options: {
    page?: number
    limit?: number
    search?: string
    status?: string
  } = {}
): Promise<{ contracts: ContractListItem[]; total: number }> {
  try {
    const { page = 1, limit = 10, search, status } = options
    const skip = (page - 1) * limit

    const where: any = {
      clientid: Number(clientId),
    }

    if (search) {
      where.OR = [
        { contname: { contains: search, mode: 'insensitive' } },
        { agreementdesc: { contains: search, mode: 'insensitive' } },
        { comments: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.contstatus = status
    }

    const [contracts, total] = await Promise.all([
      prisma.contracts.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdat: 'desc',
        },
        include: {
          projects: {
            select: {
              id: true,
              name: true,
            },
          },
          orders: {
            select: {
              id: true,
              ordertitle: true,
            },
          },
        },
      }),
      prisma.contracts.count({ where }),
    ])

    return {
      contracts: contracts.map(contract => ({
        id: contract.id.toString(),
        contName: contract.contname,
        contStatus: contract.contstatus || undefined,
        contValue: contract.contvalue || undefined,
        contValueCurr: contract.contvaluecurr || undefined,
        billingType: contract.billingtype || undefined,
        nextBillDate: contract.nextbilldate?.toISOString(),
        contSignedDate: contract.contsigneddate?.toISOString(),
        contExecutedDate: contract.contexecuteddate?.toISOString(),
        contExpiryDate: contract.contexpirydate?.toISOString(),
        createdAt: contract.createdat?.toISOString() || new Date().toISOString(),
        updatedAt: contract.updatedat?.toISOString(),
        project: contract.projects ? {
          id: contract.projects.id.toString(),
          name: contract.projects.name,
        } : undefined,
        order: contract.orders ? {
          id: contract.orders.id.toString(),
          orderTitle: contract.orders.ordertitle,
        } : undefined,
      })),
      total,
    }
  } catch (error) {
    console.error('Error fetching client contracts:', error)
    throw new Error('Failed to fetch client contracts')
  }
}

/**
 * Fetch detailed information for a specific contract
 */
export async function getContractDetails(contractId: string | number): Promise<ContractDetails | null> {
  try {
    const contract = await prisma.contracts.findUnique({
      where: { id: Number(contractId) },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
          },
        },
        orders: {
          select: {
            id: true,
            ordertitle: true,
          },
        },
        teammembers_contracts_contmanagerToteammembers: {
          select: {
            id: true,
            name: true,
            position: true,
          },
        },
        teammembers_contracts_lastupdateuserToteammembers: {
          select: {
            id: true,
            name: true,
            position: true,
          },
        },
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            duedate: true,
            description: true,
            createdat: true,
          },
          orderBy: {
            createdat: 'desc',
          },
        },
      },
    })

    if (!contract) {
      return null
    }

    return {
      id: contract.id.toString(),
      contName: contract.contname,
      contStatus: contract.contstatus || undefined,
      contServType: contract.contservtype || undefined,
      contLang: contract.contlang || undefined,
      agreementDesc: contract.agreementdesc || undefined,
      contValue: contract.contvalue || undefined,
      contValueCurr: contract.contvaluecurr || undefined,
      billingType: contract.billingtype || undefined,
      nextBillDate: contract.nextbilldate?.toISOString(),
      contSignMethod: contract.contsignmethod || undefined,
      contSignedDate: contract.contsigneddate?.toISOString(),
      contExecutedDate: contract.contexecuteddate?.toISOString(),
      contExpiryDate: contract.contexpirydate?.toISOString(),
      lastUpdateDate: contract.lastupdatedate?.toISOString(),
      contFile: contract.contfile || undefined,
      fileUploadDate: contract.fileuploaddate?.toISOString(),
      comments: contract.comments || undefined,
      notes: contract.notes || undefined,
      createdAt: contract.createdat?.toISOString() || new Date().toISOString(),
      updatedAt: contract.updatedat?.toISOString(),
      project: contract.projects ? {
        id: contract.projects.id.toString(),
        name: contract.projects.name,
      } : undefined,
      order: contract.orders ? {
        id: contract.orders.id.toString(),
        orderTitle: contract.orders.ordertitle,
      } : undefined,
      contManager: contract.teammembers_contracts_contmanagerToteammembers ? {
        id: contract.teammembers_contracts_contmanagerToteammembers.id.toString(),
        name: contract.teammembers_contracts_contmanagerToteammembers.name,
        position: contract.teammembers_contracts_contmanagerToteammembers.position,
      } : undefined,
      lastUpdateUser: contract.teammembers_contracts_lastupdateuserToteammembers ? {
        id: contract.teammembers_contracts_lastupdateuserToteammembers.id.toString(),
        name: contract.teammembers_contracts_lastupdateuserToteammembers.name,
        position: contract.teammembers_contracts_lastupdateuserToteammembers.position,
      } : undefined,
      invoices: contract.invoices.map(invoice => ({
        id: invoice.id.toString(),
        totalAmount: Number(invoice.totalamount),
        status: invoice.status,
        dueDate: invoice.duedate.toISOString(),
        description: invoice.description || undefined,
        createdAt: invoice.createdat.toISOString(),
      })),
    }
  } catch (error) {
    console.error('Error fetching contract details:', error)
    throw new Error('Failed to fetch contract details')
  }
}

/**
 * Get contract status options
 */
export function getContractStatusOptions() {
  return [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'CANCELLED', label: 'Cancelled' },
    { value: 'EXPIRED', label: 'Expired' },
  ]
}
