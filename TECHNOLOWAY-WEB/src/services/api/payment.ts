import { prisma } from '@/config/prisma'

export interface PaymentListItem {
  id: string | number
  amount: number
  paymentDate: string
  paymentMethod: string
  status: string
  notes?: string
  createdAt: string
  updatedAt?: string
  invoice: {
    id: string | number
    totalAmount: number
    status: string
    dueDate: string
    description?: string
  }
}

export interface PaymentDetails extends PaymentListItem {
  invoice: {
    id: string | number
    totalAmount: number
    subtotal?: number
    taxRate: number
    taxAmount: number
    status: string
    dueDate: string
    description?: string
    contract?: {
      id: string | number
      contName: string
    }
    project?: {
      id: string | number
      name: string
    }
  }
}

/**
 * Fetch payments for a specific client (through their invoices)
 */
export async function getClientPayments(
  clientId: string | number,
  options: {
    page?: number
    limit?: number
    search?: string
    status?: string
    paymentMethod?: string
    dateFrom?: string
    dateTo?: string
  } = {}
): Promise<{ payments: PaymentListItem[]; total: number }> {
  try {
    const { page = 1, limit = 10, search, status, paymentMethod, dateFrom, dateTo } = options
    const skip = (page - 1) * limit

    const where: any = {
      invoices: {
        clientid: Number(clientId),
      },
    }

    if (search) {
      where.OR = [
        { notes: { contains: search, mode: 'insensitive' } },
        { paymentmethod: { contains: search, mode: 'insensitive' } },
        { invoices: { description: { contains: search, mode: 'insensitive' } } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (paymentMethod) {
      where.paymentmethod = paymentMethod
    }

    if (dateFrom || dateTo) {
      where.paymentdate = {}
      if (dateFrom) {
        where.paymentdate.gte = new Date(dateFrom)
      }
      if (dateTo) {
        where.paymentdate.lte = new Date(dateTo)
      }
    }

    const [payments, total] = await Promise.all([
      prisma.payments.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
          createdat: 'desc',
        },
        include: {
          invoices: {
            select: {
              id: true,
              totalamount: true,
              status: true,
              duedate: true,
              description: true,
            },
          },
        },
      }),
      prisma.payments.count({ where }),
    ])

    return {
      payments: payments.map(payment => ({
        id: payment.id.toString(),
        amount: Number(payment.amount),
        paymentDate: payment.paymentdate.toISOString(),
        paymentMethod: payment.paymentmethod,
        status: payment.status,
        notes: payment.notes || undefined,
        createdAt: payment.createdat.toISOString(),
        updatedAt: payment.updatedat?.toISOString(),
        invoice: {
          id: payment.invoices.id.toString(),
          totalAmount: Number(payment.invoices.totalamount),
          status: payment.invoices.status,
          dueDate: payment.invoices.duedate.toISOString(),
          description: payment.invoices.description || undefined,
        },
      })),
      total,
    }
  } catch (error) {
    console.error('Error fetching client payments:', error)
    throw new Error('Failed to fetch client payments')
  }
}

/**
 * Fetch detailed information for a specific payment
 */
export async function getPaymentDetails(paymentId: string | number): Promise<PaymentDetails | null> {
  try {
    const payment = await prisma.payments.findUnique({
      where: { id: Number(paymentId) },
      include: {
        invoices: {
          include: {
            contracts: {
              select: {
                id: true,
                contname: true,
              },
            },
            projects: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    })

    if (!payment) {
      return null
    }

    return {
      id: payment.id.toString(),
      amount: Number(payment.amount),
      paymentDate: payment.paymentdate.toISOString(),
      paymentMethod: payment.paymentmethod,
      status: payment.status,
      notes: payment.notes || undefined,
      createdAt: payment.createdat.toISOString(),
      updatedAt: payment.updatedat?.toISOString(),
      invoice: {
        id: payment.invoices.id.toString(),
        totalAmount: Number(payment.invoices.totalamount),
        subtotal: payment.invoices.subtotal ? Number(payment.invoices.subtotal) : undefined,
        taxRate: Number(payment.invoices.taxrate),
        taxAmount: Number(payment.invoices.taxamount),
        status: payment.invoices.status,
        dueDate: payment.invoices.duedate.toISOString(),
        description: payment.invoices.description || undefined,
        contract: payment.invoices.contracts ? {
          id: payment.invoices.contracts.id.toString(),
          contName: payment.invoices.contracts.contname,
        } : undefined,
        project: payment.invoices.projects ? {
          id: payment.invoices.projects.id.toString(),
          name: payment.invoices.projects.name,
        } : undefined,
      },
    }
  } catch (error) {
    console.error('Error fetching payment details:', error)
    throw new Error('Failed to fetch payment details')
  }
}

/**
 * Get payment status options
 */
export function getPaymentStatusOptions() {
  return [
    { value: 'Completed', label: 'Completed' },
    { value: 'Pending', label: 'Pending' },
    { value: 'Failed', label: 'Failed' },
    { value: 'Refunded', label: 'Refunded' },
    { value: 'Cancelled', label: 'Cancelled' },
  ]
}

/**
 * Get payment method options
 */
export function getPaymentMethodOptions() {
  return [
    { value: 'Credit Card', label: 'Credit Card' },
    { value: 'Bank Transfer', label: 'Bank Transfer' },
    { value: 'PayPal', label: 'PayPal' },
    { value: 'Check', label: 'Check' },
    { value: 'Cash', label: 'Cash' },
    { value: 'Wire Transfer', label: 'Wire Transfer' },
    { value: 'ACH', label: 'ACH' },
  ]
}

/**
 * Calculate payment totals and statistics for a client
 */
export async function getClientPaymentStats(clientId: string | number) {
  try {
    const stats = await prisma.payments.aggregate({
      where: {
        invoices: {
          clientid: Number(clientId),
        },
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    })

    const statusCounts = await prisma.payments.groupBy({
      by: ['status'],
      where: {
        invoices: {
          clientid: Number(clientId),
        },
      },
      _count: {
        id: true,
      },
    })

    const methodCounts = await prisma.payments.groupBy({
      by: ['paymentmethod'],
      where: {
        invoices: {
          clientid: Number(clientId),
        },
      },
      _count: {
        id: true,
      },
    })

    return {
      totalAmount: Number(stats._sum.amount || 0),
      totalPayments: stats._count.id,
      statusBreakdown: statusCounts.reduce((acc, item) => {
        acc[item.status] = item._count.id
        return acc
      }, {} as Record<string, number>),
      methodBreakdown: methodCounts.reduce((acc, item) => {
        acc[item.paymentmethod] = item._count.id
        return acc
      }, {} as Record<string, number>),
    }
  } catch (error) {
    console.error('Error fetching client payment stats:', error)
    throw new Error('Failed to fetch client payment stats')
  }
}

/**
 * Get recent payments for a client
 */
export async function getRecentClientPayments(
  clientId: string | number,
  limit: number = 5
): Promise<PaymentListItem[]> {
  try {
    const payments = await prisma.payments.findMany({
      where: {
        invoices: {
          clientid: Number(clientId),
        },
      },
      take: limit,
      orderBy: {
        createdat: 'desc',
      },
      include: {
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            duedate: true,
            description: true,
          },
        },
      },
    })

    return payments.map(payment => ({
      id: payment.id.toString(),
      amount: Number(payment.amount),
      paymentDate: payment.paymentdate.toISOString(),
      paymentMethod: payment.paymentmethod,
      status: payment.status,
      notes: payment.notes || undefined,
      createdAt: payment.createdat.toISOString(),
      updatedAt: payment.updatedat?.toISOString(),
      invoice: {
        id: payment.invoices.id.toString(),
        totalAmount: Number(payment.invoices.totalamount),
        status: payment.invoices.status,
        dueDate: payment.invoices.duedate.toISOString(),
        description: payment.invoices.description || undefined,
      },
    }))
  } catch (error) {
    console.error('Error fetching recent client payments:', error)
    throw new Error('Failed to fetch recent client payments')
  }
}
