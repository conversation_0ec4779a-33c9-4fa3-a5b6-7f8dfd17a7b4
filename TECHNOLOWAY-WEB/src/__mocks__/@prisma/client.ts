// Manual mock for @prisma/client
export const PrismaClient = jest.fn().mockImplementation(() => ({
  contactforms: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  users: {
    findFirst: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  $disconnect: jest.fn(),
}))

export const prisma = new PrismaClient()

// Mock Prisma namespace
export const Prisma = {
  PrismaClientKnownRequestError: class PrismaClientKnownRequestError extends Error {
    code: string
    constructor(message: string, code: string) {
      super(message)
      this.code = code
      this.name = 'PrismaClientKnownRequestError'
    }
  },
  PrismaClientUnknownRequestError: class PrismaClientUnknownRequestError extends Error {
    constructor(message: string) {
      super(message)
      this.name = 'PrismaClientUnknownRequestError'
    }
  },
  PrismaClientValidationError: class PrismaClientValidationError extends Error {
    constructor(message: string) {
      super(message)
      this.name = 'PrismaClientValidationError'
    }
  },
}
