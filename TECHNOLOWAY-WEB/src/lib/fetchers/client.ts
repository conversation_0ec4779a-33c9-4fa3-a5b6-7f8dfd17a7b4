import { prisma } from '@/config/prisma'

// Re-export types from services/api
export type { ClientWithCounts, ClientDetails } from '@/services/api/client'

/**
 * Check if a client exists by ID
 */
export async function clientExists(clientId: string): Promise<boolean> {
  try {
    const client = await prisma.clients.findUnique({
      where: { id: parseInt(clientId) },
      select: { id: true }
    })
    return !!client
  } catch (error) {
    console.error('Error checking client existence:', error)
    return false
  }
}

/**
 * Get client by ID
 */
export async function getClient(clientId: string) {
  return await prisma.clients.findUnique({
    where: { id: parseInt(clientId) }
  })
}

/**
 * Get client details by ID (alias for getClient)
 */
export async function getClientDetails(clientId: string) {
  return await getClient(clientId)
}

/**
 * Get all clients with optional pagination and search
 */
export async function getClients(options: {
  page?: number
  limit?: number
  search?: string
  status?: string
} = {}) {
  const { page = 1, limit = 10, search, status } = options
  const skip = (page - 1) * limit

  const where: any = {}
  
  if (search) {
    where.OR = [
      { companyname: { contains: search, mode: 'insensitive' } },
      { contactemail: { contains: search, mode: 'insensitive' } },
      { contactphone: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.isactive = status === 'active'
  }

  const [clients, total] = await Promise.all([
    prisma.clients.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdat: 'desc' }
    }),
    prisma.clients.count({ where })
  ])

  return { clients, total }
} 