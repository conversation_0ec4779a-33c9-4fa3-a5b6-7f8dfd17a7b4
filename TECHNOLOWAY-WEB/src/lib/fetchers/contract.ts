import { prisma } from '@/config/prisma'

// Re-export types from services/api
export type { ContractListItem, ContractDetails } from '@/services/api/contract'

/**
 * Get contracts for a specific client
 */
export async function getClientContracts(clientId: string, options: {
  page?: number
  limit?: number
  search?: string
  status?: string
} = {}) {
  const { page = 1, limit = 10, search, status } = options
  const skip = (page - 1) * limit

  const where: any = {
    clientid: parseInt(clientId)
  }
  
  if (search) {
    where.OR = [
      { contname: { contains: search, mode: 'insensitive' } },
      { agreementdesc: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.contstatus = status
  }

  const [contracts, total] = await Promise.all([
    prisma.contracts.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdat: 'desc' },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactemail: true
          }
        }
      }
    }),
    prisma.contracts.count({ where })
  ])

  return { contracts, total }
}

/**
 * Get contract status options
 */
export function getContractStatusOptions() {
  return [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'CANCELLED', label: 'Cancelled' },
    { value: 'EXPIRED', label: 'Expired' }
  ]
}

/**
 * Get contract by ID
 */
export async function getContract(contractId: string) {
  return await prisma.contracts.findUnique({
    where: { id: parseInt(contractId) },
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactemail: true,
          contactphone: true
        }
      }
    }
  })
} 