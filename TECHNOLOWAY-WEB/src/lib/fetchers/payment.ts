import { prisma } from '@/config/prisma'

// Re-export types from services/api
export type { PaymentListItem, PaymentDetails } from '@/services/api/payment'

/**
 * Get payments for a specific client
 */
export async function getClientPayments(clientId: string, options: {
  page?: number
  limit?: number
  search?: string
  status?: string
  paymentMethod?: string
  dateFrom?: string
  dateTo?: string
} = {}) {
  const { page = 1, limit = 10, search, status, paymentMethod, dateFrom, dateTo } = options
  const skip = (page - 1) * limit

  const where: any = {
    invoices: {
      clientid: parseInt(clientId)
    }
  }
  
  if (search) {
    where.OR = [
      { id: { equals: isNaN(parseInt(search)) ? undefined : parseInt(search) } },
      { reference: { contains: search, mode: 'insensitive' } },
      { transactionid: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.status = status
  }

  if (paymentMethod) {
    where.paymentmethod = paymentMethod
  }

  if (dateFrom || dateTo) {
    where.paymentdate = {}
    if (dateFrom) {
      where.paymentdate.gte = new Date(dateFrom)
    }
    if (dateTo) {
      where.paymentdate.lte = new Date(dateTo)
    }
  }

  const [payments, total] = await Promise.all([
    prisma.payments.findMany({
      where,
      skip,
      take: limit,
      orderBy: { paymentdate: 'desc' },
      include: {
        invoices: {
          select: {
            id: true,
            totalamount: true,
            status: true,
            clients: {
              select: {
                id: true,
                companyname: true,
                contactemail: true
              }
            }
          }
        }
      }
    }),
    prisma.payments.count({ where })
  ])

  return { payments, total }
}

/**
 * Get payment status options
 */
export function getPaymentStatusOptions() {
  return [
    { value: 'PENDING', label: 'Pending' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'FAILED', label: 'Failed' },
    { value: 'CANCELLED', label: 'Cancelled' },
    { value: 'REFUNDED', label: 'Refunded' }
  ]
}

/**
 * Get payment method options
 */
export function getPaymentMethodOptions() {
  return [
    { value: 'CREDIT_CARD', label: 'Credit Card' },
    { value: 'DEBIT_CARD', label: 'Debit Card' },
    { value: 'BANK_TRANSFER', label: 'Bank Transfer' },
    { value: 'PAYPAL', label: 'PayPal' },
    { value: 'STRIPE', label: 'Stripe' },
    { value: 'CASH', label: 'Cash' },
    { value: 'CHECK', label: 'Check' }
  ]
}

/**
 * Get payment statistics for a client
 */
export async function getClientPaymentStats(clientId: string) {
  const clientIdNum = parseInt(clientId)
  
  const [totalPayments, completedPayments, totalAmount, completedAmount] = await Promise.all([
    prisma.payments.count({
      where: {
        invoices: {
          clientid: clientIdNum
        }
      }
    }),
    prisma.payments.count({
      where: {
        invoices: {
          clientid: clientIdNum
        },
        status: 'COMPLETED'
      }
    }),
    prisma.payments.aggregate({
      where: {
        invoices: {
          clientid: clientIdNum
        }
      },
      _sum: { amount: true }
    }),
    prisma.payments.aggregate({
      where: {
        invoices: {
          clientid: clientIdNum
        },
        status: 'COMPLETED'
      },
      _sum: { amount: true }
    })
  ])

  return {
    totalPayments,
    completedPayments,
    pendingPayments: totalPayments - completedPayments,
    totalAmount: Number(totalAmount._sum.amount) || 0,
    completedAmount: Number(completedAmount._sum.amount) || 0,
    pendingAmount: Number(totalAmount._sum.amount || 0) - Number(completedAmount._sum.amount || 0)
  }
}

/**
 * Get recent payments for a client
 */
export async function getRecentClientPayments(clientId: string, limit: number = 5) {
  return await prisma.payments.findMany({
    where: {
      invoices: {
        clientid: parseInt(clientId)
      }
    },
    take: limit,
    orderBy: { paymentdate: 'desc' },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true,
          clients: {
            select: {
              id: true,
              companyname: true
            }
          }
        }
      }
    }
  })
}

/**
 * Get payment by ID
 */
export async function getPayment(paymentId: string) {
  return await prisma.payments.findUnique({
    where: { id: parseInt(paymentId) },
    include: {
      invoices: {
        select: {
          id: true,
          totalamount: true,
          status: true,
          clients: {
            select: {
              id: true,
              companyname: true,
              contactemail: true,
              contactphone: true
            }
          }
        }
      }
    }
  })
} 