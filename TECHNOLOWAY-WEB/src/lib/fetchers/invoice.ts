import { prisma } from '@/config/prisma'

// Re-export types from services/api
export type { InvoiceListItem, InvoiceDetails } from '@/services/api/invoice'

/**
 * Get invoices for a specific client
 */
export async function getClientInvoices(clientId: string, options: {
  page?: number
  limit?: number
  search?: string
  status?: string
  dateFrom?: string
  dateTo?: string
} = {}) {
  const { page = 1, limit = 10, search, status, dateFrom, dateTo } = options
  const skip = (page - 1) * limit

  const where: any = {
    clientid: parseInt(clientId)
  }
  
  if (search) {
    where.OR = [
      { id: { equals: isNaN(parseInt(search)) ? undefined : parseInt(search) } },
      { description: { contains: search, mode: 'insensitive' } }
    ]
  }

  if (status) {
    where.status = status
  }

  if (dateFrom || dateTo) {
    where.createdat = {}
    if (dateFrom) {
      where.createdat.gte = new Date(dateFrom)
    }
    if (dateTo) {
      where.createdat.lte = new Date(dateTo)
    }
  }

  const [invoices, total] = await Promise.all([
    prisma.invoices.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdat: 'desc' },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactemail: true
          }
        }
      }
    }),
    prisma.invoices.count({ where })
  ])

  return { invoices, total }
}

/**
 * Get invoice status options
 */
export function getInvoiceStatusOptions() {
  return [
    { value: 'DRAFT', label: 'Draft' },
    { value: 'SENT', label: 'Sent' },
    { value: 'PAID', label: 'Paid' },
    { value: 'OVERDUE', label: 'Overdue' },
    { value: 'CANCELLED', label: 'Cancelled' }
  ]
}

/**
 * Get invoice statistics for a client
 */
export async function getClientInvoiceStats(clientId: string) {
  const clientIdNum = parseInt(clientId)
  
  const [totalInvoices, paidInvoices, overdueInvoices, totalAmount, paidAmount] = await Promise.all([
    prisma.invoices.count({ where: { clientid: clientIdNum } }),
    prisma.invoices.count({ where: { clientid: clientIdNum, status: 'PAID' } }),
    prisma.invoices.count({ where: { clientid: clientIdNum, status: 'OVERDUE' } }),
    prisma.invoices.aggregate({
      where: { clientid: clientIdNum },
      _sum: { totalamount: true }
    }),
    prisma.invoices.aggregate({
      where: { clientid: clientIdNum, status: 'PAID' },
      _sum: { totalamount: true }
    })
  ])

  return {
    totalInvoices,
    paidInvoices,
    overdueInvoices,
    pendingInvoices: totalInvoices - paidInvoices - overdueInvoices,
    totalAmount: Number(totalAmount._sum.totalamount) || 0,
    paidAmount: Number(paidAmount._sum.totalamount) || 0,
    outstandingAmount: Number(totalAmount._sum.totalamount || 0) - Number(paidAmount._sum.totalamount || 0)
  }
}

/**
 * Get invoice by ID
 */
export async function getInvoice(invoiceId: string) {
  return await prisma.invoices.findUnique({
    where: { id: parseInt(invoiceId) },
    include: {
      clients: {
        select: {
          id: true,
          companyname: true,
          contactemail: true,
          contactphone: true
        }
      }
    }
  })
} 