import nodemailer from 'nodemailer'

export interface EmailOptions {
  to: string
  subject: string
  html: string
  text?: string
  from?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

export interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null

  constructor() {
    this.initializeTransporter()
  }

  private initializeTransporter() {
    try {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER || '',
          pass: process.env.SMTP_PASS || ''
        }
      })
    } catch (error) {
      console.error('Failed to initialize email transporter:', error)
      this.transporter = null
    }
  }

  async sendEmail(options: EmailOptions): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.transporter) {
        return {
          success: false,
          message: 'Email service not configured'
        }
      }

      const mailOptions = {
        from: options.from || process.env.SMTP_FROM || process.env.SMTP_USER,
        to: options.to,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      
      return {
        success: true,
        message: `Email sent successfully to ${options.to}`
      }
    } catch (error) {
      console.error('Error sending email:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Failed to send email'
      }
    }
  }

  async sendContactFormNotification(formData: {
    name: string
    email: string
    subject: string
    message: string
  }): Promise<{ success: boolean; message: string }> {
    const html = `
      <h2>New Contact Form Submission</h2>
      <p><strong>Name:</strong> ${formData.name}</p>
      <p><strong>Email:</strong> ${formData.email}</p>
      <p><strong>Subject:</strong> ${formData.subject}</p>
      <p><strong>Message:</strong></p>
      <p>${formData.message}</p>
    `

    return this.sendEmail({
      to: process.env.CONTACT_EMAIL || process.env.SMTP_USER || '',
      subject: `New Contact Form: ${formData.subject}`,
      html
    })
  }

  async sendContactFormReply(to: string, replyMessage: string, originalMessage: string): Promise<{ success: boolean; message: string }> {
    const html = `
      <h2>Response to Your Inquiry</h2>
      <p>Thank you for contacting us. Here is our response:</p>
      <div style="background-color: #f5f5f5; padding: 15px; margin: 15px 0; border-left: 4px solid #007bff;">
        ${replyMessage}
      </div>
      <hr>
      <h3>Your Original Message:</h3>
      <div style="background-color: #f9f9f9; padding: 10px; margin: 10px 0; font-style: italic;">
        ${originalMessage}
      </div>
      <p>If you have any further questions, please don't hesitate to contact us.</p>
    `

    return this.sendEmail({
      to,
      subject: 'Response to Your Inquiry',
      html
    })
  }

  async sendPasswordReset(to: string, resetToken: string): Promise<{ success: boolean; message: string }> {
    const resetUrl = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${resetToken}`
    
    const html = `
      <h2>Password Reset Request</h2>
      <p>You requested a password reset for your account.</p>
      <p>Click the link below to reset your password:</p>
      <a href="${resetUrl}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;">
        Reset Password
      </a>
      <p>If you didn't request this, please ignore this email.</p>
      <p>This link will expire in 1 hour.</p>
    `

    return this.sendEmail({
      to,
      subject: 'Password Reset Request',
      html
    })
  }

  async sendWelcomeEmail(to: string, name: string): Promise<{ success: boolean; message: string }> {
    const html = `
      <h2>Welcome to Technoloway!</h2>
      <p>Hello ${name},</p>
      <p>Thank you for creating an account with us. We're excited to have you on board!</p>
      <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
      <p>Best regards,<br>The Technoloway Team</p>
    `

    return this.sendEmail({
      to,
      subject: 'Welcome to Technoloway',
      html
    })
  }

  async sendInvoiceNotification(to: string, invoiceData: {
    invoiceNumber: string
    amount: string
    dueDate: string
    downloadUrl?: string
  }): Promise<{ success: boolean; message: string }> {
    const html = `
      <h2>Invoice Generated</h2>
      <p>Your invoice has been generated successfully.</p>
      <div style="background-color: #f5f5f5; padding: 15px; margin: 15px 0; border-radius: 5px;">
        <p><strong>Invoice Number:</strong> ${invoiceData.invoiceNumber}</p>
        <p><strong>Amount:</strong> ${invoiceData.amount}</p>
        <p><strong>Due Date:</strong> ${invoiceData.dueDate}</p>
      </div>
      ${invoiceData.downloadUrl ? `<p><a href="${invoiceData.downloadUrl}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">Download Invoice</a></p>` : ''}
      <p>Thank you for your business!</p>
    `

    return this.sendEmail({
      to,
      subject: `Invoice ${invoiceData.invoiceNumber} Generated`,
      html
    })
  }
}

// Export singleton instance
export const emailService = new EmailService()

// Export convenience functions
export const sendEmail = (options: EmailOptions) => emailService.sendEmail(options)
export const sendContactFormNotification = (formData: any) => emailService.sendContactFormNotification(formData)
export const sendContactFormReply = (to: string, replyMessage: string, originalMessage: string) => 
  emailService.sendContactFormReply(to, replyMessage, originalMessage)
export const sendPasswordReset = (to: string, resetToken: string) => emailService.sendPasswordReset(to, resetToken)
export const sendWelcomeEmail = (to: string, name: string) => emailService.sendWelcomeEmail(to, name)
export const sendInvoiceNotification = (to: string, invoiceData: any) => emailService.sendInvoiceNotification(to, invoiceData) 