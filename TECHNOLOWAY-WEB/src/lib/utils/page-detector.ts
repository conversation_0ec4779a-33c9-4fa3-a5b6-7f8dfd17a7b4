export interface PageSection {
  id: string
  type: 'hero' | 'content' | 'stats' | 'features' | 'process' | 'cta' | 'contact' | 'form' | 'grid' | 'list'
  title: string
  subtitle?: string
  description?: string
  fields: Record<string, any>
  slides?: Array<{
    id: string
    title: string
    subtitle: string
    buttonText: string
    buttonUrl: string
    imageUrl: string
    displayOrder: number
    isActive: boolean
  }>
  isActive: boolean
  displayOrder: number
}

export interface PublicPage {
  id: string
  name: string
  slug: string
  path: string
  title: string
  metaDescription?: string
  sections: PageSection[]
  icon: string
}

// Define all public pages and their sections
export const PUBLIC_PAGES: PublicPage[] = [
  {
    id: 'home',
    name: 'Home',
    slug: 'home',
    path: '/',
    title: 'Home Page',
    metaDescription: 'Technoloway - Leading Software Development Company',
    icon: 'HomeIcon',
    sections: [
      {
        id: 'home-hero',
        type: 'hero',
        title: 'Hero Section',
        subtitle: 'Main hero carousel with slides',
        description: 'The main hero section with carousel slides and call-to-action',
        fields: {
          sectionTitle: 'Transform Your Ideas Into Reality',
          sectionSubtitle: 'Leading Software Development Company',
          sectionDescription: 'We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.'
        },
        slides: [
          {
            id: 'home-slide-1',
            title: 'Transform Your Ideas Into Reality',
            subtitle: 'Leading Software Development Company',
            buttonText: 'Get Started',
            buttonUrl: '/contact',
            imageUrl: '/images/hero/hero-1.jpg',
            displayOrder: 1,
            isActive: true
          },
          {
            id: 'home-slide-2',
            title: 'Cutting-Edge Technology Solutions',
            subtitle: 'Web, Mobile & Enterprise Development',
            buttonText: 'View Our Work',
            buttonUrl: '/projects',
            imageUrl: '/images/hero/hero-2.jpg',
            displayOrder: 2,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'home-client-logos',
        type: 'content',
        title: 'Client Logos',
        subtitle: 'Trusted by leading companies',
        description: 'Display client logos and testimonials',
        fields: {
          sectionTitle: 'Trusted by Leading Companies',
          sectionSubtitle: 'Our clients include Fortune 500 companies and innovative startups',
          sectionDescription: 'We\'ve helped businesses of all sizes achieve their digital transformation goals.'
        },
        isActive: true,
        displayOrder: 2
      },
      {
        id: 'home-services',
        type: 'grid',
        title: 'Services Section',
        subtitle: 'What we offer',
        description: 'Showcase our main services',
        fields: {
          sectionTitle: 'Our Services',
          sectionSubtitle: 'What We Offer',
          sectionDescription: 'Comprehensive software development services to help your business thrive in the digital age.'
        },
        isActive: true,
        displayOrder: 3
      },
      {
        id: 'home-projects',
        type: 'grid',
        title: 'Featured Projects',
        subtitle: 'Our latest work',
        description: 'Showcase featured projects',
        fields: {
          sectionTitle: 'Featured Projects',
          sectionSubtitle: 'Our Latest Work',
          sectionDescription: 'Discover our recent projects and success stories'
        },
        isActive: true,
        displayOrder: 4
      },
      {
        id: 'home-testimonials',
        type: 'content',
        title: 'Client Testimonials',
        subtitle: 'What our clients say',
        description: 'Display client testimonials and reviews',
        fields: {
          sectionTitle: 'Client Testimonials',
          sectionSubtitle: 'What Our Clients Say',
          sectionDescription: 'Read what our clients have to say about working with us'
        },
        isActive: true,
        displayOrder: 5
      },
      {
        id: 'home-technologies',
        type: 'grid',
        title: 'Technologies We Use',
        subtitle: 'Our tech stack',
        description: 'Showcase technologies and tools',
        fields: {
          sectionTitle: 'Technologies We Use',
          sectionSubtitle: 'Our Tech Stack',
          sectionDescription: 'We use the latest technologies to build exceptional solutions'
        },
        isActive: true,
        displayOrder: 6
      },
      {
        id: 'home-team',
        type: 'grid',
        title: 'Meet Our Team',
        subtitle: 'The people behind the magic',
        description: 'Showcase team members',
        fields: {
          sectionTitle: 'Meet Our Team',
          sectionSubtitle: 'The People Behind the Magic',
          sectionDescription: 'Get to know the talented individuals who make it all happen'
        },
        isActive: true,
        displayOrder: 7
      },
      {
        id: 'home-blog',
        type: 'list',
        title: 'Latest Blog Posts',
        subtitle: 'Insights and updates',
        description: 'Display latest blog posts',
        fields: {
          sectionTitle: 'Latest Insights',
          sectionSubtitle: 'From Our Blog',
          sectionDescription: 'Stay updated with the latest trends and insights from our team'
        },
        isActive: true,
        displayOrder: 8
      },
      {
        id: 'home-pricing',
        type: 'content',
        title: 'Pricing Plans',
        subtitle: 'Choose your plan',
        description: 'Display pricing information',
        fields: {
          sectionTitle: 'Pricing Plans',
          sectionSubtitle: 'Choose Your Plan',
          sectionDescription: 'Flexible pricing options to fit your business needs'
        },
        isActive: true,
        displayOrder: 9
      },
      {
        id: 'home-newsletter',
        type: 'form',
        title: 'Newsletter Signup',
        subtitle: 'Stay updated',
        description: 'Newsletter subscription form',
        fields: {
          sectionTitle: 'Stay Updated',
          sectionSubtitle: 'Subscribe to Our Newsletter',
          sectionDescription: 'Get the latest insights, tips, and updates delivered to your inbox'
        },
        isActive: true,
        displayOrder: 10
      },
      {
        id: 'home-cta',
        type: 'cta',
        title: 'Call to Action',
        subtitle: 'Ready to get started?',
        description: 'Main call-to-action section',
        fields: {
          sectionTitle: 'Ready to Get Started?',
          sectionSubtitle: 'Let\'s Build Something Amazing Together',
          sectionDescription: 'Transform your ideas into reality with our expert team',
          buttonText: 'Start Your Project',
          buttonUrl: '/contact'
        },
        isActive: true,
        displayOrder: 11
      },
      {
        id: 'home-contact',
        type: 'contact',
        title: 'Contact Section',
        subtitle: 'Get in touch',
        description: 'Contact information and form',
        fields: {
          sectionTitle: 'Get in Touch',
          sectionSubtitle: 'Let\'s Start a Conversation',
          sectionDescription: 'Ready to bring your ideas to life? Contact us today.'
        },
        isActive: true,
        displayOrder: 12
      }
    ]
  },
  {
    id: 'about',
    name: 'About',
    slug: 'about',
    path: '/about',
    title: 'About Page',
    metaDescription: 'About Technoloway - Our story, mission, and values',
    icon: 'InformationCircleIcon',
    sections: [
      {
        id: 'about-hero',
        type: 'hero',
        title: 'About Hero',
        subtitle: 'Our story and mission',
        description: 'Hero section introducing the company',
        fields: {
          sectionTitle: 'Building the Future of Software',
          sectionSubtitle: 'Our Story & Mission',
          sectionDescription: 'Founded in 2014, Technoloway has been at the forefront of software innovation, helping businesses transform their ideas into digital reality.'
        },
        slides: [
          {
            id: 'about-slide-1',
            title: 'Building the Future of Software',
            subtitle: 'Our Story & Mission',
            buttonText: 'Learn More',
            buttonUrl: '#mission',
            imageUrl: '/images/about/about-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'about-stats',
        type: 'stats',
        title: 'Company Statistics',
        subtitle: 'Our achievements',
        description: 'Display company statistics and achievements',
        fields: {
          sectionTitle: 'Our Achievements',
          sectionSubtitle: 'By the Numbers',
          sectionDescription: 'A decade of innovation and growth'
        },
        isActive: true,
        displayOrder: 2
      },
      {
        id: 'about-mission',
        type: 'content',
        title: 'Mission & Vision',
        subtitle: 'What drives us',
        description: 'Company mission and vision statements',
        fields: {
          sectionTitle: 'Our Mission',
          sectionSubtitle: 'What Drives Us',
          sectionDescription: 'To democratize cutting-edge technology and make it accessible to businesses of all sizes.',
          missionStatement: 'To democratize cutting-edge technology and make it accessible to businesses of all sizes.',
          visionStatement: 'To be the leading technology partner for businesses worldwide.'
        },
        isActive: true,
        displayOrder: 3
      },
      {
        id: 'about-values',
        type: 'features',
        title: 'Our Values',
        subtitle: 'What we stand for',
        description: 'Company values and principles',
        fields: {
          sectionTitle: 'Our Values',
          sectionSubtitle: 'What We Stand For',
          sectionDescription: 'The principles that guide everything we do'
        },
        isActive: true,
        displayOrder: 4
      },
      {
        id: 'about-timeline',
        type: 'content',
        title: 'Our Journey',
        subtitle: 'Company timeline',
        description: 'Company history and milestones',
        fields: {
          sectionTitle: 'Our Journey',
          sectionSubtitle: 'Key Milestones',
          sectionDescription: 'A decade of innovation and growth'
        },
        isActive: true,
        displayOrder: 5
      }
    ]
  },
  {
    id: 'services',
    name: 'Services',
    slug: 'services',
    path: '/services',
    title: 'Services Page',
    metaDescription: 'Our comprehensive software development services',
    icon: 'BriefcaseIcon',
    sections: [
      {
        id: 'services-hero',
        type: 'hero',
        title: 'Services Hero',
        subtitle: 'What we offer',
        description: 'Hero section introducing services',
        fields: {
          sectionTitle: 'Our Services',
          sectionSubtitle: 'What We Offer',
          sectionDescription: 'Comprehensive software development services to help your business thrive in the digital world.'
        },
        slides: [
          {
            id: 'services-slide-1',
            title: 'Our Services',
            subtitle: 'What We Offer',
            buttonText: 'View Services',
            buttonUrl: '#services-grid',
            imageUrl: '/images/services/services-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'services-grid',
        type: 'grid',
        title: 'Services Grid',
        subtitle: 'All our services',
        description: 'Display all services in a grid',
        fields: {
          sectionTitle: 'All Our Services',
          sectionSubtitle: 'Comprehensive Solutions',
          sectionDescription: 'From web development to AI solutions, we cover all your technology needs'
        },
        isActive: true,
        displayOrder: 2
      },
      {
        id: 'services-process',
        type: 'process',
        title: 'Our Process',
        subtitle: 'How we work',
        description: 'Our development process',
        fields: {
          sectionTitle: 'Our Process',
          sectionSubtitle: 'How We Work',
          sectionDescription: 'A proven methodology that delivers results'
        },
        isActive: true,
        displayOrder: 3
      },
      {
        id: 'services-cta',
        type: 'cta',
        title: 'Get Started',
        subtitle: 'Ready to start?',
        description: 'Call to action for services',
        fields: {
          sectionTitle: 'Ready to Get Started?',
          sectionSubtitle: 'Let\'s Discuss Your Project',
          sectionDescription: 'Let\'s discuss your project requirements and create a custom solution that drives your business forward.',
          buttonText: 'Start Your Project',
          buttonUrl: '/contact'
        },
        isActive: true,
        displayOrder: 4
      }
    ]
  },
  {
    id: 'contact',
    name: 'Contact',
    slug: 'contact',
    path: '/contact',
    title: 'Contact Page',
    metaDescription: 'Get in touch with Technoloway',
    icon: 'PhoneIcon',
    sections: [
      {
        id: 'contact-hero',
        type: 'hero',
        title: 'Contact Hero',
        subtitle: 'Get in touch',
        description: 'Hero section for contact page',
        fields: {
          sectionTitle: 'Let\'s Build Something Amazing',
          sectionSubtitle: 'Get in Touch',
          sectionDescription: 'Ready to transform your ideas into reality? Get in touch with our team and let\'s discuss how we can help bring your vision to life.'
        },
        slides: [
          {
            id: 'contact-slide-1',
            title: 'Let\'s Build Something Amazing',
            subtitle: 'Get in Touch',
            buttonText: 'Send Message',
            buttonUrl: '#contact-form',
            imageUrl: '/images/contact/contact-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'contact-info',
        type: 'contact',
        title: 'Contact Information',
        subtitle: 'How to reach us',
        description: 'Contact details and information',
        fields: {
          sectionTitle: 'Contact Information',
          sectionSubtitle: 'How to Reach Us',
          sectionDescription: 'Get in touch with us through any of the following channels.',
          address: '123 Innovation Drive, Tech City, TC 12345',
          phone: '+****************',
          email: '<EMAIL>',
          hours: 'Monday - Friday: 9:00 AM - 6:00 PM'
        },
        isActive: true,
        displayOrder: 2
      },
      {
        id: 'contact-form',
        type: 'form',
        title: 'Contact Form',
        subtitle: 'Send us a message',
        description: 'Contact form for inquiries',
        fields: {
          sectionTitle: 'Send Us a Message',
          sectionSubtitle: 'Tell Us About Your Project',
          sectionDescription: 'Fill out the form below and we\'ll get back to you within 24 hours.'
        },
        isActive: true,
        displayOrder: 3
      }
    ]
  },
  {
    id: 'projects',
    name: 'Projects',
    slug: 'projects',
    path: '/projects',
    title: 'Projects Page',
    metaDescription: 'View our portfolio of successful projects',
    icon: 'FolderIcon',
    sections: [
      {
        id: 'projects-hero',
        type: 'hero',
        title: 'Projects Hero',
        subtitle: 'Our work',
        description: 'Hero section for projects page',
        fields: {
          sectionTitle: 'Our Projects',
          sectionSubtitle: 'Showcase of Our Work',
          sectionDescription: 'Discover our portfolio of successful projects and see how we\'ve helped businesses achieve their goals.'
        },
        slides: [
          {
            id: 'projects-slide-1',
            title: 'Our Projects',
            subtitle: 'Showcase of Our Work',
            buttonText: 'View Projects',
            buttonUrl: '#projects-grid',
            imageUrl: '/images/projects/projects-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'projects-grid',
        type: 'grid',
        title: 'Projects Grid',
        subtitle: 'All our projects',
        description: 'Display all projects in a grid',
        fields: {
          sectionTitle: 'All Our Projects',
          sectionSubtitle: 'Portfolio Showcase',
          sectionDescription: 'Explore our diverse portfolio of successful projects across various industries and technologies.'
        },
        isActive: true,
        displayOrder: 2
      }
    ]
  },
  {
    id: 'team',
    name: 'Team',
    slug: 'team',
    path: '/team',
    title: 'Team Page',
    metaDescription: 'Meet our talented team of developers and designers',
    icon: 'UserGroupIcon',
    sections: [
      {
        id: 'team-hero',
        type: 'hero',
        title: 'Team Hero',
        subtitle: 'Meet our team',
        description: 'Hero section for team page',
        fields: {
          sectionTitle: 'Meet Our Team',
          sectionSubtitle: 'The People Behind the Magic',
          sectionDescription: 'Get to know the talented individuals who make it all happen and bring your ideas to life.'
        },
        slides: [
          {
            id: 'team-slide-1',
            title: 'Meet Our Team',
            subtitle: 'The People Behind the Magic',
            buttonText: 'Meet the Team',
            buttonUrl: '#team-grid',
            imageUrl: '/images/team/team-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'team-grid',
        type: 'grid',
        title: 'Team Grid',
        subtitle: 'Our team members',
        description: 'Display team members in a grid',
        fields: {
          sectionTitle: 'Our Team Members',
          sectionSubtitle: 'Expert Developers & Designers',
          sectionDescription: 'Meet the talented professionals who make Technoloway a leading software development company.'
        },
        isActive: true,
        displayOrder: 2
      }
    ]
  },
  {
    id: 'blog',
    name: 'Blog',
    slug: 'blog',
    path: '/blog',
    title: 'Blog Page',
    metaDescription: 'Latest insights, tips, and updates from our team',
    icon: 'DocumentTextIcon',
    sections: [
      {
        id: 'blog-hero',
        type: 'hero',
        title: 'Blog Hero',
        subtitle: 'Latest insights',
        description: 'Hero section for blog page',
        fields: {
          sectionTitle: 'Latest Insights',
          sectionSubtitle: 'From Our Blog',
          sectionDescription: 'Stay updated with the latest trends, insights, and tips from our expert team.'
        },
        slides: [
          {
            id: 'blog-slide-1',
            title: 'Latest Insights',
            subtitle: 'From Our Blog',
            buttonText: 'Read Articles',
            buttonUrl: '#blog-list',
            imageUrl: '/images/blog/blog-hero.jpg',
            displayOrder: 1,
            isActive: true
          }
        ],
        isActive: true,
        displayOrder: 1
      },
      {
        id: 'blog-list',
        type: 'list',
        title: 'Blog Posts',
        subtitle: 'All articles',
        description: 'Display blog posts in a list',
        fields: {
          sectionTitle: 'All Articles',
          sectionSubtitle: 'Insights & Updates',
          sectionDescription: 'Explore our collection of articles covering technology trends, development tips, and industry insights.'
        },
        isActive: true,
        displayOrder: 2
      }
    ]
  }
]

export function getPublicPages(): PublicPage[] {
  return PUBLIC_PAGES
}

export function getPageById(id: string): PublicPage | undefined {
  return PUBLIC_PAGES.find(page => page.id === id)
}

export function getPageBySlug(slug: string): PublicPage | undefined {
  return PUBLIC_PAGES.find(page => page.slug === slug)
}

export function getPageByPath(path: string): PublicPage | undefined {
  return PUBLIC_PAGES.find(page => page.path === path)
}

export function getSectionById(pageId: string, sectionId: string): PageSection | undefined {
  const page = getPageById(pageId)
  return page?.sections.find(section => section.id === sectionId)
} 