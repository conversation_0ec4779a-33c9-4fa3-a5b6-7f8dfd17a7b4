'use client';

import { useEffect, useState } from 'react';

interface BrowserExtensionHandler {
  isClient: boolean;
  hasBrowserExtensions: boolean;
  suppressHydrationWarning: boolean;
}

export function useBrowserExtensionHandler(): BrowserExtensionHandler {
  const [isClient, setIsClient] = useState(false);
  const [hasBrowserExtensions, setHasBrowserExtensions] = useState(false);

  useEffect(() => {
    setIsClient(true);

    // Check for common browser extension indicators
    const checkForExtensions = () => {
      let hasExtensions = false;

      // Check for form-related extensions
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        // Check for common browser extension attributes
        if (form.hasAttribute('abframeid') ||
            form.hasAttribute('abineguid') ||
            form.hasAttribute('data-pwm-inline') ||
            form.querySelector('.pwm-field-icon') ||
            form.querySelector('.pwm-inline-icon')) {
          hasExtensions = true;
        }
      });

      // Check for random ID injections (A/B testing tools, analytics, etc.)
      const elementsWithRandomIds = document.querySelectorAll('[id^="abId"], [id*="random"], [id*="uuid"], [data-ab-test], [data-analytics]');
      if (elementsWithRandomIds.length > 0) {
        hasExtensions = true;
      }

      // Check for common A/B testing and analytics tools
      const commonExtensionSelectors = [
        '[id^="abId"]',
        '[id^="ab-"]',
        '[data-ab-test]',
        '[data-analytics]',
        '[data-gtm]',
        '[data-optimizely]',
        '[data-hotjar]',
        '.pwm-field-icon',
        '.pwm-inline-icon',
        '[abframeid]',
        '[abineguid]',
        '[data-pwm-inline]'
      ];

      commonExtensionSelectors.forEach(selector => {
        if (document.querySelector(selector)) {
          hasExtensions = true;
        }
      });

      setHasBrowserExtensions(hasExtensions);
    };

    // Check immediately and after delays to catch extensions that load later
    checkForExtensions();
    const timeoutId1 = setTimeout(checkForExtensions, 100);
    const timeoutId2 = setTimeout(checkForExtensions, 500);
    const timeoutId3 = setTimeout(checkForExtensions, 1000);

    return () => {
      clearTimeout(timeoutId1);
      clearTimeout(timeoutId2);
      clearTimeout(timeoutId3);
    };
  }, []);

  return {
    isClient,
    hasBrowserExtensions,
    suppressHydrationWarning: hasBrowserExtensions
  };
} 