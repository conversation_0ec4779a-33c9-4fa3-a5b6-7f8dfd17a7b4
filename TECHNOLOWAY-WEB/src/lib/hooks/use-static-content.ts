'use client'

import { useState, useEffect } from 'react'

interface StaticContentItem {
  id: string
  content: string
  contenttype: string
  displayorder: number
}

interface StaticContentData {
  [page: string]: {
    [section: string]: {
      [contentkey: string]: StaticContentItem
    }
  }
}

interface UseStaticContentReturn {
  data: StaticContentData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  getContent: (page: string, section: string, contentkey: string, fallback?: string) => string
  updateContent: (updates: Array<{
    page: string
    section: string
    contentkey: string
    content: string
    contenttype?: string
  }>) => Promise<boolean>
}

interface StaticContent {
  [key: string]: string
}

interface ContentCache {
  [page: string]: {
    [section: string]: StaticContent
  }
}

// Global cache for content
let contentCache: ContentCache = {}
let cacheTimestamp = 0
let pendingRequest: Promise<void> | null = null
let globalIsLoading = false
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Function to clear cache
export const clearContentCache = () => {
  contentCache = {}
  cacheTimestamp = 0
  pendingRequest = null
  globalIsLoading = false
}

// Live preview mode - when true, content is fetched from the content editor
let isLivePreviewMode = false
let livePreviewContent: ContentCache = {}

export function useStaticContent() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Function to enable live preview mode
  const enableLivePreview = (content: ContentCache) => {
    isLivePreviewMode = true
    livePreviewContent = content
  }

  // Function to disable live preview mode
  const disableLivePreview = () => {
    isLivePreviewMode = false
    livePreviewContent = {}
  }

  // Function to update live preview content
  const updateLivePreview = (page: string, section: string, key: string, value: string) => {
    if (!livePreviewContent[page]) {
      livePreviewContent[page] = {}
    }
    if (!livePreviewContent[page][section]) {
      livePreviewContent[page][section] = {}
    }
    livePreviewContent[page][section][key] = value
  }

  // Function to get content with fallback
  const getContent = (page: string, section: string, key: string, fallback: string = ''): string => {
    // If in live preview mode, use live preview content
    if (isLivePreviewMode && livePreviewContent[page]?.[section]?.[key]) {
      return livePreviewContent[page][section][key]
    }

    // Otherwise, use cached content or fallback
    return contentCache[page]?.[section]?.[key] || fallback
  }

  // Function to fetch content from API with request deduplication
  const fetchContent = async (page?: string, section?: string) => {
    // If there's already a pending request, wait for it
    if (pendingRequest) {
      await pendingRequest
      return
    }

    // If already loading globally, don't start another request
    if (globalIsLoading) return

    setIsLoading(true)
    globalIsLoading = true
    setError(null)

    // Create the pending request promise
    pendingRequest = (async () => {
      try {
        const params = new URLSearchParams()
        if (page) params.append('page', page)
        if (section) params.append('section', section)

        const response = await fetch(`/api/content/pages?${params.toString()}`)

        if (!response.ok) {
          throw new Error('Failed to fetch content')
        }

        const data = await response.json()

        if (data.success && data.data) {
          // Update cache
          if (page && section) {
            if (!contentCache[page]) contentCache[page] = {}
            contentCache[page][section] = data.data
          } else {
            contentCache = data.data
          }
          cacheTimestamp = Date.now()
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch content')
        console.error('Error fetching static content:', err)
      } finally {
        setIsLoading(false)
        globalIsLoading = false
        pendingRequest = null
      }
    })()

    await pendingRequest
  }

  // Function to refresh content
  const refreshContent = async (page?: string, section?: string) => {
    cacheTimestamp = 0 // Force refresh
    await fetchContent(page, section)
  }

  // Auto-fetch content on mount if cache is empty or expired
  useEffect(() => {
    const shouldFetch = !Object.keys(contentCache).length ||
                       (Date.now() - cacheTimestamp) > CACHE_DURATION

    if (shouldFetch && !isLivePreviewMode && !globalIsLoading && !pendingRequest) {
      fetchContent()
    }

    // Listen for cache clearing events
    const handleClearCache = () => {
      clearContentCache()
      if (!globalIsLoading && !pendingRequest) {
        fetchContent() // Refetch content immediately only if not already loading
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('clearContentCache', handleClearCache)

      return () => {
        window.removeEventListener('clearContentCache', handleClearCache)
      }
    }
  }, [])

  return {
    getContent,
    fetchContent,
    refreshContent,
    isLoading,
    error,
    enableLivePreview,
    disableLivePreview,
    updateLivePreview,
    isLivePreviewMode: () => isLivePreviewMode
  }
}

// Export functions for external use
export const staticContentUtils = {
  enableLivePreview: (content: ContentCache) => {
    isLivePreviewMode = true
    livePreviewContent = content
  },
  disableLivePreview: () => {
    isLivePreviewMode = false
    livePreviewContent = {}
  },
  updateLivePreview: (page: string, section: string, key: string, value: string) => {
    if (!livePreviewContent[page]) {
      livePreviewContent[page] = {}
    }
    if (!livePreviewContent[page][section]) {
      livePreviewContent[page][section] = {}
    }
    livePreviewContent[page][section][key] = value
  },
  isLivePreviewMode: () => isLivePreviewMode,
  clearCache: clearContentCache
}

// Helper hook for a specific page/section
export function usePageContent(page: string, section?: string) {
  return useStaticContent()
}

// Helper function to get content synchronously (for SSR)
export async function getStaticContent(page?: string, section?: string): Promise<StaticContentData> {
  // Skip SSR fetch in development to avoid connection issues
  if (process.env.NODE_ENV === 'development') {
    return {}
  }

  try {
    const params = new URLSearchParams()
    if (page) params.append('page', page)
    if (section) params.append('section', section)

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'
          const response = await fetch(`${baseUrl}/api/content/pages?${params.toString()}`, {
      // Add timeout to prevent hanging
      signal: AbortSignal.timeout(5000)
    })

    if (!response.ok) {
      throw new Error('Failed to fetch static content')
    }

    const result = await response.json()

    if (result.success) {
      return result.data
    } else {
      throw new Error(result.error || 'Failed to fetch static content')
    }
  } catch (error) {
    console.error('Error fetching static content:', error)
    return {}
  }
}

// Helper function to get a single content item
export function getContentItem(
  data: StaticContentData | null, 
  page: string, 
  section: string, 
  contentkey: string, 
  fallback: string = ''
): string {
  if (!data || !data[page] || !data[page][section] || !data[page][section][contentkey]) {
    return fallback
  }
  return data[page][section][contentkey].content
}

