'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'

interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface UseAdminDataOptions {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filter?: string
}

interface UseAdminDataResult<T> {
  data: T[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  refetch: () => void
  create: (data: Partial<T>) => Promise<T | null>
  update: (id: string, data: Partial<T>) => Promise<T | null>
  remove: (id: string) => Promise<boolean>
}

export function useAdminData<T>(
  endpoint: string,
  options: UseAdminDataOptions = {}
): UseAdminDataResult<T> {
  const { data: session } = useSession()
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<{
    page: number
    limit: number
    total: number
    totalPages: number
  } | null>(null)

  const {
    page = 1,
    limit = 10,
    search = '',
    sortBy = 'createdAt',
    sortOrder = 'desc',
    filter = ''
  } = options

  const buildQueryString = useCallback(() => {
    const params = new URLSearchParams()
    params.append('page', page.toString())
    params.append('limit', limit.toString())
    if (search) params.append('search', search)
    if (sortBy) params.append('sortBy', sortBy)
    if (sortOrder) params.append('sortOrder', sortOrder)
    if (filter) params.append('filter', filter)
    return params.toString()
  }, [page, limit, search, sortBy, sortOrder, filter])

  const fetchData = useCallback(async () => {
    if (!session?.user || session.user.role !== 'ADMIN') {
      setError('Admin access required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const queryString = buildQueryString()
      const response = await fetch(`/api/admin/${endpoint}?${queryString}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: PaginatedResponse<T> = await response.json()

      if (result.success && result.data) {
        setData(result.data)
        setPagination(result.pagination || null)
      } else {
        throw new Error(result.error || 'Failed to fetch data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setData([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [session, endpoint, buildQueryString])

  const create = useCallback(async (createData: Partial<T>): Promise<T | null> => {
    if (!session?.user || session.user.role !== 'ADMIN') {
      setError('Admin access required')
      return null
    }

    try {
      const response = await fetch(`/api/admin/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<T> = await response.json()

      if (result.success && result.data) {
        // Refresh data after creation
        await fetchData()
        return result.data
      } else {
        throw new Error(result.error || 'Failed to create item')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return null
    }
  }, [session, endpoint, fetchData])

  const update = useCallback(async (id: string, updateData: Partial<T>): Promise<T | null> => {
    if (!session?.user || session.user.role !== 'ADMIN') {
      setError('Admin access required')
      return null
    }

    try {
      const response = await fetch(`/api/admin/${endpoint}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<T> = await response.json()

      if (result.success && result.data) {
        // Update local data
        setData(prevData => 
          prevData.map(item => 
            (item as any).id === id ? result.data! : item
          )
        )
        return result.data
      } else {
        throw new Error(result.error || 'Failed to update item')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return null
    }
  }, [session, endpoint])

  const remove = useCallback(async (id: string): Promise<boolean> => {
    if (!session?.user || session.user.role !== 'ADMIN') {
      setError('Admin access required')
      return false
    }

    try {
      const response = await fetch(`/api/admin/${endpoint}/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result: ApiResponse<null> = await response.json()

      if (result.success) {
        // Remove from local data
        setData(prevData => prevData.filter(item => (item as any).id !== id))
        return true
      } else {
        throw new Error(result.error || 'Failed to delete item')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      return false
    }
  }, [session, endpoint])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    pagination,
    refetch: fetchData,
    create,
    update,
    remove,
  }
}
