/**
 * @jest-environment node
 */

import { createMocks } from 'node-mocks-http'
import { NextRequest } from 'next/server'
import {
  getAuthenticatedUser,
  validateChatAccess,
  filterMessagesForUser,
  getAllowedRecipients,
  canMessageUser,
  canAccessContactForm
} from '@/services/chat/chat-access-control'
import { getServerSession } from 'next-auth'

// Mock dependencies
jest.mock('next-auth', () => ({
  getServerSession: jest.fn(),
}))

jest.mock('@/config/prisma', () => ({
  prisma: {
    contactforms: {
      findUnique: jest.fn(),
    },
    users: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
  },
}))

jest.mock('@/services/auth/auth-config', () => ({
  authOptions: {},
}))

const mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>

// Import the mocked prisma after mocking
import { prisma } from '@/config/prisma'
const mockPrisma = prisma as jest.Mocked<typeof prisma>

describe('Chat Access Control', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getAuthenticatedUser', () => {
    it('should return user for valid session', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)
      
      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue({
        id: BigInt(1),
        email: '<EMAIL>',
        role: 'ADMIN',
        firstname: 'Test',
        lastname: 'User'
      } as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const user = await getAuthenticatedUser(request)

      expect(user).toEqual({
        id: '1',
        email: '<EMAIL>',
        role: 'ADMIN',
        firstname: 'Test',
        lastname: 'User'
      })
    })

    it('should throw error for invalid session', async () => {
      mockGetServerSession.mockResolvedValue(null)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      await expect(getAuthenticatedUser(request)).rejects.toThrow('Authentication required')
    })
  })

  describe('canAccessContactForm', () => {
    const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
    const mockContactForm = {
      id: BigInt(1),
      email: '<EMAIL>',
      userid: BigInt(2),
    }

    it('should allow admin access to any contact form', async () => {
      (mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)

      const result = await canAccessContactForm(mockUser as any, 1)

      expect(result).toBe(true)
    })

    it('should allow user access to their own contact forms', async () => {
      const userMock = { id: '2', email: '<EMAIL>', role: 'USER' }
      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)

      const result = await canAccessContactForm(userMock as any, 1)

      expect(result).toBe(true)
    })

    it('should deny access to unauthorized users', async () => {
      const unauthorizedUser = { id: '3', email: '<EMAIL>', role: 'USER' }
      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)

      const result = await canAccessContactForm(unauthorizedUser as any, 1)
      expect(result).toBe(false)
    })

    it('should return false for non-existent contact form', async () => {
      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(null)

      const result = await canAccessContactForm(mockUser as any, 999)
      expect(result).toBe(false)
    })
  })

  describe('canMessageUser', () => {
    it('should allow admin to message anyone', async () => {
      const admin = { role: 'ADMIN' } as any
      const targetUser = { id: BigInt(2), role: 'USER' }

      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(targetUser as any)

      const result = await canMessageUser(admin, 2)
      expect(result).toBe(true)
    })

    it('should allow user to message admin', async () => {
      const user = { role: 'USER' } as any
      const targetUser = { id: BigInt(1), role: 'ADMIN' }

      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(targetUser as any)

      const result = await canMessageUser(user, 1)
      expect(result).toBe(true)
    })

    it('should allow client to message admin', async () => {
      const client = { role: 'CLIENT' } as any
      const targetUser = { id: BigInt(1), role: 'ADMIN' }

      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(targetUser as any)

      const result = await canMessageUser(client, 1)
      expect(result).toBe(true)
    })

    it('should not allow user to message another user', async () => {
      const user1 = { role: 'USER' } as any
      const targetUser = { id: BigInt(2), role: 'USER' }

      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(targetUser as any)

      const result = await canMessageUser(user1, 2)
      expect(result).toBe(false)
    })

    it('should not allow client to message another client', async () => {
      const client1 = { role: 'CLIENT' } as any
      const targetUser = { id: BigInt(2), role: 'CLIENT' }

      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue(targetUser as any)

      const result = await canMessageUser(client1, 2)
      expect(result).toBe(false)
    })
  })

  describe('filterMessagesForUser', () => {
    const messages = [
      {
        id: 1,
        senderid: 1,
        receiverid: 2,
        message: 'Admin to User',
        sender: { id: 1, role: 'ADMIN' },
        receiver: { id: 2, role: 'USER' },
      },
      {
        id: 2,
        senderid: 2,
        receiverid: 1,
        message: 'User to Admin',
        sender: { id: 2, role: 'USER' },
        receiver: { id: 1, role: 'ADMIN' },
      },
      {
        id: 3,
        senderid: 3,
        receiverid: 4,
        message: 'Other conversation',
        sender: { id: 3, role: 'USER' },
        receiver: { id: 4, role: 'ADMIN' },
      },
    ] as any

    it('should return all messages for admin', () => {
      const admin = { id: '1', role: 'ADMIN' } as any
      const filtered = filterMessagesForUser(messages, admin)

      expect(filtered).toHaveLength(3)
    })

    it('should return only relevant messages for user', () => {
      const user = { id: '2', role: 'USER' } as any
      const filtered = filterMessagesForUser(messages, user)

      expect(filtered).toHaveLength(2)
      expect(filtered.every(m => m.senderid === 2 || m.receiverid === 2)).toBe(true)
    })

    it('should return empty array for unrelated user', () => {
      const user = { id: '5', role: 'USER' } as any
      const filtered = filterMessagesForUser(messages, user)

      expect(filtered).toHaveLength(0)
    })
  })

  describe('getAllowedRecipients', () => {
    it('should return all users for admin', async () => {
      const admin = { id: '1', role: 'ADMIN' } as any
      const mockContactForm = {
        id: BigInt(1),
        email: '<EMAIL>',
        userid: BigInt(2),
      }
      const mockUsers = [
        { id: BigInt(1), role: 'ADMIN', email: '<EMAIL>', firstname: 'Admin', lastname: 'User' },
        { id: BigInt(2), role: 'USER', email: '<EMAIL>', firstname: 'Test', lastname: 'User' },
      ]

      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)
      ;(mockPrisma.users.findMany as jest.Mock).mockResolvedValue(mockUsers as any)

      const recipients = await getAllowedRecipients(admin, 1)

      expect(recipients).toHaveLength(2)
    })

    it('should return only admins for regular user', async () => {
      const user = { id: '2', role: 'USER' } as any
      const mockContactForm = {
        id: BigInt(1),
        email: '<EMAIL>',
        userid: BigInt(2),
      }
      const mockAdmins = [
        { id: BigInt(1), role: 'ADMIN', email: '<EMAIL>', firstname: 'Admin', lastname: 'User' },
      ]

      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)
      ;(mockPrisma.users.findMany as jest.Mock).mockResolvedValue(mockAdmins as any)

      const recipients = await getAllowedRecipients(user, 1)

      expect(recipients).toHaveLength(1)
      expect(recipients[0].role).toBe('ADMIN')
    })

    it('should return only admins for client', async () => {
      const client = { id: '3', role: 'CLIENT' } as any
      const mockContactForm = {
        id: BigInt(1),
        email: '<EMAIL>',
        userid: BigInt(3),
      }
      const mockAdmins = [
        { id: BigInt(1), role: 'ADMIN', email: '<EMAIL>', firstname: 'Admin', lastname: 'User' },
      ]

      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)
      ;(mockPrisma.users.findMany as jest.Mock).mockResolvedValue(mockAdmins as any)

      const recipients = await getAllowedRecipients(client, 1)

      expect(recipients).toHaveLength(1)
      expect(recipients[0].role).toBe('ADMIN')
    })
  })

  describe('validateChatAccess', () => {
    it('should validate access and return context', async () => {
      const mockUser = { id: '1', email: '<EMAIL>', role: 'ADMIN' }
      const mockContactForm = {
        id: BigInt(1),
        email: '<EMAIL>',
        userid: BigInt(2),
      }

      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)
      ;(mockPrisma.users.findUnique as jest.Mock).mockResolvedValue({
        id: BigInt(1),
        email: '<EMAIL>',
        role: 'ADMIN',
        firstname: 'Admin',
        lastname: 'User'
      } as any)
      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const context = await validateChatAccess(request, 1)

      expect(context.user).toEqual({
        id: '1',
        email: '<EMAIL>',
        role: 'ADMIN',
        firstname: 'Admin',
        lastname: 'User'
      })
      expect(context.contactFormId).toBe(1)
    })

    it('should validate messaging permissions when target user specified', async () => {
      const mockUser = { id: '2', email: '<EMAIL>', role: 'USER' }
      const mockContactForm = {
        id: BigInt(1),
        email: '<EMAIL>',
        userid: BigInt(2),
      }
      const mockTargetUser = { id: BigInt(1), role: 'ADMIN' }

      mockGetServerSession.mockResolvedValue({ user: mockUser } as any)
      const usersFindUniqueMock = mockPrisma.users.findUnique as jest.Mock
      usersFindUniqueMock.mockResolvedValueOnce({
          id: BigInt(2),
          email: '<EMAIL>',
          role: 'USER',
          firstname: 'Test',
          lastname: 'User'
        } as any)
      usersFindUniqueMock.mockResolvedValueOnce(mockTargetUser as any)
      ;(mockPrisma.contactforms.findUnique as jest.Mock).mockResolvedValue(mockContactForm as any)

      const { req } = createMocks({ method: 'GET' })
      const request = new NextRequest(req.url!, { method: req.method })

      const context = await validateChatAccess(request, 1, 1)

      expect(context.user).toEqual({
        id: '2',
        email: '<EMAIL>',
        role: 'USER',
        firstname: 'Test',
        lastname: 'User'
      })
      expect(context.contactFormId).toBe(1)
      expect(context.targetUserId).toBe(1)
    })
  })
})
