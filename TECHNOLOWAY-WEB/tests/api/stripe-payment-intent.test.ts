/**
 * @jest-environment node
 */
import { validateMinimumAmount } from '@/services/payment/payment-utils'

// Test the payment validation utility function
describe('Payment Amount Validation', () => {
  describe('validateMinimumAmount', () => {
    it('should reject Stripe payments below $0.50 USD', () => {
      const result = validateMinimumAmount(0.25, 'stripe_card', 'USD')

      expect(result.isValid).toBe(false)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toContain('Payment amount must be at least $0.50 USD')
    })

    it('should accept Stripe payments at exactly $0.50 USD', () => {
      const result = validateMinimumAmount(0.50, 'stripe_card', 'USD')

      expect(result.isValid).toBe(true)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toBeUndefined()
    })

    it('should accept Stripe payments above $0.50 USD', () => {
      const result = validateMinimumAmount(10.00, 'stripe_card', 'USD')

      expect(result.isValid).toBe(true)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toBeUndefined()
    })

    it('should accept bank transfers below $0.50 (no minimum)', () => {
      const result = validateMinimumAmount(0.25, 'bank_transfer', 'USD')

      expect(result.isValid).toBe(true)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toBeUndefined()
    })

    it('should reject PayPal payments below $0.50 USD', () => {
      const result = validateMinimumAmount(0.25, 'paypal', 'USD')

      expect(result.isValid).toBe(false)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toContain('Payment amount must be at least $0.50 USD')
    })

    it('should accept Apple Pay payments at minimum amount', () => {
      const result = validateMinimumAmount(0.50, 'apple_pay', 'USD')

      expect(result.isValid).toBe(true)
      expect(result.minimumAmount).toBe(0.50)
      expect(result.errorMessage).toBeUndefined()
    })
  })
})


