import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ChatInterface } from '@/components/admin/chat/chat-interface'

// Mock scrollIntoView
Object.defineProperty(Element.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true
})

describe('ChatInterface', () => {
  const mockMessages = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      message: 'Hello there!',
      messageType: 'chat',
      contentType: 'text',
      createdAt: '2024-01-01T10:00:00Z',
      isRead: false,
      attachments: [],
      sender: {
        id: 1,
        email: '<EMAIL>',
        firstname: '<PERSON>',
        lastname: '<PERSON><PERSON>',
        imageurl: undefined,
        role: 'USER',
      },
      receiver: {
        id: 2,
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        imageurl: undefined,
        role: 'ADMI<PERSON>',
      },
    },
    {
      id: 2,
      name: 'Admin User',
      email: '<EMAIL>',
      message: 'Hi! How can I help you?',
      messageType: 'chat',
      contentType: 'text',
      createdAt: '2024-01-01T10:05:00Z',
      isRead: true,
      attachments: [],
      sender: {
        id: 2,
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        imageurl: undefined,
        role: 'ADMIN',
      },
      receiver: {
        id: 1,
        email: '<EMAIL>',
        firstname: 'John',
        lastname: 'Doe',
        imageurl: undefined,
        role: 'USER',
      },
    },
  ]

  const defaultProps = {
    messages: mockMessages,
    currentUserId: 2,
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render chat interface with messages', () => {
    render(<ChatInterface {...defaultProps} />)

    expect(screen.getByText('Hello there!')).toBeInTheDocument()
    expect(screen.getByText('Hi! How can I help you?')).toBeInTheDocument()
  })

  it('should display loading state', () => {
    render(<ChatInterface {...defaultProps} messages={[]} isLoading={true} />)

    expect(screen.getByText('Loading messages...')).toBeInTheDocument()
  })

  it('should display empty state when no messages', () => {
    render(<ChatInterface {...defaultProps} messages={[]} />)

    expect(screen.getByText('No messages yet')).toBeInTheDocument()
  })

  it('should group messages by date', () => {
    const messagesWithDifferentDates = [
      {
        ...mockMessages[0],
        createdAt: '2024-01-01T10:00:00Z',
      },
      {
        ...mockMessages[1],
        createdAt: '2024-01-02T10:00:00Z',
      },
    ]

    render(<ChatInterface {...defaultProps} messages={messagesWithDifferentDates} />)

    expect(screen.getByText('Monday, January 1, 2024')).toBeInTheDocument()
    expect(screen.getByText('Tuesday, January 2, 2024')).toBeInTheDocument()
  })

  it('should show typing indicator when users are typing', () => {
    const typingUsers = [
      { id: 1, name: 'John Doe' },
      { id: 2, name: 'Jane Smith' }
    ]

    render(
      <ChatInterface
        {...defaultProps}
        showTypingIndicator={true}
        typingUsers={typingUsers}
      />
    )

    expect(screen.getByText(/typing/)).toBeInTheDocument()
  })

  it('should handle message selection', () => {
    const mockOnMessageSelect = jest.fn()
    render(
      <ChatInterface
        {...defaultProps}
        onMessageSelect={mockOnMessageSelect}
        selectedMessageId={1}
      />
    )

    const firstMessage = screen.getByText('Hello there!').closest('[data-message-id]')

    if (firstMessage) {
      fireEvent.click(firstMessage)
      expect(mockOnMessageSelect).toHaveBeenCalled()
    }
  })

  it('should handle file attachments in messages', () => {
    const messagesWithAttachments = [
      {
        ...mockMessages[0],
        attachments: [
          {
            id: '1',
            filename: 'document.pdf',
            size: 1024,
            mimeType: 'application/pdf',
            url: '/uploads/document.pdf',
          },
        ],
      },
    ]

    render(<ChatInterface {...defaultProps} messages={messagesWithAttachments} />)

    expect(screen.getByText('document.pdf')).toBeInTheDocument()
  })
})
