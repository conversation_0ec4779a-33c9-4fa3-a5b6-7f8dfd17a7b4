<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1299.45 309">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient-5);
      }

      .cls-2 {
        fill: url(#linear-gradient-4);
      }

      .cls-3 {
        fill: url(#linear-gradient-3);
      }

      .cls-4 {
        fill: url(#linear-gradient-2);
      }

      .cls-5 {
        filter: url(#drop-shadow-10);
        stroke: url(#linear-gradient-16);
      }

      .cls-5, .cls-6, .cls-7, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16 {
        stroke-miterlimit: 10;
      }

      .cls-5, .cls-6, .cls-8, .cls-9, .cls-10, .cls-11, .cls-12, .cls-13, .cls-14, .cls-15, .cls-16 {
        fill: #d1d3d4;
        stroke-width: 5px;
      }

      .cls-6 {
        filter: url(#drop-shadow-11);
        stroke: url(#linear-gradient-17);
      }

      .cls-17 {
        fill: url(#linear-gradient);
      }

      .cls-7 {
        fill: none;
        stroke: url(#linear-gradient-6);
        stroke-width: 9px;
      }

      .cls-8 {
        filter: url(#drop-shadow-9);
        stroke: url(#linear-gradient-15);
      }

      .cls-9 {
        filter: url(#drop-shadow-5);
        stroke: url(#linear-gradient-11);
      }

      .cls-10 {
        filter: url(#drop-shadow-8);
        stroke: url(#linear-gradient-14);
      }

      .cls-11 {
        filter: url(#drop-shadow-6);
        stroke: url(#linear-gradient-12);
      }

      .cls-12 {
        filter: url(#drop-shadow-7);
        stroke: url(#linear-gradient-13);
      }

      .cls-13 {
        filter: url(#drop-shadow-4);
        stroke: url(#linear-gradient-10);
      }

      .cls-14 {
        filter: url(#drop-shadow-3);
        stroke: url(#linear-gradient-9);
      }

      .cls-15 {
        filter: url(#drop-shadow-2);
        stroke: url(#linear-gradient-8);
      }

      .cls-16 {
        filter: url(#drop-shadow-1);
        stroke: url(#linear-gradient-7);
      }
    </style>
    <linearGradient id="linear-gradient" x1="1346.19" y1="159.53" x2="1449.14" y2="159.53" gradientTransform="translate(1500.69) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#772ba5"/>
      <stop offset="1" stop-color="#a90093"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="1369.08" y1="167.94" x2="1449.14" y2="167.94" gradientTransform="translate(1500.69) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#e70093"/>
      <stop offset="1" stop-color="#772ba5"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="158.53" y1="140.98" x2="261.48" y2="140.98" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#a5cbe9"/>
      <stop offset=".56" stop-color="#1a7ac7"/>
      <stop offset="1" stop-color="#0024a2"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="181.42" y1="138.88" x2="261.48" y2="138.88" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#0024a2"/>
      <stop offset=".44" stop-color="#1a7ac7"/>
      <stop offset="1" stop-color="#a5cbe9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="-34.18" y1="-298.23" x2="125.95" y2="-298.23" gradientTransform="translate(84.61 391.06) rotate(-29.47) scale(.97 .43) skewX(-20.29)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#fff"/>
      <stop offset=".4" stop-color="#1a7ac7"/>
      <stop offset="1" stop-color="#a90093"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0" y1="154.5" x2="309" y2="154.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2484c6"/>
      <stop offset=".34" stop-color="#5d5fae"/>
      <stop offset="1" stop-color="#d4137d"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="326.43" y1="152.72" x2="404.97" y2="152.72" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2484c6"/>
      <stop offset=".81" stop-color="#7c3999"/>
      <stop offset="1" stop-color="#92278f"/>
    </linearGradient>
    <filter id="drop-shadow-1" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-8" x1="409.77" y1="153.05" x2="488.3" y2="153.05" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-2" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-2" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-2" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-9" x1="492.92" y1="153.07" x2="570.94" y2="153.07" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-3" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-3" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-3" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-10" x1="578.79" y1="153" x2="657.51" y2="153" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-4" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-4" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-4" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-11" x1="666.06" y1="153.05" x2="744.79" y2="153.05" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-5" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-5" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-5" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-12" x1="750.5" y1="153.07" x2="836.68" y2="153.07" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-6" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-6" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-6" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-13" x1="842.22" y1="153.37" x2="912.49" y2="153.37" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-7" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-7" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-7" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-14" x1="909.6" y1="153.07" x2="995.78" y2="153.07" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-8" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-8" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-8" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-15" x1="997.68" y1="153.29" x2="1145.61" y2="153.29" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-9" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-9" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-9" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-16" x1="1136.8" y1="152.72" x2="1227.81" y2="152.72" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-10" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-10" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-10" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient-17" x1="1220.84" y1="153.2" x2="1299.45" y2="153.2" xlink:href="#linear-gradient-7"/>
    <filter id="drop-shadow-11" filterUnits="userSpaceOnUse">
      <feOffset dx="0" dy="0"/>
      <feGaussianBlur result="blur-11" stdDeviation="0"/>
      <feFlood flood-color="#000" flood-opacity="0"/>
      <feComposite in2="blur-11" operator="in"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <path class="cls-17" d="M118.12,106.28v134.98c12.13-11.14,24.25-22.27,36.38-33.41V77.81H61.26l31,28.47h25.86Z"/>
  <path class="cls-4" d="M118.12,106.28v134.98c4.5-4.13,8.99-8.26,13.49-12.39V94.62h-52.04l12.69,11.65h25.86Z"/>
  <path class="cls-3" d="M158.53,77.81v126.34c12.12-11.14,24.25-22.27,36.38-33.41v-64.46h25.86l31-28.47h-93.24Z"/>
  <path class="cls-2" d="M181.42,94.62v88.51c4.5-4.13,8.99-8.26,13.49-12.39v-64.46h25.86l12.69-11.65h-52.04Z"/>
  <path class="cls-1" d="M213.56,144.26c40.1-12.79-157.47,167.17-122.04,102.99-7.44,10.22-10.95,17.51-9.1,20.12,4.65,6.55,41.37-18.95,82.01-56.96,39.45-36.89,83.66-85.99,49.12-66.15Z"/>
  <path class="cls-7" d="M304.5,154.5c0,82.84-67.16,150-150,150S4.5,237.34,4.5,154.5,65.66,4.5,154.5,4.5c82.84,0,150,67.16,150,150Z"/>
  <path class="cls-16" d="M400.68,114.96c3.87,4.04,1.15,11.75-5.4,11.87-5.93.2-15.93.02-20.5.09-1.66.06-2.99-.13-2.78,1.66,0,13.06,0,50.22,0,56.64,0,1.24-.11,2.4-.67,3.69-2.26,5.59-9.09,5.48-11.31-.11-.53-1.28-.64-2.38-.64-3.58,0-4.94,0-26.08,0-41.43,0-6.49,0-11.06,0-14.61-.04-1.81.29-2.2-2.34-2.25-4.39-.05-12.86,0-18-.02-2.11-.02-4.27.03-6.11-.88-4.7-2.31-5.29-8.41-1.62-11.63,1.45-1.39,3.33-2.08,5.37-1.99,8.12,0,48.58,0,57.69,0,2.57-.06,4.34.45,6.22,2.44l.1.11Z"/>
  <path class="cls-15" d="M480.82,113.04c7.67,2.73,6.2,14.06-2.66,14.02-8.13,0-50.89,0-58.49,0-2.26.13-4.47-1.06-5.82-2.7-3.61-4.01-.74-11.8,5.92-11.64,8.46,0,33.74,0,45.09,0,6.21,0,11.17,0,13.4,0,.87,0,1.62.02,2.47.3l.1.03ZM480.86,146.27c7.64,2.67,6.12,13.97-2.6,14-8.75-.06-51.38.11-59.79-.06-5.7-.88-7.93-7.69-4.72-11.46,1.35-1.7,3.57-2.97,6.02-2.83,3.24,0,12.27,0,22.41,0,7.64,0,15.89,0,22.69,0,6.21,0,11.17,0,13.4,0,.88,0,1.64.02,2.5.31l.1.03ZM480.85,179.38c7.69,2.72,6.13,14.06-2.7,14-8.98-.06-51.28.11-59.68-.06-5.76-.86-7.93-7.63-4.72-11.46,1.35-1.7,3.57-2.97,6.02-2.83,3.24,0,12.26,0,22.4,0h22.68c6.2,0,11.16,0,13.39,0,.88,0,1.64.02,2.5.31l.1.03Z"/>
  <path class="cls-14" d="M523.04,129.08c-19.22,7.21-20.02,37.08-3.08,46.48,8.1,4.6,23.25,4.62,31.59.61,3.38-1.51,6.18-4.78,9.58-5.69,4.78-1.27,7.96,3.92,7.08,7.52-.76,4.15-5.02,7.53-9.03,10.04-10.04,6.19-25.3,6.59-37.06,3.72-12.76-3.44-21.27-13.6-24.46-24.15-6.65-20.27.95-49.88,29.33-54.29,10.98-1.41,23.55-.77,32.62,5.08,5.21,3.2,10.79,8.48,8.13,14.18-.9,1.96-2.86,3.38-5,3.39-4.81-.25-7.6-4.74-11.83-6.31-7.72-3.39-19.73-3.42-27.72-.64l-.17.06Z"/>
  <path class="cls-13" d="M648.87,112.68c2.29.04,4.06,1.79,4.97,3.43,1.3,2.34,1.09,3.93,1.1,6.67-.21,19.2.42,53.88-.24,65.09-1.3,5.88-8.41,7.9-11.37,1.53-.86-1.88-.81-3.85-.83-5.85,0-3.8,0-11.23,0-16.22-.07-1.57.32-5.56-.7-5.76-2.32-.49-4.65-.1-9.25-.22-11.88,0-31.63-.02-36.74.01-.69.04-1.31.03-1.66.52-.75,2.67-.16,6.38-.34,9.55-.23,5.23.55,12.78-.52,17.23-2.31,6.86-10.13,5.71-11.67-.79-.68-11.21-.05-57.07-.23-68,.09-2.66,1.86-6.48,5.52-7.11,2.27-.35,4.29,1.05,5.34,2.63,2.53,3.79,1.26,9.1,1.56,13.88,0,4.76-.01,10.6.01,15.11.11,1.13-.11,1.79.92,2.15,6.89.32,33.92.06,45.96.11,1.92.11,1.77-1.42,1.79-2.63.01-3.55,0-11.97,0-17.81.15-4.44-.65-7.62,1.66-10.93.96-1.37,2.6-2.63,4.56-2.61h.15Z"/>
  <path class="cls-9" d="M736.14,112.71c3.12.12,5.05,2.89,5.74,5.17.58,1.97.28,3.65.35,5.72.02,15.31-.01,38.42,0,53.88-.2,5.55.45,7.04-.39,10.89-.9,3.41-4.7,6.5-9.12,4.27-7.9-7.19-47.65-52.55-51.32-56.01-.17-.14-.24-.09-.28.19-.06,4.11-.01,8.42-.03,14.22-.11,11.56.22,25.54-.13,36.21-1.2,7.37-9.77,8.43-12,.84-.71-11.43-.3-49.1-.17-69.31.61-4.14,5.34-8.03,10-4.9,2.94,2.74,5.33,5.83,11.24,12.18,8.88,9.78,21.56,23.73,30.18,33.22,4.71,5.18,8.17,9.01,9.12,10.02.09.09.16.15.22.19.08.05.13.04.16-.02.23-5.63-.03-41.41.1-49.57.1-2.83,2.1-7.09,6.17-7.19h.16Z"/>
  <path class="cls-11" d="M824.09,124.53c16.98,19.4,13.39,59.25-17.39,67.47-26.65,5.59-46.93-4.67-52.38-27.46-4.42-18.62,1.76-43.84,25.93-50.41,8.25-1.97,17.99-1.96,26.27-.09,6.92,1.69,13.06,5.46,17.46,10.36l.12.13ZM796.24,178.98c23.89-.23,30.04-23.76,22.45-39.25-6.05-12.24-21.05-14.44-35.13-11.71-9.46,2.13-15.59,9.57-17.27,17.4-3.83,18.01,4.94,35.33,29.76,33.56h.19Z"/>
  <path class="cls-12" d="M908.16,181.68c4.27,4.45.71,12.73-6.78,12-12.93,0-39.01,0-47.59,0-2.25.08-4.27-.24-5.92-1.73-2.81-2.4-3.3-5.38-3.11-8.68,0-17.27,0-54,0-62.31-.44-6.99,8.12-11.92,11.92-3.56.52,1.25.62,2.36.62,3.56,0,6.12-.02,45.26.02,57.23-.1,1.15,1.32.97,2.35,1,7,0,34.45,0,41.94,0,2.62-.06,4.62.45,6.46,2.38l.1.11Z"/>
  <path class="cls-10" d="M983.19,124.53c16.98,19.4,13.39,59.25-17.39,67.47-26.65,5.59-46.93-4.67-52.38-27.46-4.42-18.62,1.76-43.84,25.93-50.41,8.25-1.97,17.99-1.96,26.27-.09,6.92,1.69,13.06,5.46,17.46,10.36l.12.13ZM955.34,178.98c23.89-.23,30.04-23.76,22.45-39.25-6.05-12.24-21.05-14.44-35.13-11.71-9.46,2.13-15.59,9.57-17.27,17.4-3.83,18.01,4.94,35.33,29.76,33.56h.19Z"/>
  <path class="cls-8" d="M1139.41,113.43c3.13,1.51,3.94,5.12,3.64,7.92-.64,3.18-2.29,6.17-3.41,9.38-7.07,17.42-17.31,45.1-21.46,54.02-4.24,8.49-15.85,12.45-24.04,4.65-2.55-2.46-4.18-5.57-5.3-8.56-1.98-5.03-8.37-21.3-12.75-32.42-2.04-5.16-3.6-9.2-4.15-10.51-.23-.58-.44-.63-.69,0-3.95,9.99-14.33,36.64-16.8,42.94-2.02,6.33-8.31,13.67-17.09,12.69-6.74-.6-11.58-6.31-13.46-11.04-2.76-6.84-14.06-36.03-20.2-51.59-1.12-3.15-2.72-6.28-3.44-9.35-.39-2.75.28-6.25,3.19-7.91,3.46-1.97,7.57.87,8.53,3.55,4.05,9.34,19.28,50.68,24,60.13,2.48,2.23,6.22.83,6.88-1.99.87-2.21,2.83-7.23,5.27-13.46,4.22-10.8,10.51-26.86,14.45-36.92,1.41-3.14,2.2-6.69,4.07-9.46,1.81-2.43,5.79-3.5,8.47-1.34,2.13,1.56,2.45,3.06,3.4,5.42.7,1.8,1.38,3.52,2.1,5.38,3.91,10.01,10.16,25.95,14.37,36.71,2.45,6.25,4.43,11.32,5.32,13.59.35.91.71,1.81,1.62,2.44,2.72,1.71,5.92.23,6.55-2.44,3.31-8.46,19.58-50.07,22.73-58.06.93-2.52,4.63-5.31,8.05-3.82l.14.06Z"/>
  <path class="cls-5" d="M1225.24,184.66c.38,2.96-.77,7.43-4.93,8.28-3.64.58-6.56-2.29-7.33-4.94-4.33-9.94-17.73-47.52-22.62-56.35-5.94-7.64-15.41-4.14-18.02,3.45-2.81,7.17-14.34,36.42-18.48,46.99-1.22,2.77-2.07,6.12-3.63,8.4-1.42,1.87-4.47,3.28-7.06,2.16-3.31-1.42-4.15-5.15-3.8-8,.57-2.89,2.09-5.72,3.12-8.66,6.2-15.15,13.67-36.08,20.2-50.76,6.54-13.13,23.69-18.06,35.11-5.78,2.57,2.8,4.51,6.31,5.78,9.54,2.55,6.41,10.47,26.6,16.19,41.09,2.74,7.42,4.29,9.95,5.45,14.49v.1ZM1182.4,154.5c9.82.66,12.81,11.36,10.14,18.07-3.79,10.17-16.67,10.17-20.46,0-2.67-6.71.33-17.42,10.14-18.07h.18Z"/>
  <path class="cls-6" d="M1293.53,113.44c2.16,1.12,3.17,3.43,3.37,5.49.29,3.31-.87,5.07-2.58,9.2-1.08,2.5-2.21,5.11-3.28,7.61-3.88,9.81-9.03,20.22-20.78,25.01-2.31.91-3.89,2.15-3.8,4.41-.06,4.14-.01,13.3-.03,17.99-.03,2.26.13,4.46-.88,6.59-1,2.38-3.98,4.62-7.08,3.5-4.14-1.59-4.74-5.89-4.62-9.3,0-4.19,0-13.31,0-18.01.03-1.38-.21-2.97-1.22-3.74-1.27-1.05-3.42-1.75-5.03-2.62-13.13-6.75-16.36-20.28-21.63-31.51-2.2-5.07-3.76-7.78-1.57-12.24,1.68-3.27,6.36-3.95,9.12-1.22,1.61,1.53,2.23,3.41,3.51,6.28,1.67,3.87,3.5,8.1,5.15,11.92,1.64,3.83,2.94,6.72,5.82,9.79,6.31,6.88,17.67,7.05,24.09.25,2.78-2.88,4.18-5.73,5.57-8.92,1.59-3.64,3.35-7.67,4.97-11.38,1.88-3.86,2.37-7.13,5.74-8.99,1.5-.79,3.46-.98,5.03-.18l.14.07Z"/>
</svg>