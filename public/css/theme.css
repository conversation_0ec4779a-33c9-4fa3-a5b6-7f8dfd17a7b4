/* Theme-specific styles */

/* Theme Toggle Button Styles */
.theme-toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
}

.theme-toggle-btn:hover {
  background-color: var(--secondary-color);
  transform: scale(1.1);
}

.theme-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Light Theme Overrides */
.theme-light {
  /* CSS variables are now defined in custom.css */
}

/* Dark Theme Overrides */
.theme-dark {
  /* CSS variables are now defined in custom.css */
}

/* Light theme specific component overrides */
.theme-light .service-item {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-light .service-item:hover {
  background-color: var(--hover-bg);
  transform: translateY(-5px);
  box-shadow: 0 5px 20px var(--shadow-color);
}

.theme-light .pricing-item {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-light .pricing-item.highlighted-box {
  border: 2px solid var(--accent-color);
  box-shadow: 0 5px 20px rgba(65, 133, 221, 0.2);
}

.theme-light .case-study-item {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-light .features-item {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

/* Specialties items with invisible background */
.theme-light .specialties-item {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.theme-dark .specialties-item {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

/* Why Choose Us items with invisible background */
.theme-light .why-choose-item {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

.theme-dark .why-choose-item {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

/* Header/Navbar light theme */
.theme-light header {
  background-color: var(--bg-color) !important;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.theme-light .navbar-nav .nav-link {
  color: var(--text-color) !important;
}

.theme-light .navbar-nav .nav-link:hover {
  color: var(--accent-color) !important;
}

/* Navbar theme styles */
header {
  background-color: var(--bg-color);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 10px var(--shadow-color);
}

header a,
header button {
  color: var(--text-color);
}

header a:hover,
header button:hover {
  color: var(--accent-color);
}

/* Mobile menu theme */
.fixed.inset-0.z-50 {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.fixed.inset-0.z-50 a,
.fixed.inset-0.z-50 button {
  color: var(--text-color);
}

.fixed.inset-0.z-50 a:hover,
.fixed.inset-0.z-50 button:hover {
  color: var(--accent-color);
}

/* Button styles for light theme */
.theme-light .btn-default {
  background: linear-gradient(0deg, var(--accent-color) 0%, var(--accent-secondary-color) 100%);
  color: #FFFFFF;
  border: none;
}

.theme-light .btn-default:hover {
  background: linear-gradient(0deg, var(--accent-secondary-color) 0%, var(--accent-color) 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(65, 133, 221, 0.3);
}

/* Default button styles - these work with the existing custom.css styles */
.btn-default {
  /* The existing styles in custom.css already use CSS variables */
  /* We just need to ensure proper color contrast */
}

.btn-default:hover {
  /* The existing styles in custom.css already use CSS variables */
  /* We just need to ensure proper color contrast */
}

/* Form elements for light theme */
.theme-light input,
.theme-light textarea,
.theme-light select {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.theme-light input:focus,
.theme-light textarea:focus,
.theme-light select:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(65, 133, 221, 0.1);
}

/* Footer light theme */
.theme-light footer {
  background-color: var(--card-bg);
  border-top: 1px solid var(--border-color);
}

/* Smooth transitions for theme switching */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure images and icons maintain proper contrast in light mode */
.theme-light img[src*="logo.svg"] {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%);
}

.theme-light .icon-box img {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%);
}

/* Dark theme specific overrides to ensure proper contrast */
.theme-dark img[src*="logo.svg"] {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

.theme-dark .icon-box img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* Additional theme-specific component styles */
.theme-light .about-counter-box h2 {
  color: var(--accent-color);
}

.theme-light .facts-counter-item h2 {
  color: var(--accent-color);
}

.theme-light .award-box h2 {
  color: var(--accent-color);
}

.theme-light .section-title h2 span {
  color: var(--accent-color);
}

.theme-light .section-footer-text span {
  color: var(--accent-color);
}

.theme-light .section-footer-text a {
  color: var(--accent-color);
}

.theme-light .section-footer-text a:hover {
  color: var(--accent-secondary-color);
}

/* Preloader theme support */
.theme-light .preloader {
  background: var(--bg-color);
}

/* Hero video visibility in light mode */
.theme-light .hero.hero-bg-image::before {
  background: var(--bg-color);
  opacity: 60%; /* 60% overlay for better video visibility */
}

.theme-light .hero.hero-bg-image.hero-slider-layout .hero-slide::before {
  background: var(--bg-color);
  opacity: 60%; /* 60% overlay for better video visibility */
}

/* Readmore button theme styles */
.theme-light .readmore-btn {
  color: var(--primary-color);
}

.theme-light .readmore-btn:hover {
  color: var(--accent-secondary-color);
}

/* Make service readmore buttons have btn-default gradient but keep original size/font - LIGHT MODE ONLY */
.theme-light .service-btn .readmore-btn {
  background: linear-gradient(to right, var(--accent-color) 0%, var(--accent-secondary-color) 50%, var(--accent-color) 100%) !important;
  background-size: 200% auto !important;
  border: none !important;
  overflow: hidden !important;
  color: #FFFFFF !important; /* White text by default */
}

.theme-light .service-btn .readmore-btn:hover {
  background-position: right center !important;
  color: var(--accent-color) !important; /* Blue text on hover */
}

.theme-dark .readmore-btn {
  color: var(--primary-color);
}

.theme-dark .readmore-btn:hover {
  color: var(--accent-secondary-color);
}

.theme-dark .service-btn .readmore-btn {
  background: var(--secondary-color);
  color: var(--primary-color);
}

.theme-dark .service-btn .readmore-btn:hover {
  color: var(--primary-color);
}

/* Scrolling ticker star icons for light mode */
.theme-light .scrolling-ticker-box img[src*="star-icon.svg"] {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%);
}

/* Icon flip effect for light mode - black instead of white */
.theme-light .icon-box img:hover {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%) !important;
  transform: rotateY(180deg) !important;
}

.theme-light .specialties-item .icon-box img:hover {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%) !important;
  transform: rotateY(180deg) !important;
}

.theme-light .why-choose-item .icon-box img:hover {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0%) contrast(100%) !important;
  transform: rotateY(180deg) !important;
}

/* Ensure proper contrast for all text elements */
.theme-light h1,
.theme-light h2,
.theme-light h3,
.theme-light h4,
.theme-light h5,
.theme-light h6 {
  color: var(--primary-color);
}

.theme-light p {
  color: var(--text-color);
}

.theme-light a {
  color: var(--text-color);
}

.theme-light a:hover {
  color: var(--accent-color);
}
