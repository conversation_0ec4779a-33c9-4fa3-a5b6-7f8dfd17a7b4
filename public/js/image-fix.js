// Image Fix Script - Ensures images are visible on page load
(function() {
    'use strict';
    
    console.log('Image fix script loaded');
    
    function fixImages() {
        console.log('Running image fix...');
        
        // Fix reveal elements
        const revealElements = document.querySelectorAll('.reveal');
        revealElements.forEach(function(element) {
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            
            const img = element.querySelector('img');
            if (img) {
                img.style.visibility = 'visible';
                img.style.opacity = '1';
            }
        });
        
        // Fix image-anime elements
        const imageAnimeElements = document.querySelectorAll('.image-anime');
        imageAnimeElements.forEach(function(element) {
            element.style.visibility = 'visible';
            element.style.opacity = '1';
            
            const img = element.querySelector('img');
            if (img) {
                img.style.visibility = 'visible';
                img.style.opacity = '1';
            }
        });
        
        // Fix all images
        const allImages = document.querySelectorAll('img');
        allImages.forEach(function(img) {
            img.style.visibility = 'visible';
            img.style.opacity = '1';
            
            // Force reload if image hasn't loaded
            if (!img.complete || img.naturalHeight === 0) {
                const src = img.src;
                img.src = '';
                img.src = src;
            }
        });
        
        console.log('Image fix completed:', {
            reveal: revealElements.length,
            imageAnime: imageAnimeElements.length,
            total: allImages.length
        });
    }
    
    // Fix images immediately if DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixImages);
    } else {
        fixImages();
    }
    
    // Fix images on window load
    window.addEventListener('load', fixImages);
    
    // Fix images periodically for the first few seconds
    setTimeout(fixImages, 100);
    setTimeout(fixImages, 500);
    setTimeout(fixImages, 1000);
    setTimeout(fixImages, 2000);
    setTimeout(fixImages, 3000);
    
    // Override the original GSAP reveal animation to ensure visibility
    function overrideGSAPReveal() {
        if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
            console.log('GSAP and ScrollTrigger available, overriding reveal animations');
            
            // Register ScrollTrigger plugin
            gsap.registerPlugin(ScrollTrigger);
            
            // Find all reveal containers and ensure they're visible
            const revealContainers = document.querySelectorAll(".reveal");
            revealContainers.forEach(function(container) {
                // Make container visible immediately
                container.style.visibility = 'visible';
                container.style.opacity = '1';
                
                const image = container.querySelector("img");
                if (image) {
                    image.style.visibility = 'visible';
                    image.style.opacity = '1';
                }
                
                // Create a simple fade-in animation instead of the complex reveal
                gsap.fromTo(container, 
                    { opacity: 0.8, y: 20 },
                    { 
                        opacity: 1, 
                        y: 0, 
                        duration: 0.8,
                        ease: "power2.out",
                        scrollTrigger: {
                            trigger: container,
                            start: "top 80%",
                            toggleActions: "play none none none"
                        }
                    }
                );
            });
        } else {
            // Retry if GSAP not ready yet
            setTimeout(overrideGSAPReveal, 100);
        }
    }
    
    // Try to override GSAP animations after scripts load
    setTimeout(overrideGSAPReveal, 1000);
    setTimeout(overrideGSAPReveal, 2000);
    setTimeout(overrideGSAPReveal, 3000);
    
})();
