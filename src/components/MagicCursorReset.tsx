'use client';

import { useEffect } from 'react';

export default function MagicCursorReset() {
  useEffect(() => {
    const resetCursorState = () => {
      if (typeof window !== 'undefined' && (window as any).cursor) {
        // Force remove all text states
        (window as any).cursor.removeState('-text');
        (window as any).cursor.removeText();
        
        // Also clear the cursor text element directly
        const cursorTextElement = document.querySelector('.cb-cursor-text') as HTMLElement;
        if (cursorTextElement) {
          cursorTextElement.textContent = '';
          cursorTextElement.style.opacity = '0';
          cursorTextElement.style.transform = 'scale(0) rotate(10deg)';
        }
        
        // Remove any lingering classes
        const cursorElement = document.querySelector('.cb-cursor');
        if (cursorElement) {
          cursorElement.classList.remove('-text');
        }
        
        console.log('Magic cursor state reset');
      }
    };
    
    // Reset immediately when component mounts
    resetCursorState();
    
    // Reset multiple times to ensure it catches all scenarios
    const timers = [50, 100, 200, 500, 1000, 2000].map(delay => 
      setTimeout(resetCursorState, delay)
    );
    
    // Also reset on window focus (when user returns to tab)
    const handleFocus = () => {
      resetCursorState();
    };
    
    // Reset on any mouse movement (in case cursor gets stuck)
    const handleMouseMove = () => {
      // Only reset if cursor has text state
      if (typeof window !== 'undefined' && (window as any).cursor) {
        const cursorElement = document.querySelector('.cb-cursor');
        if (cursorElement && cursorElement.classList.contains('-text')) {
          resetCursorState();
        }
      }
    };
    
    window.addEventListener('focus', handleFocus);
    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('mousemove', handleMouseMove);
      resetCursorState();
    };
  }, []);

  return null; // This component doesn't render anything
}
