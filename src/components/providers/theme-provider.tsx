'use client'

import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { type ThemeProviderProps } from 'next-themes/dist/types'
import { useEffect } from 'react'

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  useEffect(() => {
    console.log('ThemeProvider mounted with props:', props);
    console.log('Current HTML classes:', document.documentElement.className);
  }, [props]);

  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}
