# Reusable Modal Components

This directory contains reusable modal components extracted from the blog modal design. These components provide a consistent, professional modal experience across the application.

## Components

### Modal
The main modal wrapper component with animations and backdrop.

```tsx
import { Modal } from '@/components/ui/Modal'

<Modal
  isOpen={isOpen}
  onClose={onClose}
  title="Modal Title"
  subtitle="Optional subtitle"
  maxWidth="4xl" // sm, md, lg, xl, 2xl, 3xl, 4xl, 5xl, 6xl, 7xl
  showCloseButton={true}
  closeOnBackdrop={true}
>
  {/* Modal content */}
</Modal>
```

### ModalContent
Wrapper for modal content with scrollable area.

```tsx
import { ModalContent } from '@/components/ui/Modal'

<ModalContent>
  {/* Your content here */}
</ModalContent>
```

### ModalFooter
Sticky footer for modal actions.

```tsx
import { ModalFooter } from '@/components/ui/Modal'

<ModalFooter>
  {/* Action buttons */}
</ModalFooter>
```

### FormSection
Styled section for form groups with icons and colors.

```tsx
import { FormSection } from '@/components/ui/Modal'

<FormSection
  title="Section Title"
  icon={<YourIcon className="w-3 h-3" />}
  iconColor="blue" // blue, green, purple, orange, red
>
  {/* Form fields */}
</FormSection>
```

### FormField
Wrapper for form fields with labels and error handling.

```tsx
import { FormField } from '@/components/ui/Modal'

<FormField label="Field Label" required error="Error message">
  {/* Input component */}
</FormField>
```

### Input
Styled input component with focus states and error handling.

```tsx
import { Input } from '@/components/ui/Modal'

<Input
  type="text"
  name="fieldName"
  value={value}
  onChange={handleChange}
  placeholder="Enter value..."
  error="Error message"
/>
```

### Textarea
Styled textarea component with focus states and error handling.

```tsx
import { Textarea } from '@/components/ui/Modal'

<Textarea
  name="fieldName"
  value={value}
  onChange={handleChange}
  placeholder="Enter text..."
  rows={4}
  error="Error message"
/>
```

### Button
Styled button component with variants and loading states.

```tsx
import { Button } from '@/components/ui/Modal'

<Button
  variant="primary" // primary, secondary, outline, danger
  size="md" // sm, md, lg
  loading={false}
  disabled={false}
  onClick={handleClick}
>
  Button Text
</Button>
```

## Usage Examples

### Basic Modal
```tsx
import { Modal, ModalContent, ModalFooter, Button } from '@/components/ui/Modal'

function BasicModal({ isOpen, onClose }) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Basic Modal">
      <ModalContent>
        <div className="p-6">
          <p>This is a basic modal content.</p>
        </div>
      </ModalContent>
      <ModalFooter>
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary">
            Save
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}
```

### Form Modal
```tsx
import { 
  Modal, 
  ModalContent, 
  ModalFooter, 
  FormSection, 
  FormField, 
  Input, 
  Textarea, 
  Button 
} from '@/components/ui/Modal'

function FormModal({ isOpen, onClose, onSubmit }) {
  const [formData, setFormData] = useState({ name: '', email: '' })

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="Contact Form"
      subtitle="Send us a message"
    >
      <ModalContent>
        <form className="p-6 space-y-6">
          <FormSection title="Personal Info" iconColor="blue">
            <FormField label="Name" required>
              <Input
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Enter your name"
              />
            </FormField>
            <FormField label="Email" required>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
              />
            </FormField>
          </FormSection>
        </form>
      </ModalContent>
      <ModalFooter>
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            Submit
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  )
}
```

## Features

- **Consistent Design**: All modals follow the same design system
- **Animations**: Smooth Framer Motion animations
- **Responsive**: Works on all screen sizes
- **Accessible**: Proper ARIA labels and keyboard navigation
- **Customizable**: Flexible props for different use cases
- **TypeScript**: Fully typed components
- **Form Integration**: Built-in form components with validation support

## Styling

The components use Tailwind CSS classes and follow a consistent design pattern:
- Rounded corners (`rounded-2xl`)
- Subtle shadows (`shadow-2xl`)
- Gradient backgrounds
- Consistent spacing and typography
- Color-coded sections with icons

## Migration from Old Modals

To migrate existing modals to use these components:

1. Replace the modal wrapper with `<Modal>`
2. Wrap content with `<ModalContent>`
3. Replace form sections with `<FormSection>`
4. Use `<FormField>`, `<Input>`, `<Textarea>`, and `<Button>` components
5. Add `<ModalFooter>` for action buttons

This ensures consistency across all modals in the application.
