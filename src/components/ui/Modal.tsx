'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title: string
  subtitle?: string
  children: React.ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl'
  showCloseButton?: boolean
  closeOnBackdrop?: boolean
  className?: string
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
  '4xl': 'max-w-4xl',
  '5xl': 'max-w-5xl',
  '6xl': 'max-w-6xl',
  '7xl': 'max-w-7xl',
}

export function Modal({
  isOpen,
  onClose,
  title,
  subtitle,
  children,
  maxWidth = '6xl',
  showCloseButton = true,
  closeOnBackdrop = true,
  className = ''
}: ModalProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closeOnBackdrop && e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/60 backdrop-blur-sm"
          onClick={handleBackdropClick}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="flex min-h-screen items-center justify-center p-2"
            onClick={(e) => e.stopPropagation()}
          >
            <div className={`w-full ${maxWidthClasses[maxWidth]} max-h-[95vh] bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden ${className}`}>
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-lg font-bold text-blue-600">T</span>
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-gray-900">{title}</h2>
                    {subtitle && (
                      <p className="text-xs text-gray-500">{subtitle}</p>
                    )}
                  </div>
                </div>
                {showCloseButton && (
                  <button
                    onClick={onClose}
                    className="w-7 h-7 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
                  >
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                )}
              </div>

              {/* Content */}
              <div className="flex-1 overflow-y-auto">
                {children}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Modal Header Component
interface ModalHeaderProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  onClose?: () => void
  showCloseButton?: boolean
}

export function ModalHeader({ title, subtitle, icon, onClose, showCloseButton = true }: ModalHeaderProps) {
  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
      <div className="flex items-center space-x-2">
        {icon && (
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            {icon}
          </div>
        )}
        <div>
          <h2 className="text-lg font-bold text-gray-900">{title}</h2>
          {subtitle && (
            <p className="text-xs text-gray-500">{subtitle}</p>
          )}
        </div>
      </div>
      {showCloseButton && onClose && (
        <button
          onClick={onClose}
          className="w-7 h-7 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  )
}

// Modal Content Component
interface ModalContentProps {
  children: React.ReactNode
  className?: string
}

export function ModalContent({ children, className = '' }: ModalContentProps) {
  return (
    <div className={`flex-1 overflow-y-auto ${className}`}>
      {children}
    </div>
  )
}

// Modal Footer Component
interface ModalFooterProps {
  children: React.ReactNode
  className?: string
}

export function ModalFooter({ children, className = '' }: ModalFooterProps) {
  return (
    <div className={`border-t border-gray-100 bg-gray-50 p-3 ${className}`}>
      {children}
    </div>
  )
}

// Form Section Component
interface FormSectionProps {
  title: string
  icon?: React.ReactNode
  iconColor?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
  children: React.ReactNode
  className?: string
}

const iconColorClasses = {
  blue: 'bg-blue-100 text-blue-600',
  green: 'bg-green-100 text-green-600',
  purple: 'bg-purple-100 text-purple-600',
  orange: 'bg-orange-100 text-orange-600',
  red: 'bg-red-100 text-red-600',
}

export function FormSection({ title, icon, iconColor = 'blue', children, className = '' }: FormSectionProps) {
  return (
    <div className={`bg-white rounded-lg border border-gray-100 p-4 shadow-sm ${className}`}>
      <div className="flex items-center space-x-2 mb-3">
        {icon && (
          <div className={`w-5 h-5 ${iconColorClasses[iconColor]} rounded flex items-center justify-center`}>
            {icon}
          </div>
        )}
        <h3 className="text-base font-semibold text-gray-900">{title}</h3>
      </div>
      {children}
    </div>
  )
}

// Form Field Component
interface FormFieldProps {
  label: string
  required?: boolean
  error?: string
  children: React.ReactNode
  className?: string
}

export function FormField({ label, required = false, error, children, className = '' }: FormFieldProps) {
  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {children}
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

// Input Component
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
}

export function Input({ error, className = '', ...props }: InputProps) {
  return (
    <input
      {...props}
      className={`w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${error ? 'border-red-300' : ''} ${className}`}
    />
  )
}

// Textarea Component
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: string
}

export function Textarea({ error, className = '', ...props }: TextareaProps) {
  return (
    <textarea
      {...props}
      className={`w-full px-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${error ? 'border-red-300' : ''} ${className}`}
    />
  )
}

// Button Component
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'danger' | 'success' | 'warning' | 'info'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
}

const buttonVariants = {
  primary: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  secondary: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  outline: 'text-black bg-gray-100 border-2 border-gray-400 hover:bg-gray-200 hover:border-gray-500 shadow-md hover:shadow-lg',
  danger: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  success: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  warning: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
  info: 'bg-gray-200 text-black hover:bg-gray-300 border-gray-300 shadow-md hover:shadow-lg',
}

const buttonSizes = {
  sm: 'px-2 py-1 text-sm',
  md: 'px-3 py-2',
  lg: 'px-4 py-3 text-lg',
}

export function Button({ 
  variant = 'primary', 
  size = 'md', 
  loading = false, 
  disabled, 
  children, 
  className = '', 
  ...props 
}: ButtonProps) {
  return (
    <button
      {...props}
      disabled={disabled || loading}
      className={`rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 border ${buttonVariants[variant]} ${buttonSizes[size]} ${className}`}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// File Upload Component with Inline Layout
interface FileUploadProps {
  label: string
  accept?: string
  onFileSelect: (file: File) => void
  selectedFile?: File | null
  previewUrl?: string | null
  onClear?: () => void
  error?: string
  className?: string
  filePath?: string
  onFilePathChange?: (path: string) => void
}

export function FileUpload({ 
  label, 
  accept = "image/*", 
  onFileSelect, 
  selectedFile, 
  previewUrl, 
  onClear, 
  error, 
  className = '',
  filePath = '',
  onFilePathChange
}: FileUploadProps) {
  const fileInputRef = React.useRef<HTMLInputElement>(null)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      onFileSelect(file)
    }
  }

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      
      <div className="space-y-3">
        {/* Inline File Upload Layout */}
        <div className="flex items-center space-x-3">
          {/* File Path Input */}
          {onFilePathChange && (
            <div className="flex-1">
              <Input
                type="text"
                placeholder="Enter file path or URL..."
                value={filePath}
                onChange={(e) => onFilePathChange(e.target.value)}
              />
            </div>
          )}
          
          {/* Choose File Button */}
          <div className="flex-shrink-0">
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileSelect}
              className="hidden"
            />
            <Button
              type="button"
              variant="info"
              onClick={() => fileInputRef.current?.click()}
            >
              Choose File
            </Button>
          </div>

          {/* Image Preview */}
          {previewUrl && (
            <div className="flex-shrink-0">
              <div className="relative">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-16 h-12 object-cover border border-gray-200 rounded-md bg-gray-50"
                />
                {onClear && (
                  <button
                    type="button"
                    onClick={onClear}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                  >
                    ×
                  </button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* File Info */}
        {selectedFile && (
          <div className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
            <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span className="text-sm text-gray-700 truncate">{selectedFile.name}</span>
            <span className="text-xs text-gray-500">({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)</span>
            {onClear && (
              <Button
                type="button"
                variant="danger"
                size="sm"
                onClick={onClear}
              >
                Clear
              </Button>
            )}
          </div>
        )}

        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
      </div>
    </div>
  )
}

// Image Preview Component
interface ImagePreviewProps {
  src: string
  alt?: string
  onRemove?: () => void
  className?: string
}

export function ImagePreview({ src, alt = "Preview", onRemove, className = '' }: ImagePreviewProps) {
  return (
    <div className={`relative inline-block ${className}`}>
      <img
        src={src}
        alt={alt}
        className="w-32 h-24 object-cover border border-gray-200 rounded-md bg-gray-50"
      />
      {onRemove && (
        <button
          type="button"
          onClick={onRemove}
          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
        >
          ×
        </button>
      )}
    </div>
  )
}
