'use client'

import { usePathname } from 'next/navigation'
import Navbar from './Navbar'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export default function LayoutWrapper({ children }: LayoutWrapperProps) {
  const pathname = usePathname()
  
  // Don't show navbar on auth pages or admin pages
  const isAuthPage = pathname?.startsWith('/auth') || pathname === '/auth'
  const isAdminPage = pathname?.startsWith('/admin') || pathname === '/admin'
  
  return (
    <>
      {!isAuthPage && !isAdminPage && <Navbar />}
      {children}
    </>
  )
}
