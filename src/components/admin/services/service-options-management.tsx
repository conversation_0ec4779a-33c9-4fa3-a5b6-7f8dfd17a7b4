'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ListBulletIcon,
  MagnifyingGlassIcon,
  FunnelIcon
} from '@heroicons/react/24/outline'

interface Service {
  id: string
  categoryId: string
  name: string
  description: string
  iconClass?: string
  price: number
  discountRate?: number
  totalDiscount?: number
  manager?: string
  isActive: boolean
  displayOrder: number
  createdAt: string
  updatedAt: string
  category?: {
    id: string
    name: string
  }
  _count?: {
    serviceOptions: number
    orderDetails: number
  }
}

interface ServiceOption {
  id: string
  serviceId: string
  name: string
  description?: string
  price?: number
  discountRate?: number
  totalDiscount?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  service?: {
    id: string
    name: string
    category?: {
      id: string
      name: string
    }
  }
  _count?: {
    features: number
    orderDetails: number
  }
}

interface ServiceOptionsManagementProps {
  service: Service
  selectedOption: ServiceOption | null
  onOptionSelect: (option: ServiceOption | null) => void
}

interface ServiceOptionFormData {
  name: string
  description: string
  price: number
  discountRate: number
  isActive: boolean
}

export function ServiceOptionsManagement({ service, selectedOption, onOptionSelect }: ServiceOptionsManagementProps) {
  const [options, setOptions] = useState<ServiceOption[]>([])
  const [filteredOptions, setFilteredOptions] = useState<ServiceOption[]>([])
  const [loading, setLoading] = useState(true)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOption, setEditingOption] = useState<ServiceOption | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  const [formData, setFormData] = useState<ServiceOptionFormData>({
    name: '',
    description: '',
    price: 0,
    discountRate: 0,
    isActive: true
  })

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 300)
    return () => clearTimeout(timer)
  }, [searchTerm])

  useEffect(() => {
    fetchOptions()
  }, [service.id])

  // Mock data for demonstration
  const fetchOptions = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual API call
      const mockOptions: ServiceOption[] = [
        {
          id: '1',
          serviceId: service.id,
          name: 'Basic Package',
          description: 'Essential features for small businesses',
          price: 1000,
          discountRate: 5,
          totalDiscount: 50,
          isActive: true,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 5, orderDetails: 12 }
        },
        {
          id: '2',
          serviceId: service.id,
          name: 'Professional Package',
          description: 'Advanced features for growing businesses',
          price: 2500,
          discountRate: 10,
          totalDiscount: 250,
          isActive: true,
          createdAt: '2024-01-14T10:00:00Z',
          updatedAt: '2024-01-14T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 12, orderDetails: 8 }
        },
        {
          id: '3',
          serviceId: service.id,
          name: 'Enterprise Package',
          description: 'Complete solution for large organizations',
          price: 5000,
          discountRate: 15,
          totalDiscount: 750,
          isActive: true,
          createdAt: '2024-01-13T10:00:00Z',
          updatedAt: '2024-01-13T10:00:00Z',
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 20, orderDetails: 15 }
        }
      ]
      
      setOptions(mockOptions)
      setFilteredOptions(mockOptions)
    } catch (error) {
      console.error('Error fetching options:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleOptionSelect = (option: ServiceOption) => {
    onOptionSelect(option)
  }

  const handleCreateOption = () => {
    setIsFormOpen(true)
    setEditingOption(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      discountRate: 0,
      isActive: true
    })
  }

  const handleEditOption = (option: ServiceOption) => {
    setEditingOption(option)
    setFormData({
      name: option.name,
      description: option.description || '',
      price: option.price || 0,
      discountRate: option.discountRate || 0,
      isActive: option.isActive
    })
    setIsFormOpen(true)
  }

  const handleDeleteOption = async (optionId: string) => {
    if (confirm('Are you sure you want to delete this option?')) {
      try {
        // Mock delete - replace with actual API call
        setOptions(prev => prev.filter(option => option.id !== optionId))
        setFilteredOptions(prev => prev.filter(option => option.id !== optionId))
      } catch (error) {
        console.error('Error deleting option:', error)
      }
    }
  }

  const handleSubmitForm = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      if (editingOption) {
        // Update existing option
        const updatedOption = { ...editingOption, ...formData }
        setOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
        setFilteredOptions(prev => prev.map(option => option.id === editingOption.id ? updatedOption : option))
      } else {
        // Create new option
        const newOption: ServiceOption = {
          id: Date.now().toString(),
          serviceId: service.id,
          ...formData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          service: { id: service.id, name: service.name, category: service.category },
          _count: { features: 0, orderDetails: 0 }
        }
        setOptions(prev => [...prev, newOption])
        setFilteredOptions(prev => [...prev, newOption])
      }
      setIsFormOpen(false)
      setEditingOption(null)
    } catch (error) {
      console.error('Error saving option:', error)
    }
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Service Options</h2>
          <p className="text-sm text-gray-600">Manage options for {service.name}</p>
        </div>
        <button
          onClick={handleCreateOption}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Option
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search options..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
          />
        </div>
        <button
          onClick={() => {/* Toggle filters */}}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          Filters
        </button>
      </div>

      {/* Options List */}
      <div className="space-y-2">
        {filteredOptions.map((option) => (
          <motion.div
            key={option.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
              selectedOption?.id === option.id
                ? 'border-orange-500 bg-orange-50'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm'
            }`}
            onClick={() => handleOptionSelect(option)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <ListBulletIcon className="h-5 w-5 text-orange-500" />
                <div>
                  <h3 className="font-medium text-gray-900">{option.name}</h3>
                  {option.description && (
                    <p className="text-sm text-gray-600">{option.description}</p>
                  )}
                  <div className="flex items-center space-x-4 mt-1">
                    {option.price && option.price > 0 && (
                      <span className="text-sm font-medium text-orange-600">
                        ${option.price.toLocaleString()}
                      </span>
                    )}
                    {option.discountRate && option.discountRate > 0 && (
                      <span className="text-sm text-red-600">
                        {option.discountRate}% off
                      </span>
                    )}
                    <span className="text-xs text-gray-500">
                      {option._count?.features || 0} features
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleEditOption(option)
                    }}
                    className="p-1 text-gray-400 hover:text-orange-600"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleDeleteOption(option.id)
                    }}
                    className="p-1 text-gray-400 hover:text-red-600"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Create/Edit Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setIsFormOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg p-6 w-full max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingOption ? 'Edit Option' : 'Create Option'}
              </h3>
              <form onSubmit={handleSubmitForm} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      step="0.01"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Discount Rate (%)</label>
                    <input
                      type="number"
                      value={formData.discountRate}
                      onChange={(e) => setFormData({ ...formData, discountRate: parseFloat(e.target.value) || 0 })}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      min="0"
                      max="100"
                      step="0.1"
                    />
                  </div>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">Active</label>
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setIsFormOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700"
                  >
                    {editingOption ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
