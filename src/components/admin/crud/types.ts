import { ReactNode } from 'react'

export interface CrudColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  searchable?: boolean
  renderType?: 'text' | 'email' | 'date' | 'currency' | 'boolean' | 'status' | 'rating' | 'progress' | 'image' | 'custom' | 'company' | 'number'
  renderFunction?: (value: any, row: T) => React.ReactNode
  renderProps?: any
  width?: string
  className?: string
  hideable?: boolean // Whether this column can be hidden
  defaultVisible?: boolean // Whether this column is visible by default
}

export interface CrudField {
  key: string
  label: string
  name?: string // For form field name attribute
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'boolean' | 'radio' | 'date' | 'datetime-local' | 'url' | 'file'
  required?: boolean
  searchable?: boolean
  placeholder?: string
  defaultValue?: any
  options?: Array<{ value: string; label: string }>
  validation?: {
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: RegExp
    step?: number
  }
  rows?: number
  multiple?: boolean
  accept?: string
}

export interface CrudFilter {
  key: string
  label: string
  name?: string // For form field name attribute
  type: 'text' | 'select' | 'date' | 'daterange' | 'checkbox'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

export interface FormSection {
  title: string
  fields: string[]
}

export interface FormLayout {
  type: 'compact' | 'full'
  columns: number
  sections: FormSection[]
}

export interface CrudAction<T = any> {
  label: string
  icon: string
  action: 'edit' | 'delete' | 'view' | 'preview' | 'toggle-status' | 'toggle-published' | 'toggle-featured' | 'custom'
  customAction?: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  disabled?: boolean
  hidden?: boolean
  requiresConfirmation?: boolean
  confirmationMessage?: string
  tooltip?: string
}

export interface CrudBulkAction<T = any> {
  label: string
  icon?: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  requiresConfirmation?: boolean
  confirmationMessage?: string
}

export interface CrudPermissions {
  create: boolean
  read: boolean
  update: boolean
  delete: boolean
  export: boolean
}

export type ViewMode = 'list' | 'grid' | 'card'
export type DisplayDensity = 'compact' | 'comfortable'

export interface ViewSettings {
  mode: ViewMode
  density: DisplayDensity
  visibleColumns: string[]
}

export interface CrudConfig<T = any> {
  title: string
  description?: string
  endpoint: string
  columns: CrudColumn<T>[]
  fields: CrudField[]
  filters?: CrudFilter[]
  actions?: CrudAction<T>[]
  bulkActions?: CrudBulkAction<T>[]
  permissions: CrudPermissions
  searchPlaceholder?: string
  defaultSort?: { field: string; direction: 'asc' | 'desc' }
  pageSize?: number
  enableSearch?: boolean
  enableFilters?: boolean
  enableBulkActions?: boolean
  enableExport?: boolean
  enableViewControls?: boolean // Enable view mode controls
  enableDensityControls?: boolean // Enable display density controls
  enableColumnVisibility?: boolean // Enable column visibility controls
  defaultViewSettings?: Partial<ViewSettings>
  formLayout?: FormLayout
  customCreateButton?: ReactNode
  customHeader?: ReactNode
  customFooter?: ReactNode
  onItemClick?: boolean
}

export interface CrudState<T = any> {
  items: T[]
  loading: boolean
  error: string | null
  selectedItems: T[]
  currentPage: number
  totalPages: number
  totalItems: number
  searchQuery: string
  filters: Record<string, any>
  sortBy: string | null
  sortDirection: 'asc' | 'desc'
  viewSettings: ViewSettings
  isCreateModalOpen: boolean
  isEditModalOpen: boolean
  isDeleteModalOpen: boolean
  editingItem: T | null
  deletingItem: T | null
}

export interface CrudActions<T = any> {
  fetchData: () => Promise<void>
  setPage: (page: number) => void
  setSearch: (query: string) => void
  setFilters: (filters: Record<string, any>) => void
  setSort: (field: string, direction: 'asc' | 'desc') => void
  setViewMode: (mode: ViewMode) => void
  setDisplayDensity: (density: DisplayDensity) => void
  setColumnVisibility: (columnKey: string, visible: boolean) => void
  resetViewSettings: () => void
  selectItem: (item: T) => void
  selectAll: () => void
  clearSelection: () => void
  openCreateModal: () => void
  closeCreateModal: () => void
  openEditModal: (item: T) => void
  closeEditModal: () => void
  openDeleteModal: (item: T) => void
  closeDeleteModal: () => void
  createItem: (data: any) => Promise<void>
  updateItem: (id: string, data: any) => Promise<void>
  deleteItem: (id: string) => Promise<void>
  bulkDelete: (ids: string[]) => Promise<void>
  bulkAction: (action: string, items: T[]) => Promise<void>
  refresh: () => void
  exportData: () => Promise<void>
}

export interface CrudContextValue<T = any> {
  state: CrudState<T>
  actions: CrudActions<T>
}

export type UserRole = 'ADMIN' | 'USER' | 'CLIENT'

export const DEFAULT_ROLE_PERMISSIONS: Record<UserRole, CrudPermissions> = {
  ADMIN: { create: true, read: true, update: true, delete: true, export: true },
  USER: { create: false, read: true, update: false, delete: false, export: false },
  CLIENT: { create: false, read: false, update: false, delete: false, export: false },
}
