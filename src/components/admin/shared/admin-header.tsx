'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  ChevronDownIcon,
  PlusIcon,
  ViewColumnsIcon,
  TableCellsIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';

interface AdminHeaderProps {
  title: string;
  description?: string;
  searchPlaceholder?: string;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  enableSearch?: boolean;
  enableFilters?: boolean;
  enableViewControls?: boolean;
  enableDensityControls?: boolean;
  enableColumnVisibility?: boolean;
  enableCreate?: boolean;
  onCreateClick?: () => void;
  createButtonText?: string;
  viewMode?: 'list' | 'grid' | 'card';
  onViewModeChange?: (mode: 'list' | 'grid' | 'card') => void;
  density?: 'compact' | 'comfortable' | 'spacious';
  onDensityChange?: (density: 'compact' | 'comfortable' | 'spacious') => void;
  visibleColumns?: string[];
  onVisibleColumnsChange?: (columns: string[]) => void;
  availableColumns?: Array<{ key: string; label: string }>;
  filters?: Array<{
    key: string;
    label: string;
    type: 'select' | 'text' | 'checkbox' | 'date' | 'daterange';
    options?: Array<{ value: string; label: string }>;
  }>;
  onFiltersChange?: (filters: Record<string, string>) => void;
  currentFilters?: Record<string, string>;
  itemCount?: number;
  totalItems?: number;
}

export function AdminHeader({
  title,
  description,
  searchPlaceholder = 'Search...',
  searchQuery,
  onSearchChange,
  enableSearch = true,
  enableFilters = true,
  enableViewControls = true,
  enableDensityControls = true,
  enableColumnVisibility = true,
  enableCreate = true,
  onCreateClick,
  createButtonText = 'Add New',
  viewMode = 'list',
  onViewModeChange,
  density = 'comfortable',
  onDensityChange,
  visibleColumns = [],
  onVisibleColumnsChange,
  availableColumns = [],
  filters = [],
  onFiltersChange,
  currentFilters = {},
  itemCount = 0,
  totalItems = 0,
}: AdminHeaderProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [showDensitySelector, setShowDensitySelector] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const densityDropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (densityDropdownRef.current && !densityDropdownRef.current.contains(event.target as Node)) {
        setShowDensitySelector(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...currentFilters };
    if (value) {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }
    onFiltersChange?.(newFilters);
  };

  const clearAllFilters = () => {
    onFiltersChange?.({});
    onSearchChange('');
  };

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {description && <p className="text-gray-600">{description}</p>}
        </div>

        {enableCreate && onCreateClick && (
          <button
            onClick={onCreateClick}
            className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <PlusIcon className="w-5 h-5 mr-2" />
            {createButtonText}
          </button>
        )}
      </div>

      {/* Search and Controls */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search Bar */}
            {enableSearch && (
              <div className="flex-1">
                <div className="relative group">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder={searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => onSearchChange(e.target.value)}
                    className="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50 focus:bg-white"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => {
                        onSearchChange('');
                        searchInputRef.current?.focus();
                      }}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <XMarkIcon className="w-5 h-5" />
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Controls */}
            <div className="flex items-center gap-3">
              {/* Filters */}
              {enableFilters && filters.length > 0 && (
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`inline-flex items-center px-4 py-2.5 border rounded-lg text-sm font-medium transition-all duration-200 ${
                    showFilters
                      ? 'border-blue-500 text-blue-700 bg-blue-50 shadow-sm'
                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400'
                  }`}
                >
                  <FunnelIcon className="w-4 h-4 mr-2" />
                  Filters
                  {Object.keys(currentFilters).length > 0 && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                      {Object.keys(currentFilters).length}
                    </span>
                  )}
                </button>
              )}

              {/* View Controls */}
              {enableViewControls && onViewModeChange && (
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => onViewModeChange('list')}
                    className={`p-2 rounded-md transition-all duration-200 ${
                      viewMode === 'list' 
                        ? 'bg-white text-blue-600 shadow-sm' 
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                    }`}
                    title="List View"
                  >
                    <ListBulletIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onViewModeChange('grid')}
                    className={`p-2 rounded-md transition-all duration-200 ${
                      viewMode === 'grid' 
                        ? 'bg-white text-blue-600 shadow-sm' 
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                    }`}
                    title="Grid View"
                  >
                    <Squares2X2Icon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => onViewModeChange('card')}
                    className={`p-2 rounded-md transition-all duration-200 ${
                      viewMode === 'card' 
                        ? 'bg-white text-blue-600 shadow-sm' 
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'
                    }`}
                    title="Card View"
                  >
                    <RectangleStackIcon className="w-4 h-4" />
                  </button>
                </div>
              )}

              {/* Density Controls */}
              {enableDensityControls && onDensityChange && (
                <div className="relative" ref={densityDropdownRef}>
                  <button
                    onClick={() => setShowDensitySelector(!showDensitySelector)}
                    className={`inline-flex items-center px-4 py-2.5 border rounded-lg text-sm font-medium transition-all duration-200 ${
                      showDensitySelector 
                        ? 'border-blue-500 text-blue-700 bg-blue-50' 
                        : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400'
                    }`}
                  >
                    <AdjustmentsHorizontalIcon className="w-4 h-4 mr-2" />
                    {density.charAt(0).toUpperCase() + density.slice(1)}
                    <ChevronDownIcon className="w-4 h-4 ml-2" />
                  </button>

                  <AnimatePresence>
                    {showDensitySelector && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50"
                      >
                        <div className="py-1">
                          {['compact', 'comfortable', 'spacious'].map((option) => (
                            <button
                              key={option}
                              onClick={() => {
                                onDensityChange(option as 'compact' | 'comfortable' | 'spacious');
                                setShowDensitySelector(false);
                              }}
                              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 transition-colors ${
                                density === option ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                              }`}
                            >
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </button>
                          ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}

              {/* Column Visibility */}
              {enableColumnVisibility && onVisibleColumnsChange && availableColumns.length > 0 && viewMode === 'list' && (
                <div className="relative">
                  <button
                    onClick={() => setShowColumnSelector(!showColumnSelector)}
                    className="inline-flex items-center px-4 py-2.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
                  >
                    <ViewColumnsIcon className="w-4 h-4 mr-2" />
                    Columns
                    <ChevronDownIcon className="w-4 h-4 ml-2" />
                  </button>

                  <AnimatePresence>
                    {showColumnSelector && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-10"
                      >
                        <div className="p-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-3">Show Columns</h4>
                          <div className="space-y-2">
                            {availableColumns.map((column) => (
                              <label key={column.key} className="flex items-center hover:bg-gray-50 p-2 rounded-md transition-colors">
                                <input
                                  type="checkbox"
                                  checked={visibleColumns.includes(column.key)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      onVisibleColumnsChange([...visibleColumns, column.key]);
                                    } else {
                                      onVisibleColumnsChange(visibleColumns.filter(col => col !== column.key));
                                    }
                                  }}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <span className="ml-2 text-sm text-gray-700">{column.label}</span>
                              </label>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Filters Panel */}
        <AnimatePresence>
          {showFilters && filters.length > 0 && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border-t border-gray-200 bg-gray-50 rounded-b-xl"
            >
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {filters.map((filter) => (
                    <div key={filter.key} className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        {filter.label}
                      </label>
                      <select
                        value={currentFilters[filter.key] || ''}
                        onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                        className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                      >
                        {filter.options?.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        )) || null}
                      </select>
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    Showing {itemCount} of {totalItems} items
                  </div>
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
                  >
                    Clear all filters
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
