'use client'

import React, { useState, useRef, useEffect } from 'react'
import { 
  DocumentTextIcon, 
  CalendarIcon, 
  TagIcon,
  PhotoIcon,
  EyeIcon,
  EyeSlashIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { 
  Modal, 
  ModalContent, 
  ModalFooter, 
  FormSection, 
  FormField, 
  Input, 
  Textarea, 
  Button,
  FileUpload
} from '@/components/ui/Modal'

interface BlogPost {
  id?: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  authorId?: string
  isPublished: boolean
  publishedAt?: string
  categories?: string
  tags?: string
  createdAt?: string
  updatedAt?: string
}

interface BlogModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: BlogPost) => Promise<void>
  title: string
  initialData?: BlogPost
}

export function BlogModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData
}: BlogModalProps) {
  const [formData, setFormData] = useState<BlogPost>({
    title: initialData?.title || '',
    slug: initialData?.slug || '',
    content: initialData?.content || '',
    excerpt: initialData?.excerpt || '',
    featuredImageUrl: initialData?.featuredImageUrl || '',
    authorId: initialData?.authorId || '',
    isPublished: initialData?.isPublished ?? false,
    publishedAt: initialData?.publishedAt || '',
    categories: initialData?.categories || '',
    tags: initialData?.tags || '',
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialData?.featuredImageUrl || null)
  const [activeTab, setActiveTab] = useState<'content' | 'settings' | 'preview'>('content')
  const [showPreview, setShowPreview] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Reset form when modal opens/closes or initialData changes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        title: initialData?.title || '',
        slug: initialData?.slug || '',
        content: initialData?.content || '',
        excerpt: initialData?.excerpt || '',
        featuredImageUrl: initialData?.featuredImageUrl || '',
        authorId: initialData?.authorId || '',
        isPublished: initialData?.isPublished ?? false,
        publishedAt: initialData?.publishedAt || '',
        categories: initialData?.categories || '',
        tags: initialData?.tags || '',
      })
      setPreviewUrl(initialData?.featuredImageUrl || null)
      setSelectedFile(null)
      setActiveTab('content')
    }
  }, [isOpen, initialData])

  // Auto-generate slug from title
  useEffect(() => {
    if (formData.title && !initialData) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setFormData(prev => ({ ...prev, slug }))
    }
  }, [formData.title, initialData])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleFileSelect = (file: File) => {
    setSelectedFile(file)
    
    // Create preview URL
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleFileUpload = async (): Promise<string | null> => {
    if (!selectedFile) return null

    const formData = new FormData()
    formData.append('file', selectedFile)
    formData.append('type', 'blog-featured-image')

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Upload failed with status: ${response.status}`)
      }

      const data = await response.json()
      if (!data.url) {
        throw new Error('No URL returned from upload')
      }
      
      return data.url
    } catch (error) {
      console.error('File upload error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload featured image'
      throw new Error(errorMessage)
    }
  }

  const clearFile = () => {
    setSelectedFile(null)
    setPreviewUrl(null)
    setFormData(prev => ({ ...prev, featuredImageUrl: '' }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let featuredImageUrl = formData.featuredImageUrl

      // Upload file if selected
      if (selectedFile) {
        featuredImageUrl = await handleFileUpload() || ''
      }

      const submitData: BlogPost = {
        title: formData.title,
        slug: formData.slug,
        content: formData.content,
        excerpt: formData.excerpt,
        featuredImageUrl: featuredImageUrl,
        authorId: formData.authorId || undefined,
        isPublished: formData.isPublished,
        publishedAt: formData.publishedAt || undefined,
        categories: formData.categories,
        tags: formData.tags,
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to save blog post'
      alert(`Error: ${errorMessage}. Please try again.`)
    } finally {
      setIsSubmitting(false)
    }
  }

  const getWordCount = (text: string) => {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  const getReadingTime = (text: string) => {
    const wordsPerMinute = 200
    const wordCount = getWordCount(text)
    return Math.ceil(wordCount / wordsPerMinute)
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      subtitle="Create and manage your blog content with our advanced editor"
      maxWidth="7xl"
    >
      <ModalContent>
        <div className="flex h-[80vh]">
          {/* Left Sidebar - Navigation */}
          <div className="w-64 bg-gradient-to-b from-slate-50 to-slate-100 border-r border-slate-200 p-4">
            <div className="space-y-4">
              {/* Tab Navigation */}
              <div className="space-y-2">
                <button
                  onClick={() => setActiveTab('content')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'content'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <DocumentTextIcon className="w-5 h-5" />
                  <span className="font-medium">Content</span>
                </button>
                
                <button
                  onClick={() => setActiveTab('settings')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'settings'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <TagIcon className="w-5 h-5" />
                  <span className="font-medium">Settings</span>
                </button>
                
                <button
                  onClick={() => setActiveTab('preview')}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === 'preview'
                      ? 'bg-gray-200 text-black border border-gray-300 shadow-sm'
                      : 'text-slate-600 hover:bg-gray-200 hover:text-black'
                  }`}
                >
                  <EyeIcon className="w-5 h-5" />
                  <span className="font-medium">Preview</span>
                </button>
              </div>

              {/* Content Stats */}
              <div className="bg-white rounded-lg p-4 border border-slate-200">
                <h3 className="text-sm font-semibold text-slate-700 mb-3">Content Stats</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Words:</span>
                    <span className="font-medium">{getWordCount(formData.content)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Reading time:</span>
                    <span className="font-medium">{getReadingTime(formData.content)} min</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Characters:</span>
                    <span className="font-medium">{formData.content.length}</span>
                  </div>
                </div>
              </div>

              {/* Publishing Status */}
              <div className="bg-white rounded-lg p-4 border border-slate-200">
                <h3 className="text-sm font-semibold text-slate-700 mb-3">Publishing Status</h3>
                <div className="flex items-center space-x-2">
                  {formData.isPublished ? (
                    <>
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                      <span className="text-sm text-green-700 font-medium">Published</span>
                    </>
                  ) : (
                    <>
                      <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
                      <span className="text-sm text-yellow-700 font-medium">Draft</span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 overflow-y-auto">
            {activeTab === 'content' && (
              <div className="p-6 space-y-6">
                {/* Featured Image Section */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <PhotoIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Featured Image</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <FileUpload
                        label="Upload Featured Image"
                        accept="image/*"
                        onFileSelect={handleFileSelect}
                        selectedFile={selectedFile}
                        previewUrl={previewUrl}
                        onClear={clearFile}
                        filePath={formData.featuredImageUrl}
                        onFilePathChange={(path) => setFormData(prev => ({ ...prev, featuredImageUrl: path }))}
                      />
                    </div>
                    
                    {previewUrl && (
                      <div className="relative">
                        <img
                          src={previewUrl}
                          alt="Featured image preview"
                          className="w-full h-48 object-cover rounded-lg border border-slate-200"
                        />
                        <button
                          onClick={clearFile}
                          className="absolute top-2 right-2 w-8 h-8 bg-gray-200 text-black rounded-full flex items-center justify-center hover:bg-gray-300 transition-colors"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Title and Slug */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Blog Title" required>
                      <Input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter your blog post title..."
                        className="text-lg font-medium"
                      />
                    </FormField>

                    <FormField label="URL Slug" required>
                      <Input
                        type="text"
                        name="slug"
                        value={formData.slug}
                        onChange={handleInputChange}
                        required
                        placeholder="blog-post-slug"
                        className="font-mono"
                      />
                    </FormField>
                  </div>
                </div>

                {/* Excerpt */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <FormField label="Excerpt">
                    <Textarea
                      name="excerpt"
                      value={formData.excerpt}
                      onChange={handleInputChange}
                      placeholder="Write a brief description of your blog post..."
                      rows={3}
                      className="resize-none"
                    />
                    <p className="text-xs text-slate-500 mt-2">
                      This will appear in blog previews and search results
                    </p>
                  </FormField>
                </div>

                {/* Main Content */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <FormField label="Blog Content" required>
                    <Textarea
                      name="content"
                      value={formData.content}
                      onChange={handleInputChange}
                      required
                      placeholder="Write your blog post content here..."
                      rows={12}
                      className="resize-none text-base leading-relaxed"
                    />
                  </FormField>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="p-6 space-y-6">
                {/* Author and Publishing */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <CalendarIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Publishing Settings</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Author ID">
                      <Input
                        type="text"
                        name="authorId"
                        value={formData.authorId}
                        onChange={handleInputChange}
                        placeholder="author-123"
                      />
                    </FormField>

                    <FormField label="Publish Date">
                      <Input
                        type="datetime-local"
                        name="publishedAt"
                        value={formData.publishedAt}
                        onChange={handleInputChange}
                      />
                    </FormField>
                  </div>

                  <div className="mt-6">
                    <label className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        name="isPublished"
                        checked={formData.isPublished}
                        onChange={handleInputChange}
                        className="w-4 h-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded"
                      />
                      <span className="text-sm font-medium text-slate-700">
                        Publish this blog post immediately
                      </span>
                    </label>
                    <p className="text-xs text-slate-500 mt-2">
                      Unpublished posts will be saved as drafts
                    </p>
                  </div>
                </div>

                {/* Categories and Tags */}
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <TagIcon className="w-6 h-6 text-blue-600" />
                    <h3 className="text-lg font-semibold text-slate-900">Categories & Tags</h3>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField label="Categories">
                      <Input
                        type="text"
                        name="categories"
                        value={formData.categories}
                        onChange={handleInputChange}
                        placeholder="Web Development, Technology"
                      />
                      <p className="text-xs text-slate-500 mt-2">Separate with commas</p>
                    </FormField>

                    <FormField label="Tags">
                      <Input
                        type="text"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="react, nextjs, javascript"
                      />
                      <p className="text-xs text-slate-500 mt-2">Separate with commas</p>
                    </FormField>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="p-6">
                <div className="bg-white rounded-xl border border-slate-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-slate-900">Blog Post Preview</h3>
                    <Button
                      onClick={() => setShowPreview(!showPreview)}
                      variant="info"
                      className="flex items-center space-x-2"
                    >
                      {showPreview ? (
                        <>
                          <EyeSlashIcon className="w-4 h-4" />
                          <span>Hide Preview</span>
                        </>
                      ) : (
                        <>
                          <EyeIcon className="w-4 h-4" />
                          <span>Show Preview</span>
                        </>
                      )}
                    </Button>
                  </div>

                  {showPreview && (
                    <div className="prose max-w-none">
                      {previewUrl && (
                        <img
                          src={previewUrl}
                          alt="Featured image"
                          className="w-full h-64 object-cover rounded-lg mb-6"
                        />
                      )}
                      
                      <h1 className="text-3xl font-bold text-slate-900 mb-4">
                        {formData.title || 'Blog Post Title'}
                      </h1>
                      
                      {formData.excerpt && (
                        <p className="text-lg text-slate-600 mb-6 italic">
                          {formData.excerpt}
                        </p>
                      )}
                      
                      <div className="text-sm text-slate-500 mb-6">
                        <span>By {formData.authorId || 'Author'}</span>
                        {formData.publishedAt && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{new Date(formData.publishedAt).toLocaleDateString()}</span>
                          </>
                        )}
                      </div>
                      
                      <div className="whitespace-pre-wrap text-slate-700 leading-relaxed">
                        {formData.content || 'Your blog content will appear here...'}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </ModalContent>

      <ModalFooter>
        <div className="flex items-center justify-between w-full">
          <div className="text-sm text-slate-600">
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                <span>Saving your blog post...</span>
              </div>
            ) : (
              <span>Ready to save your blog post</span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              onClick={handleSubmit}
              loading={isSubmitting}
              disabled={isSubmitting}
              variant="success"
            >
              {formData.isPublished ? 'Publish Post' : 'Save as Draft'}
            </Button>
          </div>
        </div>
      </ModalFooter>
    </Modal>
  )
}
