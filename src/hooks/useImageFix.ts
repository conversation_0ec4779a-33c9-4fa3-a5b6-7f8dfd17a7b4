'use client';

import { useEffect } from 'react';

export const useImageFix = () => {
  useEffect(() => {
    const fixImages = () => {
      // Fix all images with reveal class
      const revealElements = document.querySelectorAll('.reveal');
      revealElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.visibility = 'visible';
        htmlElement.style.opacity = '1';
        
        const img = element.querySelector('img');
        if (img) {
          img.style.visibility = 'visible';
          img.style.opacity = '1';
        }
      });

      // Fix all images with image-anime class
      const imageAnimeElements = document.querySelectorAll('.image-anime');
      imageAnimeElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        htmlElement.style.visibility = 'visible';
        htmlElement.style.opacity = '1';
        
        const img = element.querySelector('img');
        if (img) {
          img.style.visibility = 'visible';
          img.style.opacity = '1';
        }
      });

      // Fix all images in general
      const allImages = document.querySelectorAll('img');
      allImages.forEach((img) => {
        img.style.visibility = 'visible';
        img.style.opacity = '1';
        
        // Force reload if image hasn't loaded
        if (!img.complete || img.naturalHeight === 0) {
          const src = img.src;
          img.src = '';
          img.src = src;
        }
      });

      console.log('Images fixed:', {
        reveal: revealElements.length,
        imageAnime: imageAnimeElements.length,
        total: allImages.length
      });
    };

    // Fix images immediately
    fixImages();

    // Fix images after DOM is fully loaded
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', fixImages);
    }

    // Fix images after window load
    window.addEventListener('load', fixImages);

    // Fix images periodically for the first few seconds
    const intervals = [100, 500, 1000, 2000, 3000].map(delay =>
      setTimeout(fixImages, delay)
    );

    // Observer for dynamically added images
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Check if the added element is an image or contains images
              if (element.tagName === 'IMG') {
                const img = element as HTMLImageElement;
                img.style.visibility = 'visible';
                img.style.opacity = '1';
              } else {
                const images = element.querySelectorAll('img');
                images.forEach((img) => {
                  img.style.visibility = 'visible';
                  img.style.opacity = '1';
                });
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return () => {
      document.removeEventListener('DOMContentLoaded', fixImages);
      window.removeEventListener('load', fixImages);
      intervals.forEach(clearTimeout);
      observer.disconnect();
    };
  }, []);
};
