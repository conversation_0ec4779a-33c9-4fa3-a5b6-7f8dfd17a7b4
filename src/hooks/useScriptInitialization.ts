import { useEffect, useState } from 'react';

export const useScriptInitialization = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializeScripts = () => {
      if (!mounted) return;

      // Set scripts loaded flag
      if (typeof window !== 'undefined') {
        window.scriptsLoaded = true;
      }

      // Comprehensive preloader hiding strategy
      const hidePreloader = () => {
        const preloader = document.querySelector('.preloader') as HTMLElement;
        if (preloader && preloader.style.display !== 'none') {
          // Use fadeOut effect similar to jQuery
          preloader.style.transition = 'opacity 0.6s ease-in-out';
          preloader.style.opacity = '0';
          preloader.style.pointerEvents = 'none';
          
          setTimeout(() => {
            if (mounted) {
              preloader.style.display = 'none';
            }
          }, 600);
        }
      };

      // Multiple strategies to hide preloader
      // 1. Try immediately
      hidePreloader();
      
      // 2. Try after DOM is ready
      setTimeout(hidePreloader, 100);
      
      // 3. Try after a short delay
      setTimeout(hidePreloader, 500);
      
      // 4. Try after jQuery should be loaded
      setTimeout(hidePreloader, 1000);
      
      // 5. Try after images should be loaded
      setTimeout(hidePreloader, 2000);
      
      // 6. Final fallback
      setTimeout(hidePreloader, 3000);

      // Also listen for window load event as backup
      if (typeof window !== 'undefined') {
        const handleLoad = () => {
          if (mounted) {
            hidePreloader();
          }
        };
        
        if (document.readyState === 'complete') {
          handleLoad();
        } else {
          window.addEventListener('load', handleLoad);
        }
      }

      // Initialize scripts when they're available
      const initScripts = () => {
        if (typeof window !== 'undefined' &&
            window.jQuery &&
            window.jQuery.fn) {

          // Remove any existing SlickNav elements
          const existingSlicknav = document.querySelector('.slicknav_menu');
          if (existingSlicknav) {
            existingSlicknav.remove();
          }

          // Initialize SlickNav only if it's available
          if (window.jQuery.fn.slicknav) {
            window.jQuery('#menu').slicknav({
              prependTo: '.responsive-menu',
              label: '',
              allowParentLinks: true,
              closedSymbol: '<i class="fa-solid fa-plus"></i>',
              openedSymbol: '<i class="fa-solid fa-minus"></i>'
            });
          }

          // Magic cursor is handled automatically by the script
          if ((window as any).gsap && window.jQuery) {
            console.log('Magic cursor should be working automatically');
          }

          // Fix image reveal animations - ensure images are visible
          const fixImageRevealAnimations = () => {
            // Wait for GSAP and ScrollTrigger to be available
            if (typeof window !== 'undefined' && (window as any).gsap && (window as any).ScrollTrigger) {
              const revealContainers = document.querySelectorAll('.reveal');
              revealContainers.forEach((container) => {
                // Make sure the container is visible
                (container as HTMLElement).style.visibility = 'visible';
                (container as HTMLElement).style.opacity = '1';
              });

              // Also handle image-anime elements
              const imageAnimeElements = document.querySelectorAll('.image-anime');
              imageAnimeElements.forEach((element) => {
                const img = element.querySelector('img');
                if (img) {
                  // Ensure image is loaded and visible
                  if (img.complete) {
                    (element as HTMLElement).style.visibility = 'visible';
                    (element as HTMLElement).style.opacity = '1';
                  } else {
                    img.onload = () => {
                      (element as HTMLElement).style.visibility = 'visible';
                      (element as HTMLElement).style.opacity = '1';
                    };
                  }
                }
              });

              console.log('Image reveal animations fixed');
            } else {
              // Retry if GSAP/ScrollTrigger not ready yet
              setTimeout(fixImageRevealAnimations, 100);
            }
          };

          // Fix images after a short delay to ensure all scripts are loaded
          setTimeout(fixImageRevealAnimations, 500);
          setTimeout(fixImageRevealAnimations, 1000);
          setTimeout(fixImageRevealAnimations, 2000);

          setIsInitialized(true);
        }
      };

      // Try to initialize immediately
      initScripts();

      // If not ready, wait for scripts to load
      if (typeof window !== 'undefined' && !window.jQuery) {
        const checkScripts = setInterval(() => {
          if (typeof window !== 'undefined' && window.jQuery) {
            clearInterval(checkScripts);
            initScripts();
          }
        }, 100);

        // Timeout after 10 seconds to prevent infinite waiting
        setTimeout(() => {
          clearInterval(checkScripts);
          setIsInitialized(true); // Set to true anyway to not block rendering
        }, 10000);
      } else {
        setIsInitialized(true);
      }
    };

    // Initialize immediately
    initializeScripts();

    return () => {
      mounted = false;
    };
  }, []);

  return isInitialized;
};
