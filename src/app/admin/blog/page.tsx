'use client';

import { BlogManagerNew } from '@/components/admin/blog/blog-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface BlogPost {
  id: string
  title: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  authorId?: string
  isPublished: boolean
  publishedAt?: string
  categories?: string
  tags?: string
  createdAt: string
  updatedAt: string
  _count?: {
    comments?: number;
    views?: number;
    [key: string]: number | undefined;
  }
}

const blogConfig: CrudConfig<BlogPost> = {
  title: 'Blog Posts',
  description: 'Create, edit, and manage your blog content',
  endpoint: 'blog', // API endpoint

  // Table columns configuration
  columns: [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      searchable: true,
      width: '30%'
    },
    {
      key: 'excerpt',
      label: 'Excerpt',
      sortable: false,
      searchable: true,
      width: '25%'
    },
    {
      key: 'isPublished',
      label: 'Status',
      sortable: true,
      searchable: false,
      width: '10%'
    },
    {
      key: 'categories',
      label: 'Category',
      sortable: true,
      searchable: true,
      width: '15%'
    },
    {
      key: 'tags',
      label: 'Tags',
      sortable: false,
      searchable: true,
      width: '15%'
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      searchable: false,
      width: '15%'
    }
  ],

  // Filters configuration
  filters: [
    {
      key: 'isPublished',
      label: 'Publication Status',
      type: 'select',
      options: [
        { value: '', label: 'All Posts' },
        { value: 'true', label: 'Published' },
        { value: 'false', label: 'Draft' }
      ]
    },
    {
      key: 'categories',
      label: 'Category',
      type: 'select',
      options: [
        { value: '', label: 'All Categories' },
        { value: 'Web Development', label: 'Web Development' },
        { value: 'Mobile Development', label: 'Mobile Development' },
        { value: 'Cloud Computing', label: 'Cloud Computing' },
        { value: 'Programming', label: 'Programming' },
        { value: 'Security', label: 'Security' },
        { value: 'AI/ML', label: 'AI/ML' }
      ]
    }
  ],

  // Bulk actions configuration
  bulkActions: [
    {
      action: 'publish',
      label: 'Publish',
      icon: 'PowerIcon',
      variant: 'primary',
      confirmationMessage: 'Are you sure you want to publish the selected blog posts?'
    },
    {
      action: 'unpublish',
      label: 'Unpublish',
      icon: 'PowerIcon',
      variant: 'secondary',
      confirmationMessage: 'Are you sure you want to unpublish the selected blog posts?'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      confirmationMessage: 'Are you sure you want to delete the selected blog posts? This action cannot be undone.'
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View blog post details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit blog post'
    },
    {
      action: 'toggle-published',
      label: 'Toggle Published',
      icon: 'PowerIcon',
      variant: 'warning',
      tooltip: 'Publish/Unpublish blog post'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete blog post'
    }
  ],

  fields: [
    {
      key: 'title',
      label: 'Title',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., How to Build Amazing Web Applications'
    },
    {
      key: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'e.g., how-to-build-amazing-web-applications'
    },
    {
      key: 'excerpt',
      label: 'Excerpt',
      type: 'textarea',
      searchable: true,
      placeholder: 'Brief description of the blog post...',
      rows: 3
    },
    {
      key: 'content',
      label: 'Content',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Write your blog post content here...',
      rows: 8
    },
    {
      key: 'featuredImageUrl',
      label: 'Featured Image',
      type: 'url',
      searchable: false,
      placeholder: 'Enter image URL or click Upload to select file'
    },
    {
      key: 'authorId',
      label: 'Author ID',
      type: 'text',
      searchable: false,
      placeholder: 'e.g., author-123'
    },
    {
      key: 'categories',
      label: 'Categories',
      type: 'text',
      searchable: true,
      placeholder: 'e.g., Web Development, Technology'
    },
    {
      key: 'tags',
      label: 'Tags',
      type: 'text',
      searchable: true,
      placeholder: 'e.g., react, nextjs, javascript, tutorial'
    },
    {
      key: 'isPublished',
      label: 'Published',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
    {
      key: 'publishedAt',
      label: 'Published Date',
      type: 'datetime-local',
      searchable: false,
    },
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search blog posts by title, content, excerpt, categories, tags...',
  defaultSort: { field: 'updatedAt', direction: 'desc' }, // Sort by Last Active (most recent)
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['title', 'excerpt', 'isPublished', 'categories', 'tags', 'updatedAt']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Basic Information',
        fields: ['title', 'slug', 'authorId', 'publishedAt']
      },
      {
        title: 'Featured Image',
        fields: ['featuredImageUrl']
      },
      {
        title: 'Content',
        fields: ['excerpt', 'content']
      },
      {
        title: 'Categories & Tags',
        fields: ['categories', 'tags']
      },
      {
        title: 'Publishing Settings',
        fields: ['isPublished']
      }
    ]
  }
};

export default function BlogPage() {
  return (
    <div className="admin-page">
      <BlogManagerNew config={blogConfig} />
    </div>
  );
}
