import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin } from '@/lib/auth'
import { mockCategories } from '@/lib/mock-data'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const id = parseInt(params.id)
    const category = mockCategories.find(c => c.id === id)

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      data: category
    })
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const id = parseInt(params.id)
    const body = await request.json()
    const { categname, categdesc, parentid, isactive, displayorder } = body

    if (!categname) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    const categoryIndex = mockCategories.findIndex(c => c.id === id)
    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Update category
    mockCategories[categoryIndex] = {
      ...mockCategories[categoryIndex],
      categname,
      categdesc: categdesc || '',
      parentid: parentid || null,
      isactive: isactive !== undefined ? isactive : mockCategories[categoryIndex].isactive,
      displayorder: displayorder !== undefined ? displayorder : mockCategories[categoryIndex].displayorder
    }

    return NextResponse.json({
      data: mockCategories[categoryIndex],
      message: 'Category updated successfully'
    })
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const id = parseInt(params.id)
    const categoryIndex = mockCategories.findIndex(c => c.id === id)

    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has children
    const hasChildren = mockCategories.some(c => c.parentid === id)
    if (hasChildren) {
      return NextResponse.json(
        { error: 'Cannot delete category with subcategories' },
        { status: 400 }
      )
    }

    // Check if category has services
    if (mockCategories[categoryIndex]._count.services > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with services' },
        { status: 400 }
      )
    }

    // Delete category
    const deletedCategory = mockCategories.splice(categoryIndex, 1)[0]

    return NextResponse.json({
      message: 'Category deleted successfully',
      data: deletedCategory
    })
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}
