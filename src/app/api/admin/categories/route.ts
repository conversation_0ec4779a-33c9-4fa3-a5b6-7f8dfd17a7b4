import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin } from '@/lib/auth'
import { mockCategories } from '@/lib/mock-data'

export async function GET(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const page = parseInt(searchParams.get('page') || '1')
    const search = searchParams.get('search') || ''
    const parentId = searchParams.get('parentId')
    const isActive = searchParams.get('isActive')

    let filteredCategories = [...mockCategories]

    // Apply search filter
    if (search) {
      filteredCategories = filteredCategories.filter(category =>
        category.categname.toLowerCase().includes(search.toLowerCase()) ||
        (category.categdesc && category.categdesc.toLowerCase().includes(search.toLowerCase()))
      )
    }

    // Apply parent filter
    if (parentId) {
      if (parentId === 'null' || parentId === '0') {
        filteredCategories = filteredCategories.filter(category => !category.parentid)
      } else {
        filteredCategories = filteredCategories.filter(category => category.parentid === parseInt(parentId))
      }
    }

    // Apply active filter
    if (isActive !== null && isActive !== undefined) {
      const active = isActive === 'true'
      filteredCategories = filteredCategories.filter(category => category.isactive === active)
    }

    // Sort by display order
    filteredCategories.sort((a, b) => a.displayorder - b.displayorder)

    // Apply pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedCategories = filteredCategories.slice(startIndex, endIndex)

    return NextResponse.json({
      data: paginatedCategories,
      pagination: {
        page,
        limit,
        total: filteredCategories.length,
        totalPages: Math.ceil(filteredCategories.length / limit)
      }
    })
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const body = await request.json()
    const { categname, categdesc, parentid, isactive, displayorder } = body

    if (!categname) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Create new category
    const newCategory = {
      id: Math.max(...mockCategories.map(c => c.id)) + 1,
      categname,
      categdesc: categdesc || '',
      parentid: parentid || null,
      isactive: isactive !== undefined ? isactive : true,
      displayorder: displayorder || 0,
      _count: { services: 0, children: 0 }
    }

    mockCategories.push(newCategory)

    return NextResponse.json({
      data: newCategory,
      message: 'Category created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const body = await request.json()
    const { id, categname, categdesc, parentid, isactive, displayorder } = body

    if (!id || !categname) {
      return NextResponse.json(
        { error: 'Category ID and name are required' },
        { status: 400 }
      )
    }

    const categoryIndex = mockCategories.findIndex(c => c.id === parseInt(id))
    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Update category
    mockCategories[categoryIndex] = {
      ...mockCategories[categoryIndex],
      categname,
      categdesc: categdesc || '',
      parentid: parentid || null,
      isactive: isactive !== undefined ? isactive : mockCategories[categoryIndex].isactive,
      displayorder: displayorder !== undefined ? displayorder : mockCategories[categoryIndex].displayorder
    }

    return NextResponse.json({
      data: mockCategories[categoryIndex],
      message: 'Category updated successfully'
    })
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Category ID is required' },
        { status: 400 }
      )
    }

    const categoryIndex = mockCategories.findIndex(c => c.id === parseInt(id))
    if (categoryIndex === -1) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has children
    const hasChildren = mockCategories.some(c => c.parentid === parseInt(id))
    if (hasChildren) {
      return NextResponse.json(
        { error: 'Cannot delete category with subcategories' },
        { status: 400 }
      )
    }

    // Check if category has services
    if (mockCategories[categoryIndex]._count.services > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with services' },
        { status: 400 }
      )
    }

    // Delete category
    const deletedCategory = mockCategories.splice(categoryIndex, 1)[0]

    return NextResponse.json({
      message: 'Category deleted successfully',
      data: deletedCategory
    })
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}
