import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin, hasPermission } from '@/lib/auth'
import { mockBlogPosts, addBlogPost, updateBlogPost, deleteBlogPost } from '@/lib/mock-data'



export async function GET(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const sortBy = searchParams.get('sortBy') || 'updatedAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const isPublished = searchParams.get('isPublished')
    const categories = searchParams.get('categories')

    // Filter posts based on search query
    let filteredPosts = mockBlogPosts.filter(post => {
      const matchesSearch = !search || 
        post.title.toLowerCase().includes(search.toLowerCase()) ||
        post.content.toLowerCase().includes(search.toLowerCase()) ||
        post.excerpt?.toLowerCase().includes(search.toLowerCase()) ||
        post.categories?.toLowerCase().includes(search.toLowerCase()) ||
        post.tags?.toLowerCase().includes(search.toLowerCase())

      const matchesPublished = isPublished === null || 
        (isPublished === 'true' && post.isPublished) ||
        (isPublished === 'false' && !post.isPublished)

      const matchesCategories = !categories || 
        post.categories?.toLowerCase().includes(categories.toLowerCase())

      return matchesSearch && matchesPublished && matchesCategories
    })

    // Sort posts
    filteredPosts.sort((a, b) => {
      const aValue = a[sortBy as keyof typeof a] || ''
      const bValue = b[sortBy as keyof typeof b] || ''
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      }
    })

    // Paginate results
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedPosts = filteredPosts.slice(startIndex, endIndex)

    const totalPosts = filteredPosts.length
    const totalPages = Math.ceil(totalPosts / limit)

    return NextResponse.json({
      data: paginatedPosts,
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    })
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const body = await request.json()
    
    // Validate required fields
    if (!body.title || !body.content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      )
    }

    // Create new blog post
    const newPost = {
      id: Date.now().toString(),
      title: body.title,
      slug: body.slug || body.title.toLowerCase().replace(/\s+/g, '-'),
      content: body.content,
      excerpt: body.excerpt || '',
      featuredImageUrl: body.featuredImageUrl || '',
      authorId: body.authorId || 'admin-1',
      isPublished: body.isPublished || false,
      publishedAt: body.isPublished ? (body.publishedAt || new Date().toISOString()) : null,
      categories: body.categories || '',
      tags: body.tags || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    // Add the new post to the mock data array
    addBlogPost(newPost)

    return NextResponse.json({
      data: newPost,
      message: 'Blog post created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Error creating blog post:', error)
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const body = await request.json()
    const { ids, data } = body

    if (!ids || !Array.isArray(ids)) {
      return NextResponse.json(
        { error: 'IDs array is required' },
        { status: 400 }
      )
    }

    // Update multiple posts (for bulk actions)
    ids.forEach(id => {
      updateBlogPost(id, data)
    })

    const updatedPosts = mockBlogPosts.filter(post => ids.includes(post.id))

    return NextResponse.json({
      data: updatedPosts.filter(post => ids.includes(post.id)),
      message: 'Blog posts updated successfully'
    })
  } catch (error) {
    console.error('Error updating blog posts:', error)
    return NextResponse.json(
      { error: 'Failed to update blog posts' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const body = await request.json()
    const { ids } = body

    if (!ids || !Array.isArray(ids)) {
      return NextResponse.json(
        { error: 'IDs array is required' },
        { status: 400 }
      )
    }

    // Delete multiple posts (for bulk actions)
    const deletedPosts = mockBlogPosts.filter(post => ids.includes(post.id))
    
    ids.forEach(id => {
      deleteBlogPost(id)
    })

    return NextResponse.json({
      data: deletedPosts,
      message: 'Blog posts deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting blog posts:', error)
    return NextResponse.json(
      { error: 'Failed to delete blog posts' },
      { status: 500 }
    )
  }
}
