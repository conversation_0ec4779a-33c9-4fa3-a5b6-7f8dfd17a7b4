import { NextRequest, NextResponse } from 'next/server'
import { authenticateAdmin, hasPermission } from '@/lib/auth'
import { mockBlogPosts, getBlogPost, updateBlogPost, deleteBlogPost } from '@/lib/mock-data'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { id } = params
    const post = getBlogPost(id)

    if (!post) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({ data: post })
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { id } = params
    const body = await request.json()

    // Check if post exists
    const existingPost = getBlogPost(id)
    if (!existingPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      )
    }

    // Update the post
    updateBlogPost(id, body)
    const updatedPost = getBlogPost(id)

    return NextResponse.json({
      data: updatedPost,
      message: 'Blog post updated successfully'
    })
  } catch (error) {
    console.error('Error updating blog post:', error)
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Temporarily disable authentication for testing
    // const auth = await authenticateAdmin(request)
    // if (!auth.isAdmin) {
    //   return NextResponse.json(
    //     { error: auth.error || 'Admin access required' },
    //     { status: 401 }
    //   )
    // }

    const { id } = params

    // Check if post exists
    const existingPost = getBlogPost(id)
    if (!existingPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      )
    }

    const deletedPost = existingPost

    // Delete the post
    deleteBlogPost(id)

    return NextResponse.json({
      data: deletedPost,
      message: 'Blog post deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting blog post:', error)
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    )
  }
}
