import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Get basic counts from the database
    const [
      totalClients,
      totalProjects,
      totalServices,
      totalTeamMembers,
      blogPosts,
      categories
    ] = await Promise.all([
      prisma.clients.count(),
      prisma.projects.count(),
      prisma.services.count(),
      prisma.teammembers.count(),
      prisma.blogposts.count(),
      prisma.categories.count()
    ]);

    // Mock data for demonstration (replace with real data from your database)
    const dashboardStats = {
      overview: {
        totalClients: totalClients || 0,
        totalProjects: totalProjects || 0,
        totalServices: totalServices || 0,
        totalTeamMembers: totalTeamMembers || 0,
        activeProjects: Math.floor((totalProjects || 0) * 0.7),
        completedProjects: Math.floor((totalProjects || 0) * 0.3),
        pendingInvoices: 5,
        totalRevenue: 125000,
        monthlyRevenue: 25000,
      },
      growth: {
        projects: 12.5,
        clients: 8.3,
        revenue: 15.7,
      },
      charts: {
        projectsByStatus: [
          { status: 'Active', count: Math.floor((totalProjects || 0) * 0.7) },
          { status: 'Completed', count: Math.floor((totalProjects || 0) * 0.3) },
          { status: 'Pending', count: 2 },
        ],
        invoicesByStatus: [
          { status: 'Paid', count: 15, amount: 75000 },
          { status: 'Pending', count: 5, amount: 25000 },
          { status: 'Overdue', count: 2, amount: 10000 },
        ],
      },
      recent: {
        projects: [],
        clients: [],
      },
      insights: {
        topServices: [],
        upcomingDeadlines: [],
      },
    };

    return NextResponse.json({
      success: true,
      data: dashboardStats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard statistics',
      },
      { status: 500 }
    );
  }
}
