'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Footer from '@/components/layout/Footer';
import PageHeader from '@/components/layout/PageHeader';
import ScrollingTicker from '@/components/layout/ScrollingTicker';
import { useScriptInitialization } from '@/hooks/useScriptInitialization';
import { useImageFix } from '@/hooks/useImageFix';
import { getAllCaseStudies } from '@/data/caseStudies';

export default function CaseStudy() {
  const isScriptsInitialized = useScriptInitialization();
  const caseStudies = getAllCaseStudies();

  // Fix image loading issues
  useImageFix();

  return (
    <>
      {/* Preloader Start */}
      <div className="preloader">
        <div className="loading-container">
          <div className="loading" />
          <div id="loading-icon">
            <img src="/images/loader.svg" alt="Loading" />
          </div>
        </div>
      </div>
      {/* Preloader End */}



      <PageHeader 
        title="Case"
        titleHighlight="Study"
        breadcrumbs={[
          { label: 'home', href: '/' },
          { label: 'case study', isActive: true }
        ]}
      />

      <ScrollingTicker />

      <>
  {/* Page Case Study Start */}
  <div className="page-case-study">
    <div className="container">
      <div className="row">
        {caseStudies.map((caseStudy, index) => (
          <div key={caseStudy.id} className="col-lg-4 col-md-6">
            {/* Case Study Item Start */}
            <div className="case-study-item wow fadeInUp" data-wow-delay={`${index * 0.2}s`}>
              {/* Case Study Image Start */}
              <div className="case-study-image">
                <Link href={`/case-study/${caseStudy.slug}`} data-cursor-text="View">
                  <figure className="image-anime">
                    <img src={caseStudy.image} alt={caseStudy.title} />
                  </figure>
                </Link>
              </div>
              {/* Case Study Image End */}
              {/* Case Study Content Start */}
              <div className="case-study-content">
                <h3>
                  <Link href={`/case-study/${caseStudy.slug}`}>
                    {caseStudy.title}
                  </Link>
                </h3>
              </div>
              {/* Case Study Content End */}
              {/* Case Study Button Start */}
              <div className="case-study-btn">
                <Link href={`/case-study/${caseStudy.slug}`}>
                  <img src="/images/arrow-white.svg" alt="" />
                </Link>
              </div>
              {/* Case Study Button End */}
            </div>
            {/* Case Study Item End */}
          </div>
        ))}
      </div>
    </div>
  </div>
  {/* Page Case Study End */}
</>


      <Footer />
    </>
  );
}
