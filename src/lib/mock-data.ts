// Mock data for categories - replace with database calls
export let mockCategories = [
  {
    id: 1,
    categname: 'Web Development',
    categdesc: 'Custom web applications and websites',
    parentid: null,
    isactive: true,
    displayorder: 1,
    _count: { services: 5, children: 2 }
  },
  {
    id: 2,
    categname: 'Mobile Development',
    categdesc: 'iOS and Android applications',
    parentid: null,
    isactive: true,
    displayorder: 2,
    _count: { services: 3, children: 1 }
  },
  {
    id: 3,
    categname: 'Cloud Services',
    categdesc: 'Cloud infrastructure and deployment',
    parentid: null,
    isactive: true,
    displayorder: 3,
    _count: { services: 4, children: 0 }
  },
  {
    id: 4,
    categname: 'Frontend Development',
    categdesc: 'React, Vue, Angular development',
    parentid: 1,
    isactive: true,
    displayorder: 1,
    _count: { services: 2, children: 0 }
  },
  {
    id: 5,
    categname: 'Backend Development',
    categdesc: 'Node.js, Python, Java development',
    parentid: 1,
    isactive: true,
    displayorder: 2,
    _count: { services: 3, children: 0 }
  }
]

// Shared mock data for blog posts
export let mockBlogPosts = [
  {
    id: '1',
    title: 'Getting Started with Next.js',
    slug: 'getting-started-with-nextjs',
    content: 'Next.js is a powerful React framework that makes building full-stack web applications simple and efficient...',
    excerpt: 'Learn how to get started with Next.js and build your first application.',
    featuredImageUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
    authorId: 'admin-1',
    isPublished: true,
    publishedAt: '2024-01-15T10:00:00Z',
    categories: 'Web Development, React',
    tags: 'nextjs, react, javascript, tutorial',
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'Building Modern UIs with Tailwind CSS',
    slug: 'building-modern-uis-with-tailwind-css',
    content: 'Tailwind CSS is a utility-first CSS framework that allows you to build custom designs without leaving your HTML...',
    excerpt: 'Discover how to create beautiful, responsive user interfaces using Tailwind CSS.',
    featuredImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',
    authorId: 'admin-1',
    isPublished: true,
    publishedAt: '2024-01-14T14:30:00Z',
    categories: 'CSS, Design',
    tags: 'tailwind, css, design, ui',
    createdAt: '2024-01-14T13:00:00Z',
    updatedAt: '2024-01-14T14:30:00Z',
  },
  {
    id: '3',
    title: 'TypeScript Best Practices',
    slug: 'typescript-best-practices',
    content: 'TypeScript adds static typing to JavaScript, making your code more reliable and maintainable...',
    excerpt: 'Explore the best practices for writing clean and maintainable TypeScript code.',
    featuredImageUrl: 'https://images.unsplash.com/photo-1516116216624-53e697fedbea?w=800&h=400&fit=crop',
    authorId: 'admin-1',
    isPublished: false,
    publishedAt: null,
    categories: 'Programming, TypeScript',
    tags: 'typescript, javascript, programming, best-practices',
    createdAt: '2024-01-13T16:00:00Z',
    updatedAt: '2024-01-13T16:00:00Z',
  },
  {
    id: '4',
    title: 'Deploying Next.js Applications',
    slug: 'deploying-nextjs-applications',
    content: 'Learn how to deploy your Next.js applications to various platforms including Vercel, Netlify, and AWS...',
    excerpt: 'A comprehensive guide to deploying Next.js applications to production.',
    featuredImageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop',
    authorId: 'admin-1',
    isPublished: true,
    publishedAt: '2024-01-12T11:00:00Z',
    categories: 'Deployment, DevOps',
    tags: 'nextjs, deployment, vercel, aws, devops',
    createdAt: '2024-01-12T10:00:00Z',
    updatedAt: '2024-01-12T11:00:00Z',
  },
  {
    id: '5',
    title: 'State Management in React',
    slug: 'state-management-in-react',
    content: 'Understanding state management is crucial for building complex React applications...',
    excerpt: 'Explore different state management solutions for React applications.',
    featuredImageUrl: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=800&h=400&fit=crop',
    authorId: 'admin-1',
    isPublished: true,
    publishedAt: '2024-01-11T15:00:00Z',
    categories: 'React, State Management',
    tags: 'react, state, redux, context, hooks',
    createdAt: '2024-01-11T14:00:00Z',
    updatedAt: '2024-01-11T15:00:00Z',
  }
]

// Helper functions for blog post data manipulation
export const addBlogPost = (post: any) => {
  mockBlogPosts.push(post)
}

export const updateBlogPost = (id: string, updates: any) => {
  const index = mockBlogPosts.findIndex(p => p.id === id)
  if (index !== -1) {
    mockBlogPosts[index] = { ...mockBlogPosts[index], ...updates, updatedAt: new Date().toISOString() }
  }
}

export const deleteBlogPost = (id: string) => {
  const index = mockBlogPosts.findIndex(p => p.id === id)
  if (index !== -1) {
    mockBlogPosts.splice(index, 1)
  }
}

export const getBlogPost = (id: string) => {
  return mockBlogPosts.find(p => p.id === id)
}
