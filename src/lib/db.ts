import { prisma } from './prisma'

// Example function to get all blog posts
export async function getAllBlogPosts() {
  try {
    const posts = await prisma.blogposts.findMany({
      where: {
        ispublished: true
      },
      orderBy: {
        publishedat: 'desc'
      }
    })
    return posts
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    throw error
  }
}

// Example function to get a single blog post by slug
export async function getBlogPostBySlug(slug: string) {
  try {
    const post = await prisma.blogposts.findUnique({
      where: {
        slug: slug
      }
    })
    return post
  } catch (error) {
    console.error('Error fetching blog post:', error)
    throw error
  }
}

// Example function to get all services
export async function getAllServices() {
  try {
    const services = await prisma.services.findMany({
      include: {
        categories: true
      }
    })
    return services
  } catch (error) {
    console.error('Error fetching services:', error)
    throw error
  }
}

// Example function to get all categories
export async function getAllCategories() {
  try {
    const categories = await prisma.categories.findMany({
      where: {
        isactive: true
      },
      orderBy: {
        displayorder: 'asc'
      }
    })
    return categories
  } catch (error) {
    console.error('Error fetching categories:', error)
    throw error
  }
}
