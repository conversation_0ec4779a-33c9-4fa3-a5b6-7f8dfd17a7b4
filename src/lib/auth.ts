import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/[...nextauth]/route'

// Admin authentication utility that works with NextAuth
export interface AdminUser {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
}

export async function authenticateAdmin(request: NextRequest): Promise<{
  isAdmin: boolean
  user?: AdminUser
  error?: string
}> {
  try {
    // Get the session using NextAuth
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user) {
      return { isAdmin: false, error: 'No session found' }
    }

    // Check if user has admin role
    const userRole = session.user.role || 'USER'
    const isAdmin = userRole === 'ADMIN' || userRole === 'MODERATOR'

    if (!isAdmin) {
      return { isAdmin: false, error: 'Admin access required' }
    }

    // Map permissions based on role
    const permissions = userRole === 'ADMIN' 
      ? ['read', 'write', 'delete', 'publish', 'manage_users']
      : ['read', 'write', 'publish']

    const adminUser: AdminUser = {
      id: session.user.id || '',
      email: session.user.email || '',
      name: session.user.name || '',
      role: userRole,
      permissions
    }

    return { isAdmin: true, user: adminUser }
  } catch (error) {
    console.error('Authentication error:', error)
    return { isAdmin: false, error: 'Authentication failed' }
  }
}

export function hasPermission(user: AdminUser, permission: string): boolean {
  return user.permissions.includes(permission) || user.role === 'ADMIN'
}

// Helper function to check if user is admin (for client-side)
export function isAdminUser(role?: string): boolean {
  return role === 'ADMIN' || role === 'MODERATOR'
}
