export type Theme = 'dark' | 'light';

export interface ThemeColors {
  primary: string;
  secondary: string;
  text: string;
  bg: string;
  accent: string;
  accentSecondary: string;
  divider: string;
  error: string;
}

export const themes: Record<Theme, ThemeColors> = {
  dark: {
    primary: '#FFFFFF',
    secondary: '#FFFFFF0A',
    text: '#D1D1D1',
    bg: '#1C1B2B',
    accent: '#4185DD',
    accentSecondary: '#B42FDA',
    divider: '#FFFFFF0A',
    error: 'rgb(230, 87, 87)',
  },
  light: {
    primary: '#1C1B2B',
    secondary: '#F8F9FA',
    text: '#333333',
    bg: '#FFFFFF',
    accent: '#4185DD',
    accentSecondary: '#B42FDA',
    divider: '#E9ECEF',
    error: 'rgb(230, 87, 87)',
  },
};

export const getThemeColors = (theme: Theme): ThemeColors => {
  return themes[theme];
};
