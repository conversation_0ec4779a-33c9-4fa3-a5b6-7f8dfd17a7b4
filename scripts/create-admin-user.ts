import { PrismaClient } from '../generated/prisma'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.users.findUnique({
      where: {
        email: '<EMAIL>'
      }
    })

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email)
      return
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 12)

    // Create admin user
    const adminUser = await prisma.users.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstname: 'Admin',
        lastname: 'User',
        role: 'ADMIN',
        isactive: true
      }
    })

    console.log('Admin user created successfully:', adminUser.email)
  } catch (error) {
    console.error('Error creating admin user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createAdminUser()
